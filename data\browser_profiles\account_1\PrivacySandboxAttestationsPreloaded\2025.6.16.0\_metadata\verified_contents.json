[{"description": "treehash per file", "signed_content": {"payload": "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "signatures": [{"header": {"kid": "publisher"}, "protected": "eyJhbGciOiJSUzI1NiJ9", "signature": "a_85g7yJteM96RGQaoIwb2eoYRHPnjfJA6pBwPJtLNVQtskfoHHbNaTGPM7axTjQ5bcWL2tv2_RY80-C7OQio5z7sWyJjEsAQF26VaiiHShBGeRjv0M0pyxde_IZrblA0qwkSiw-osjryK7LnFhYa0wiGKOiN-DMdmtlBJVPm2-jM1yKtnOiyFoKG42cSwyduAPtY0cshpiiWyI2hda4ZgE-b9yqUKFtt_9xubmPvz12MYlsn5Gdj6XjwKjrLyHEgA4Vh31M8iXfuIuTrRe-HO5wKNz3xGQkX3f6NkSIGw6zpzBEFh5F2EX_2We07GhoEXmfvhRaM04bJmmUN4L9GVhLA5-8BV9GPEhRJlGe2TqOZseS1MEFp5-uIoqfiqhEravMwVVKApEczyENH1IAbiRGmHSKgvbMMm3mxlDG-0RDUAqfPjCP2aNdDO_2bj-HLz3XMna1q2YxAWcQMeydJLQNZvyu7AQWo9pdQHbYtYa4EV5bM6Q6PXR2N1lkwIo2MmX5E0VRlOsZn-sgHVP3diFmtrbejvANlnXS0Yq3c87qp-3yUzXuo3cYPsVFCbAAD9FxTeoOJszOigyXQssy-ykfJMK-1jP82gmR60IGPvjdS9Nf_CS9xXPhO4U2SDer3zGtsAHJ5a12umE1d_MJyuNIKI4Mz17LaV1Pr2VWFYg"}, {"header": {"kid": "webstore"}, "protected": "eyJhbGciOiJSUzI1NiJ9", "signature": "W4EKQua7Ma6wEnkvqBm7w1YDhL9IOwne12dAN9B8cEpSvfcARCm3rksXx2w1x6dGHaPNJ6qd2qIFrLejagF1mDkzbOCgqnmLGLPpMLslHUa4foAKdiuIDR5ZHoi9TE7LSZKyvHiooyexkogjmwBJnkwbfYGJzNb4JgDc8F3q03NumyyesPHZPEG5IEwQGybk_dM9nQYA19usYjajRFNHZE3-K0NUfax6tBsqmvHg28x17C37JWTUn1_TjsRf_h8TdwacvOOZg1xqgWumYdzKGIBVrm_3__iQnjT_R_UEeMtM8Z2Isj-G3_1zSm28ZvxAvcVqz5z1Fs97Mv8e1JVpKw"}]}}]
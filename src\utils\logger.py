#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志工具模块
"""

import sys
from pathlib import Path
from loguru import logger
from typing import Optional

from PyQt6.QtCore import QObject, pyqtSignal

from src.config.settings import Settings


class TaskLogSignals(QObject):
    """全局任务日志信号系统"""

    # 任务日志信号
    task_started = pyqtSignal(str, dict)  # task_type, task_config
    task_ended = pyqtSignal(str, dict)    # task_type, result
    account_started = pyqtSignal(str, str)  # account_name, action
    step_success = pyqtSignal(str, str, str)  # account_name, step, details
    step_failure = pyqtSignal(str, str, str)  # account_name, step, error
    interaction_action = pyqtSignal(str, str, str, bool, str)  # actor, target, action, success, details
    custom_log = pyqtSignal(str, str, str)  # message, log_type, color


# 全局任务日志信号实例
_task_log_signals = None


def get_task_log_signals() -> TaskLogSignals:
    """获取全局任务日志信号实例"""
    global _task_log_signals
    if _task_log_signals is None:
        _task_log_signals = TaskLogSignals()
    return _task_log_signals


def setup_logger(settings: Optional[Settings] = None) -> logger:
    """
    设置日志系统
    
    Args:
        settings: 应用设置对象
        
    Returns:
        配置好的logger实例
    """
    if settings is None:
        from src.config.settings import get_settings
        settings = get_settings()
    
    # 移除默认处理器
    logger.remove()
    
    # 控制台输出格式
    console_format = (
        "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level>"
    )
    
    # 文件输出格式
    file_format = (
        "{time:YYYY-MM-DD HH:mm:ss} | "
        "{level: <8} | "
        "{name}:{function}:{line} | "
        "{message}"
    )
    
    # 添加控制台处理器
    logger.add(
        sys.stdout,
        format=console_format,
        level=settings.log_level,
        colorize=True,
        backtrace=True,
        diagnose=True
    )
    
    # 确保日志目录存在
    log_file_path = Path(settings.log_file)
    log_file_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 添加文件处理器
    logger.add(
        settings.log_file,
        format=file_format,
        level=settings.log_level,
        rotation=settings.log_max_size,
        retention=settings.log_backup_count,
        compression="zip",
        encoding="utf-8",
        backtrace=True,
        diagnose=True
    )
    
    # 添加错误日志文件处理器
    error_log_file = log_file_path.parent / "error.log"
    logger.add(
        str(error_log_file),
        format=file_format,
        level="ERROR",
        rotation=settings.log_max_size,
        retention=settings.log_backup_count,
        compression="zip",
        encoding="utf-8",
        backtrace=True,
        diagnose=True
    )
    
    return logger


def get_logger(name: str = None) -> logger:
    """
    获取logger实例
    
    Args:
        name: logger名称
        
    Returns:
        logger实例
    """
    if name:
        return logger.bind(name=name)
    return logger


class LoggerMixin:
    """日志混入类"""

    @property
    def logger(self):
        """获取当前类的logger"""
        return get_logger(self.__class__.__name__)

    def emit_task_log(self, signal_name: str, *args):
        """发送任务日志信号"""
        try:
            signals = get_task_log_signals()
            if hasattr(signals, signal_name):
                signal = getattr(signals, signal_name)
                signal.emit(*args)
        except Exception as e:
            # 如果信号发送失败，至少记录到普通日志
            self.logger.debug(f"任务日志信号发送失败: {e}")

    def log_task_start(self, task_type: str, task_config: dict):
        """记录任务开始"""
        self.logger.info(f"开始{task_type}任务")
        self.emit_task_log('task_started', task_type, task_config)

    def log_task_end(self, task_type: str, result: dict):
        """记录任务结束"""
        self.logger.info(f"{task_type}任务完成")
        self.emit_task_log('task_ended', task_type, result)

    def log_account_start(self, account_name: str, action: str):
        """记录账号开始操作"""
        self.logger.info(f"账号 {account_name} 开始{action}")
        self.emit_task_log('account_started', account_name, action)

    def log_step_success(self, account_name: str, step: str, details: str = ""):
        """记录步骤成功"""
        self.logger.info(f"账号 {account_name} {step}成功: {details}")
        self.emit_task_log('step_success', account_name, step, details)

    def log_step_failure(self, account_name: str, step: str, error: str):
        """记录步骤失败"""
        self.logger.error(f"账号 {account_name} {step}失败: {error}")
        self.emit_task_log('step_failure', account_name, step, error)

    def log_interaction_action(self, actor_account: str, target_account: str, action: str, success: bool, details: str = ""):
        """记录互动操作"""
        status = "成功" if success else "失败"
        self.logger.info(f"账号 {actor_account} 对 {target_account} 进行{action} - {status}: {details}")
        self.emit_task_log('interaction_action', actor_account, target_account, action, success, details)

    def log_custom(self, message: str, log_type: str = "info", color: str = "#ffffff"):
        """记录自定义日志"""
        self.logger.info(message)
        self.emit_task_log('custom_log', message, log_type, color)


def log_function_call(func):
    """
    装饰器：记录函数调用
    
    Args:
        func: 被装饰的函数
        
    Returns:
        装饰后的函数
    """
    def wrapper(*args, **kwargs):
        func_logger = get_logger(func.__module__)
        func_logger.debug(f"调用函数 {func.__name__}, args={args}, kwargs={kwargs}")
        
        try:
            result = func(*args, **kwargs)
            func_logger.debug(f"函数 {func.__name__} 执行成功")
            return result
        except Exception as e:
            func_logger.error(f"函数 {func.__name__} 执行失败: {e}")
            raise
    
    return wrapper


def log_async_function_call(func):
    """
    装饰器：记录异步函数调用
    
    Args:
        func: 被装饰的异步函数
        
    Returns:
        装饰后的异步函数
    """
    async def wrapper(*args, **kwargs):
        func_logger = get_logger(func.__module__)
        func_logger.debug(f"调用异步函数 {func.__name__}, args={args}, kwargs={kwargs}")
        
        try:
            result = await func(*args, **kwargs)
            func_logger.debug(f"异步函数 {func.__name__} 执行成功")
            return result
        except Exception as e:
            func_logger.error(f"异步函数 {func.__name__} 执行失败: {e}")
            raise
    
    return wrapper

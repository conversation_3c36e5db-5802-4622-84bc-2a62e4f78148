2025-07-24 23:41:55 | ERROR    | __main__:initialize:76 | 应用程序初始化失败: 核心组件初始化失败: 数据库管理器未初始化
2025-07-24 23:46:45 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x24300d42940> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:47:03 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x24308fa1c70> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:47:33 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x24308fa1070> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:48:03 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x24308fa1c70> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:48:33 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x24308fa1520> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:49:03 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x24308fa1d90> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:49:33 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x24308fa1040> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:50:03 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x24308fa1d90> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:50:33 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x24308fa1640> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:51:03 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x24308fa1910> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:51:33 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x24308fa17f0> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:52:03 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x24308fa1430> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:52:22 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x24308fd6370> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:52:33 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x24308fcdf70> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:52:36 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x24308fd6820> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:52:51 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x243245c4a30> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:53:03 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x243245c48b0> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:53:14 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x243245c4cd0> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:53:17 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x243245c4970> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:53:22 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x243245c44c0> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:53:25 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x243245c4bb0> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:53:28 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x243245dc1c0> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:53:30 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x243245dc2e0> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:53:32 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x243245dc5b0> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:53:33 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x243245dc130> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-25 00:18:18 | ERROR    | src.ui.widgets.account_widget:import_accounts:752 | 打开导入对话框失败: name 'QDialog' is not defined
2025-07-25 00:21:42 | ERROR    | src.ui.dialogs.group_management_dialog:update_group_table:212 | 更新分组表格失败: Parent instance <AccountGroup at 0x26fb8fb78e0> is not bound to a Session; lazy load operation of attribute 'accounts' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-25 00:24:31 | ERROR    | src.ui.widgets.account_widget:import_accounts:752 | 打开导入对话框失败: name 'QDialog' is not defined
2025-07-25 00:47:22 | ERROR    | src.ui.widgets.account_widget:import_accounts:830 | 打开导入对话框失败: name 'QDialog' is not defined
2025-07-25 01:13:08 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:13:08 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:13:08 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:13:09 | ERROR    | src.ui.widgets.account_widget:import_accounts:830 | 打开导入对话框失败: name 'QDialog' is not defined
2025-07-25 01:13:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:13:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:13:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:13:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:13:26 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:13:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:13:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:13:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:14:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:14:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:14:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:14:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:14:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:14:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:15:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:15:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:15:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:15:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:15:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:15:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:16:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:16:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:16:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:16:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:16:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:16:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:17:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:17:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:17:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:17:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:17:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:17:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:18:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:18:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:18:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:18:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:18:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:18:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:19:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:19:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:19:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:19:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:19:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:19:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:20:02 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:20:02 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:20:02 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:20:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:20:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:20:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:33:08 | ERROR    | src.core.proxy_manager:test_proxies:377 | 测试代理失败: 代理状态必须是: active, inactive, testing
2025-07-25 01:33:14 | ERROR    | src.core.proxy_manager:test_proxies:377 | 测试代理失败: 代理状态必须是: active, inactive, testing
2025-07-25 01:33:39 | ERROR    | src.core.proxy_manager:test_proxies:377 | 测试代理失败: 代理状态必须是: active, inactive, testing
2025-07-25 01:33:47 | ERROR    | src.core.proxy_manager:test_proxies:377 | 测试代理失败: 代理状态必须是: active, inactive, testing
2025-07-25 01:47:27 | ERROR    | src.ui.widgets.proxy_widget:assign_proxies:451 | 分配代理失败: 'AccountGroupManager' object has no attribute 'get_groups'
2025-07-25 01:57:58 | ERROR    | src.core.account_manager:get_accounts:557 | 获取账号列表失败: No module named 'src.models'
2025-07-25 01:58:03 | ERROR    | src.core.account_manager:get_accounts:557 | 获取账号列表失败: No module named 'src.models'
2025-07-25 01:58:08 | ERROR    | src.core.account_manager:get_accounts:557 | 获取账号列表失败: No module named 'src.models'
2025-07-25 01:58:13 | ERROR    | src.core.account_manager:get_accounts:557 | 获取账号列表失败: No module named 'src.models'
2025-07-25 01:58:18 | ERROR    | src.core.account_manager:get_accounts:557 | 获取账号列表失败: No module named 'src.models'
2025-07-25 01:58:23 | ERROR    | src.core.account_manager:get_accounts:557 | 获取账号列表失败: No module named 'src.models'
2025-07-25 01:58:28 | ERROR    | src.core.account_manager:get_accounts:557 | 获取账号列表失败: No module named 'src.models'
2025-07-25 01:58:28 | ERROR    | src.core.account_manager:get_accounts:557 | 获取账号列表失败: No module named 'src.models'
2025-07-25 01:58:28 | ERROR    | src.core.account_manager:get_accounts:557 | 获取账号列表失败: No module named 'src.models'
2025-07-25 01:58:28 | ERROR    | src.core.account_manager:get_accounts:557 | 获取账号列表失败: No module named 'src.models'
2025-07-25 01:58:33 | ERROR    | src.core.account_manager:get_accounts:557 | 获取账号列表失败: No module named 'src.models'
2025-07-25 01:58:38 | ERROR    | src.core.account_manager:get_accounts:557 | 获取账号列表失败: No module named 'src.models'
2025-07-25 03:13:10 | ERROR    | src.ui.main_window:create_tabs:111 | 创建标签页失败: type object 'Qt' has no attribute 'Horizontal'
2025-07-25 03:34:20 | ERROR    | src.modules.posting.content_editor_widget:analyze_content:551 | 内容分析失败: wrapped C/C++ object of type QLabel has been deleted
2025-07-25 03:39:16 | ERROR    | __main__:handle_exception:323 | 未捕获的异常
2025-07-25 03:39:34 | ERROR    | src.modules.posting.content_editor_widget:update_variable_list:597 | 更新变量列表失败: wrapped C/C++ object of type QListWidget has been deleted
2025-07-25 03:39:34 | ERROR    | src.modules.posting.content_editor_widget:update_history_list:779 | 更新历史记录失败: wrapped C/C++ object of type QListWidget has been deleted
2025-07-25 03:56:00 | ERROR    | src.modules.posting.content_editor_widget:analyze_content:551 | 内容分析失败: wrapped C/C++ object of type QLabel has been deleted
2025-07-25 03:57:39 | ERROR    | src.modules.variables.variable_manager:add_variable:123 | 添加变量失败: the JSON object must be str, bytes or bytearray, not InstrumentedAttribute
2025-07-25 03:58:03 | ERROR    | src.modules.variables.variable_manager:add_variable:123 | 添加变量失败: the JSON object must be str, bytes or bytearray, not InstrumentedAttribute
2025-07-25 04:04:07 | ERROR    | __main__:handle_exception:323 | 未捕获的异常
2025-07-25 04:04:11 | ERROR    | __main__:handle_exception:323 | 未捕获的异常
2025-07-25 04:04:44 | ERROR    | __main__:handle_exception:323 | 未捕获的异常
2025-07-25 04:05:55 | ERROR    | __main__:handle_exception:323 | 未捕获的异常: KeyError: 'name'
2025-07-25 04:06:11 | ERROR    | __main__:handle_exception:323 | 未捕获的异常: KeyError: 'name'
2025-07-25 04:06:41 | ERROR    | __main__:handle_exception:323 | 未捕获的异常: KeyError: 'name'
2025-07-25 04:06:45 | ERROR    | __main__:handle_exception:323 | 未捕获的异常: KeyError: 'name'
2025-07-25 04:07:27 | ERROR    | __main__:handle_exception:325 | 未捕获的异常: KeyError: 'name'
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\variable_widget.py", line 526, in <lambda>
    edit_btn.clicked.connect(lambda checked, n=name: self.edit_variable(n))
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\variable_widget.py", line 559, in edit_variable
    dialog = VariableEditDialog(variable_data, parent=self)
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\variable_widget.py", line 236, in __init__
    self.load_variable_data()
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\variable_widget.py", line 307, in load_variable_data
    self.name_edit.setText(self.variable_data['name'])
KeyError: 'name'

2025-07-25 04:07:42 | ERROR    | __main__:handle_exception:325 | 未捕获的异常: KeyError: 'name'
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\variable_widget.py", line 526, in <lambda>
    edit_btn.clicked.connect(lambda checked, n=name: self.edit_variable(n))
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\variable_widget.py", line 559, in edit_variable
    dialog = VariableEditDialog(variable_data, parent=self)
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\variable_widget.py", line 236, in __init__
    self.load_variable_data()
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\variable_widget.py", line 307, in load_variable_data
    self.name_edit.setText(self.variable_data['name'])
KeyError: 'name'

2025-07-25 04:10:16 | ERROR    | __main__:handle_exception:325 | 未捕获的异常: RuntimeError: wrapped C/C++ object of type QLineEdit has been deleted
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 524, in open_variables_dialog
    dialog = VariablesDialog(self)
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 951, in __init__
    self.setup_ui()
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 966, in setup_ui
    self.parent_widget.var_search_edit.textChanged.connect(self.parent_widget.filter_variables)
RuntimeError: wrapped C/C++ object of type QLineEdit has been deleted

2025-07-25 04:10:25 | ERROR    | __main__:handle_exception:325 | 未捕获的异常: RuntimeError: wrapped C/C++ object of type QLineEdit has been deleted
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 524, in open_variables_dialog
    dialog = VariablesDialog(self)
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 951, in __init__
    self.setup_ui()
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 966, in setup_ui
    self.parent_widget.var_search_edit.textChanged.connect(self.parent_widget.filter_variables)
RuntimeError: wrapped C/C++ object of type QLineEdit has been deleted

2025-07-25 04:10:25 | ERROR    | __main__:handle_exception:325 | 未捕获的异常: RuntimeError: wrapped C/C++ object of type QLineEdit has been deleted
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 524, in open_variables_dialog
    dialog = VariablesDialog(self)
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 951, in __init__
    self.setup_ui()
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 966, in setup_ui
    self.parent_widget.var_search_edit.textChanged.connect(self.parent_widget.filter_variables)
RuntimeError: wrapped C/C++ object of type QLineEdit has been deleted

2025-07-25 04:11:00 | ERROR    | src.modules.posting.content_editor_widget:update_analysis_display:592 | 更新分析显示失败: wrapped C/C++ object of type QLabel has been deleted
2025-07-25 04:16:51 | ERROR    | __main__:handle_exception:325 | 未捕获的异常: RuntimeError: wrapped C/C++ object of type QListWidget has been deleted
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 532, in open_variables_dialog
    dialog = VariablesDialog(self)
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 974, in __init__
    self.setup_ui()
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1006, in setup_ui
    self.parent_widget.variable_list.itemDoubleClicked.connect(self.insert_and_close)
RuntimeError: wrapped C/C++ object of type QListWidget has been deleted

2025-07-25 04:20:24 | ERROR    | __main__:handle_exception:325 | 未捕获的异常: SystemError: <class 'PyQt6.QtWidgets.QListWidget'> returned a result with an error set
堆栈跟踪:
RuntimeError: wrapped C/C++ object of type QListWidget has been deleted

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 532, in open_variables_dialog
    dialog = VariablesDialog(self)
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1030, in __init__
    self.setup_ui()
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1071, in setup_ui
    temp_variable_list = QListWidget()
SystemError: <class 'PyQt6.QtWidgets.QListWidget'> returned a result with an error set

2025-07-25 04:20:33 | ERROR    | __main__:handle_exception:325 | 未捕获的异常: SystemError: <class 'PyQt6.QtWidgets.QListWidget'> returned a result with an error set
堆栈跟踪:
RuntimeError: wrapped C/C++ object of type QListWidget has been deleted

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 532, in open_variables_dialog
    dialog = VariablesDialog(self)
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1030, in __init__
    self.setup_ui()
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1071, in setup_ui
    temp_variable_list = QListWidget()
SystemError: <class 'PyQt6.QtWidgets.QListWidget'> returned a result with an error set

2025-07-25 04:20:37 | ERROR    | __main__:handle_exception:325 | 未捕获的异常: SystemError: <class 'PyQt6.QtWidgets.QListWidget'> returned a result with an error set
堆栈跟踪:
RuntimeError: wrapped C/C++ object of type QListWidget has been deleted

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 532, in open_variables_dialog
    dialog = VariablesDialog(self)
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1030, in __init__
    self.setup_ui()
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1071, in setup_ui
    temp_variable_list = QListWidget()
SystemError: <class 'PyQt6.QtWidgets.QListWidget'> returned a result with an error set

2025-07-25 04:21:05 | ERROR    | __main__:handle_exception:325 | 未捕获的异常: SystemError: <class 'PyQt6.QtWidgets.QListWidget'> returned a result with an error set
堆栈跟踪:
RuntimeError: wrapped C/C++ object of type QListWidget has been deleted

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 532, in open_variables_dialog
    dialog = VariablesDialog(self)
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1030, in __init__
    self.setup_ui()
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1071, in setup_ui
    temp_variable_list = QListWidget()
SystemError: <class 'PyQt6.QtWidgets.QListWidget'> returned a result with an error set

2025-07-25 04:22:14 | ERROR    | __main__:handle_exception:325 | 未捕获的异常: NameError: name 'custom_layout' is not defined
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 532, in open_variables_dialog
    dialog = VariablesDialog(self)
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1049, in __init__
    self.setup_ui()
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1094, in setup_ui
    self._create_temp_variable_list(custom_layout)
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1129, in _create_temp_variable_list
    custom_layout.addLayout(var_btn_layout)
NameError: name 'custom_layout' is not defined

2025-07-25 04:23:26 | ERROR    | __main__:handle_exception:325 | 未捕获的异常: NameError: name 'tab_widget' is not defined
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 532, in open_variables_dialog
    dialog = VariablesDialog(self)
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1049, in __init__
    self.setup_ui()
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1094, in setup_ui
    self._create_temp_variable_list(custom_layout)
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1131, in _create_temp_variable_list
    tab_widget.addTab(custom_tab, "自定义变量")
NameError: name 'tab_widget' is not defined

2025-07-25 04:25:30 | ERROR    | __main__:handle_exception:325 | 未捕获的异常: RuntimeError: wrapped C/C++ object of type QListWidget has been deleted
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 532, in open_variables_dialog
    dialog = VariablesDialog(self)
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1049, in __init__
    self.setup_ui()
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1094, in setup_ui
    self._create_temp_variable_list(custom_layout)
RuntimeError: wrapped C/C++ object of type QListWidget has been deleted

2025-07-25 04:30:19 | ERROR    | __main__:handle_exception:325 | 未捕获的异常: RuntimeError: wrapped C/C++ object of type QListWidget has been deleted
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 532, in open_variables_dialog
    dialog = VariablesDialog(self)
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1086, in __init__
    self.setup_ui()
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1151, in setup_ui
    self._create_temp_variable_list(custom_layout)
RuntimeError: wrapped C/C++ object of type QListWidget has been deleted

2025-07-25 04:57:25 | ERROR    | src.ui.widgets.account_widget:load_groups:528 | 加载分组失败: 数据库管理器未初始化
2025-07-25 04:57:26 | ERROR    | src.ui.widgets.proxy_widget:load_data:202 | 加载代理数据失败: 数据库管理器未初始化
2025-07-25 04:57:26 | ERROR    | src.ui.main_window:create_tabs:111 | 创建标签页失败: 数据库管理器未初始化
2025-07-25 04:57:26 | ERROR    | src.ui.widgets.account_widget:load_data:560 | 加载账号数据失败: 数据库管理器未初始化
2025-07-25 04:57:56 | ERROR    | src.ui.widgets.account_widget:load_data:560 | 加载账号数据失败: 数据库管理器未初始化
2025-07-25 04:58:05 | ERROR    | src.ui.widgets.account_widget:load_data:560 | 加载账号数据失败: 数据库管理器未初始化
2025-07-25 04:58:06 | ERROR    | src.ui.widgets.account_widget:load_groups:528 | 加载分组失败: 数据库管理器未初始化
2025-07-25 04:58:06 | ERROR    | src.ui.widgets.account_widget:load_data:560 | 加载账号数据失败: 数据库管理器未初始化
2025-07-25 04:58:13 | ERROR    | src.ui.main_window:update_status:248 | 更新状态失败: 数据库管理器未初始化
2025-07-25 04:58:18 | ERROR    | src.ui.main_window:update_status:248 | 更新状态失败: 数据库管理器未初始化
2025-07-25 04:58:23 | ERROR    | src.ui.main_window:update_status:248 | 更新状态失败: 数据库管理器未初始化
2025-07-25 05:26:23 | ERROR    | src.modules.variables.variable_manager:add_variable:123 | 添加变量失败: 变量类型必须是: emoji, text, tag
2025-07-25 05:27:10 | ERROR    | __main__:handle_exception:332 | 未捕获的异常: AttributeError: 'QMenu' object has no attribute 'exec_'
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\variable_widget.py", line 706, in show_context_menu
    menu.exec_(self.variable_table.mapToGlobal(position))
AttributeError: 'QMenu' object has no attribute 'exec_'

2025-07-25 05:27:34 | ERROR    | src.modules.variables.variable_manager:add_variable:123 | 添加变量失败: 变量类型必须是: emoji, text, tag
2025-07-25 05:30:34 | ERROR    | src.modules.posting.content_editor_widget:refresh_data:544 | 刷新数据失败: 'ContentEditorWidget' object has no attribute 'update_variable_list'
2025-07-25 05:31:15 | ERROR    | src.modules.variables.variable_manager:add_variable:82 | 变量名格式不正确: 123
2025-07-25 05:32:54 | ERROR    | src.modules.posting.content_editor_widget:refresh_data:544 | 刷新数据失败: 'ContentEditorWidget' object has no attribute 'update_variable_list'
2025-07-25 05:34:24 | ERROR    | src.modules.posting.content_editor_widget:refresh_data:544 | 刷新数据失败: 'ContentEditorWidget' object has no attribute 'update_variable_list'
2025-07-25 06:17:19 | ERROR    | src.ui.main_window:open_template_config:387 | 打开模板池配置失败: QDialog(parent: Optional[QWidget] = None, flags: Union[Qt.WindowFlags, Qt.WindowType] = Qt.WindowFlags()): argument 1 has unexpected type 'MainWindow'
2025-07-25 06:19:21 | ERROR    | src.ui.main_window:open_template_config:387 | 打开模板池配置失败: QDialog(parent: Optional[QWidget] = None, flags: Union[Qt.WindowFlags, Qt.WindowType] = Qt.WindowFlags()): argument 1 has unexpected type 'MainWindow'
2025-07-25 06:22:11 | ERROR    | src.ui.main_window:open_template_config:387 | 打开模板池配置失败: 'TemplatePoolConfigDialog' object has no attribute 'exec_'
2025-07-25 06:25:16 | ERROR    | src.ui.dialogs.template_pool_config_dialog:save_config:467 | 保存配置失败: type object 'Qt' has no attribute 'UserRole'
2025-07-25 06:26:16 | ERROR    | src.ui.main_window:open_template_config:387 | 打开模板池配置失败: type object 'QDialog' has no attribute 'Accepted'
2025-07-25 06:28:17 | ERROR    | src.ui.main_window:open_template_config:387 | 打开模板池配置失败: type object 'QDialog' has no attribute 'Accepted'
2025-07-25 06:34:03 | ERROR    | src.modules.posting.template_pool:get_template_for_account:272 | 获取模板失败: string indices must be integers
2025-07-25 06:34:03 | ERROR    | src.modules.posting.template_pool:get_template_for_account:272 | 获取模板失败: string indices must be integers
2025-07-25 06:34:03 | ERROR    | src.modules.posting.template_pool:get_template_for_account:272 | 获取模板失败: string indices must be integers
2025-07-25 06:34:03 | ERROR    | src.modules.posting.template_pool:get_template_for_account:272 | 获取模板失败: string indices must be integers
2025-07-25 06:34:03 | ERROR    | src.modules.posting.template_pool:get_template_for_account:272 | 获取模板失败: string indices must be integers
2025-07-25 06:34:11 | ERROR    | src.modules.posting.template_pool:get_template_for_account:272 | 获取模板失败: string indices must be integers
2025-07-25 06:34:11 | ERROR    | src.modules.posting.template_pool:get_template_for_account:272 | 获取模板失败: string indices must be integers
2025-07-25 06:34:11 | ERROR    | src.modules.posting.template_pool:get_template_for_account:272 | 获取模板失败: string indices must be integers
2025-07-25 06:34:11 | ERROR    | src.modules.posting.template_pool:get_template_for_account:272 | 获取模板失败: string indices must be integers
2025-07-25 06:34:18 | ERROR    | src.modules.posting.template_pool:get_template_for_account:272 | 获取模板失败: string indices must be integers
2025-07-25 06:34:18 | ERROR    | src.modules.posting.template_pool:get_template_for_account:272 | 获取模板失败: string indices must be integers
2025-07-25 06:34:18 | ERROR    | src.modules.posting.template_pool:get_template_for_account:272 | 获取模板失败: string indices must be integers
2025-07-25 06:37:31 | ERROR    | src.ui.main_window:create_tabs:144 | 创建标签页失败: No module named 'src.utils.settings'
2025-07-25 06:38:18 | ERROR    | src.modules.posting.template_pool:get_template_for_account:261 | 获取模板失败: string indices must be integers
2025-07-25 06:38:18 | ERROR    | src.modules.posting.template_pool:get_template_for_account:261 | 获取模板失败: string indices must be integers
2025-07-25 06:38:18 | ERROR    | src.modules.posting.template_pool:get_template_for_account:261 | 获取模板失败: string indices must be integers
2025-07-25 06:40:27 | ERROR    | src.modules.posting.template_pool:get_template_for_account:261 | 获取模板失败: string indices must be integers
2025-07-25 06:40:27 | ERROR    | src.modules.posting.template_pool:get_template_for_account:261 | 获取模板失败: string indices must be integers
2025-07-25 06:40:27 | ERROR    | src.modules.posting.template_pool:get_template_for_account:261 | 获取模板失败: string indices must be integers
2025-07-25 06:40:27 | ERROR    | src.modules.posting.template_pool:get_template_for_account:261 | 获取模板失败: string indices must be integers
2025-07-25 06:40:27 | ERROR    | src.modules.posting.template_pool:get_template_for_account:261 | 获取模板失败: string indices must be integers
2025-07-25 06:40:42 | ERROR    | src.modules.posting.template_pool:get_template_for_account:261 | 获取模板失败: string indices must be integers
2025-07-25 06:40:42 | ERROR    | src.modules.posting.template_pool:get_template_for_account:261 | 获取模板失败: string indices must be integers
2025-07-25 06:40:42 | ERROR    | src.modules.posting.template_pool:get_template_for_account:261 | 获取模板失败: string indices must be integers
2025-07-25 06:40:42 | ERROR    | src.modules.posting.template_pool:get_template_for_account:261 | 获取模板失败: string indices must be integers
2025-07-25 06:40:42 | ERROR    | src.modules.posting.template_pool:get_template_for_account:261 | 获取模板失败: string indices must be integers
2025-07-25 06:41:35 | ERROR    | src.modules.posting.template_pool:get_template_for_account:267 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:41:35 | ERROR    | src.modules.posting.template_pool:get_template_for_account:267 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:41:35 | ERROR    | src.modules.posting.template_pool:get_template_for_account:267 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:41:35 | ERROR    | src.modules.posting.template_pool:get_template_for_account:267 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:41:35 | ERROR    | src.modules.posting.template_pool:get_template_for_account:267 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:41:45 | ERROR    | src.modules.posting.template_pool:get_template_for_account:267 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:41:45 | ERROR    | src.modules.posting.template_pool:get_template_for_account:267 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:41:45 | ERROR    | src.modules.posting.template_pool:get_template_for_account:267 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:41:45 | ERROR    | src.modules.posting.template_pool:get_template_for_account:267 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:41:45 | ERROR    | src.modules.posting.template_pool:get_template_for_account:267 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:42:20 | ERROR    | src.modules.posting.template_pool:get_template_for_account:270 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:42:20 | ERROR    | src.modules.posting.template_pool:get_template_for_account:270 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:42:20 | ERROR    | src.modules.posting.template_pool:get_template_for_account:270 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:42:20 | ERROR    | src.modules.posting.template_pool:get_template_for_account:270 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:42:20 | ERROR    | src.modules.posting.template_pool:get_template_for_account:270 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:43:52 | ERROR    | src.modules.posting.template_pool:get_template_for_account:286 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:43:52 | ERROR    | src.modules.posting.template_pool:get_template_for_account:286 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:43:52 | ERROR    | src.modules.posting.template_pool:get_template_for_account:286 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:43:52 | ERROR    | src.modules.posting.template_pool:get_template_for_account:286 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:43:52 | ERROR    | src.modules.posting.template_pool:get_template_for_account:286 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:47:45 | ERROR    | src.modules.posting.template_pool:get_template_for_account:270 | 意外的字符串模板，这不应该发生: 上帝是
我们哈哈哈...
2025-07-25 06:47:45 | ERROR    | src.modules.posting.template_pool:get_template_for_account:270 | 意外的字符串模板，这不应该发生: 哈啊{{random_emoji}} {{custom_text}}...
2025-07-25 06:47:45 | ERROR    | src.modules.posting.template_pool:get_template_for_account:270 | 意外的字符串模板，这不应该发生: {{random_emoji}}

{{random_emoji}}才说的   {{custom_t...
2025-07-25 06:47:45 | ERROR    | src.modules.posting.template_pool:get_template_for_account:270 | 意外的字符串模板，这不应该发生: 哈哈{{random_emoji}} {{custom_text}}...
2025-07-25 06:47:45 | ERROR    | src.modules.posting.template_pool:get_template_for_account:270 | 意外的字符串模板，这不应该发生: 上帝是
我们哈哈哈...
2025-07-25 14:38:42 | ERROR    | src.modules.variables.variable_manager:add_variable:123 | 添加变量失败: 变量类型必须是: emoji, text, tag
2025-07-25 14:39:03 | ERROR    | src.modules.variables.variable_manager:add_variable:123 | 添加变量失败: 变量类型必须是: emoji, text, tag
2025-07-25 15:07:45 | ERROR    | src.modules.variables.variable_manager:add_variable:123 | 添加变量失败: 变量类型必须是: emoji, text, tag
2025-07-25 15:08:16 | ERROR    | src.modules.variables.variable_manager:add_variable:123 | 添加变量失败: 变量类型必须是: emoji, text, tag
2025-07-25 15:31:08 | ERROR    | src.ui.widgets.account_widget:batch_login_accounts:1048 | 批量登录账号失败: 'AccountWidget' object has no attribute 'get_selected_accounts'
2025-07-25 15:33:55 | ERROR    | src.modules.posting.executor:login_account:97 | 登录异常: PaigeBaldw49027, Page.goto: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://x.com/i/flow/login", waiting until "networkidle"

2025-07-25 15:34:30 | ERROR    | src.modules.posting.executor:login_account:97 | 登录异常: CharleneCa2057, Locator.wait_for: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("[data-testid=\"ocfEnterTextNextButton\"]") to be visible

2025-07-25 15:34:30 | ERROR    | __main__:handle_exception:332 | 未捕获的异常: AttributeError: 'AccountWidget' object has no attribute 'refresh_accounts'
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yyu7\src\ui\widgets\account_widget.py", line 1106, in on_login_finished
    self.refresh_accounts()
AttributeError: 'AccountWidget' object has no attribute 'refresh_accounts'

2025-07-25 15:37:05 | ERROR    | src.modules.posting.executor:login_account:97 | 登录异常: PaigeBaldw49027, Page.goto: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://x.com/i/flow/login", waiting until "networkidle"

2025-07-25 15:37:45 | ERROR    | src.modules.posting.executor:login_account:97 | 登录异常: CharleneCa2057, Page.goto: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://x.com/i/flow/login", waiting until "networkidle"

2025-07-25 15:54:52 | ERROR    | src.modules.posting.executor:login_account:129 | 登录异常: PaigeBaldw49027 (尝试 1/3), Locator.wait_for: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("[data-testid=\"ocfEnterTextNextButton\"]") to be visible

2025-07-25 15:55:57 | ERROR    | src.modules.posting.executor:login_account:129 | 登录异常: PaigeBaldw49027 (尝试 2/3), Locator.wait_for: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("[data-testid=\"ocfEnterTextNextButton\"]") to be visible

2025-07-25 15:57:01 | ERROR    | src.modules.posting.executor:login_account:129 | 登录异常: PaigeBaldw49027 (尝试 3/3), Locator.wait_for: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("[data-testid=\"ocfEnterTextNextButton\"]") to be visible

2025-07-25 15:57:42 | ERROR    | src.modules.posting.executor:login_account:129 | 登录异常: CharleneCa2057 (尝试 1/3), Page.goto: Target page, context or browser has been closed
2025-07-25 15:57:49 | ERROR    | src.modules.posting.executor:login_account:129 | 登录异常: CharleneCa2057 (尝试 2/3), Page.goto: Target page, context or browser has been closed
2025-07-25 15:57:54 | ERROR    | src.modules.posting.executor:login_account:129 | 登录异常: CharleneCa2057 (尝试 3/3), Page.goto: Target page, context or browser has been closed
2025-07-25 16:21:55 | ERROR    | src.modules.posting.executor:login_account:177 | 登录异常: CharleneCa2057 (尝试 1/3), Page.goto: net::ERR_PROXY_CONNECTION_FAILED at https://x.com/i/flow/login
Call log:
  - navigating to "https://x.com/i/flow/login", waiting until "domcontentloaded"

2025-07-25 16:22:08 | ERROR    | src.modules.posting.executor:login_account:177 | 登录异常: CharleneCa2057 (尝试 2/3), Page.goto: net::ERR_PROXY_CONNECTION_FAILED at https://x.com/i/flow/login
Call log:
  - navigating to "https://x.com/i/flow/login", waiting until "domcontentloaded"

2025-07-25 16:22:22 | ERROR    | src.modules.posting.executor:login_account:177 | 登录异常: CharleneCa2057 (尝试 3/3), Page.goto: net::ERR_PROXY_CONNECTION_FAILED at https://x.com/i/flow/login
Call log:
  - navigating to "https://x.com/i/flow/login", waiting until "domcontentloaded"

2025-07-25 16:38:25 | ERROR    | __main__:handle_exception:332 | 未捕获的异常: ImportError: cannot import name 'X_SELECTORS' from 'src.config.constants' (C:\Users\<USER>\Desktop\yyu7\src\config\constants.py)
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yyu7\src\ui\widgets\account_widget.py", line 39, in run
    from src.core.account_status_manager import AccountStatusManager
  File "C:\Users\<USER>\Desktop\yyu7\src\core\account_status_manager.py", line 15, in <module>
    from src.config.constants import ACCOUNT_STATUS, X_SELECTORS
ImportError: cannot import name 'X_SELECTORS' from 'src.config.constants' (C:\Users\<USER>\Desktop\yyu7\src\config\constants.py)

2025-07-25 17:15:15 | ERROR    | src.modules.posting.content_manager:prepare_content:355 | 准备内容失败: get_random_media() got an unexpected keyword argument 'image_count'
2025-07-25 17:15:15 | ERROR    | src.modules.posting.workflow:_create_posting_task:158 | 创建发帖任务失败: the JSON object must be str, bytes or bytearray, not InstrumentedAttribute
2025-07-25 17:15:15 | ERROR    | src.modules.posting.content_manager:prepare_content:355 | 准备内容失败: get_random_media() got an unexpected keyword argument 'image_count'
2025-07-25 17:15:15 | ERROR    | src.modules.posting.workflow:_create_posting_task:158 | 创建发帖任务失败: the JSON object must be str, bytes or bytearray, not InstrumentedAttribute
2025-07-25 17:15:46 | ERROR    | src.modules.posting.content_manager:prepare_content:355 | 准备内容失败: get_random_media() got an unexpected keyword argument 'image_count'
2025-07-25 17:15:46 | ERROR    | src.modules.posting.workflow:_create_posting_task:158 | 创建发帖任务失败: the JSON object must be str, bytes or bytearray, not InstrumentedAttribute
2025-07-25 17:15:46 | ERROR    | src.modules.posting.content_manager:prepare_content:355 | 准备内容失败: get_random_media() got an unexpected keyword argument 'image_count'
2025-07-25 17:15:46 | ERROR    | src.modules.posting.workflow:_create_posting_task:158 | 创建发帖任务失败: the JSON object must be str, bytes or bytearray, not InstrumentedAttribute
2025-07-25 17:22:27 | ERROR    | src.modules.posting.content_manager:prepare_content:355 | 准备内容失败: get_random_media() got an unexpected keyword argument 'image_count'
2025-07-25 17:22:27 | ERROR    | src.modules.posting.workflow:_create_posting_task:158 | 创建发帖任务失败: the JSON object must be str, bytes or bytearray, not InstrumentedAttribute
2025-07-25 17:22:27 | ERROR    | src.modules.posting.content_manager:prepare_content:355 | 准备内容失败: get_random_media() got an unexpected keyword argument 'image_count'
2025-07-25 17:22:27 | ERROR    | src.modules.posting.workflow:_create_posting_task:158 | 创建发帖任务失败: the JSON object must be str, bytes or bytearray, not InstrumentedAttribute
2025-07-25 17:27:04 | ERROR    | src.modules.posting.workflow:_create_posting_task:158 | 创建发帖任务失败: the JSON object must be str, bytes or bytearray, not InstrumentedAttribute
2025-07-25 17:27:04 | ERROR    | src.modules.posting.workflow:_create_posting_task:158 | 创建发帖任务失败: the JSON object must be str, bytes or bytearray, not InstrumentedAttribute
2025-07-25 17:31:09 | ERROR    | src.modules.posting.workflow:_create_posting_task:162 | 创建发帖任务失败: the JSON object must be str, bytes or bytearray, not InstrumentedAttribute
2025-07-25 17:31:09 | ERROR    | src.modules.posting.workflow:_create_posting_task:162 | 创建发帖任务失败: the JSON object must be str, bytes or bytearray, not InstrumentedAttribute
2025-07-25 17:33:15 | ERROR    | src.modules.posting.workflow:_execute_sequential_posting:198 | 执行发帖任务失败: Instance <PostingTask at 0x222d99bfa90> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-25 17:33:15 | ERROR    | src.modules.posting.workflow:execute_batch_posting:105 | 批量发帖失败: Instance <PostingTask at 0x222d99bfa90> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-25 17:34:36 | ERROR    | src.modules.posting.workflow:_execute_sequential_posting:198 | 执行发帖任务失败: Instance <PostingTask at 0x1f8bbdd4850> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-25 17:34:36 | ERROR    | src.modules.posting.workflow:execute_batch_posting:105 | 批量发帖失败: Instance <PostingTask at 0x1f8bbdd4850> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-25 17:35:27 | ERROR    | src.modules.posting.workflow:execute_batch_posting:114 | 批量发帖失败: Instance <PostingTask at 0x18f87a85850> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-25 17:37:32 | ERROR    | src.modules.posting.workflow:execute_batch_posting:105 | 批量发帖失败: Instance <PostingTask at 0x1d690174850> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-25 17:38:30 | ERROR    | src.modules.posting.workflow:execute_batch_posting:105 | 批量发帖失败: Instance <PostingTask at 0x1369abc4b80> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-25 17:39:33 | ERROR    | src.modules.posting.workflow:_execute_single_posting_task_by_id:316 | 执行发帖任务失败: 'PostingWorkflow' object has no attribute 'poster'
2025-07-25 17:39:33 | ERROR    | src.modules.posting.workflow:_execute_single_posting_task_by_id:316 | 执行发帖任务失败: 'PostingWorkflow' object has no attribute 'poster'
2025-07-25 17:40:44 | ERROR    | src.modules.posting.workflow:_execute_single_posting_task_by_id:316 | 执行发帖任务失败: 'PostingExecutor' object has no attribute 'post'
2025-07-25 17:40:44 | ERROR    | src.modules.posting.workflow:_execute_single_posting_task_by_id:316 | 执行发帖任务失败: 'PostingExecutor' object has no attribute 'post'
2025-07-25 17:43:49 | ERROR    | src.ui.widgets.account_widget:load_groups:694 | 加载分组失败: 数据库管理器未初始化
2025-07-25 17:43:49 | ERROR    | src.ui.widgets.proxy_widget:load_data:202 | 加载代理数据失败: 数据库管理器未初始化
2025-07-25 17:43:49 | ERROR    | src.ui.main_window:create_tabs:144 | 创建标签页失败: 数据库管理器未初始化
2025-07-25 17:43:49 | ERROR    | src.ui.widgets.account_widget:load_data:726 | 加载账号数据失败: 数据库管理器未初始化
2025-07-25 17:44:00 | ERROR    | src.ui.main_window:update_status:288 | 更新状态失败: 数据库管理器未初始化
2025-07-25 17:44:05 | ERROR    | src.ui.main_window:update_status:288 | 更新状态失败: 数据库管理器未初始化
2025-07-25 17:44:10 | ERROR    | src.ui.main_window:update_status:288 | 更新状态失败: 数据库管理器未初始化
2025-07-25 17:46:08 | ERROR    | src.modules.posting.executor:_input_text_content:364 | 输入文本内容失败: Locator.wait_for: Error: strict mode violation: locator("[data-testid=\"tweetTextarea_0\"]") resolved to 2 elements:
    1) <div tabindex="0" role="textbox" spellcheck="true" aria-multiline="true" aria-label="Post text" contenteditable="true" aria-autocomplete="list" data-testid="tweetTextarea_0" no-focustrapview-refocus="true" aria-describedby="placeholder-6r22t" aria-controls="typeaheadDropdownWrapped-7" class="notranslate public-DraftEditor-content" aria-activedescendant="typeaheadFocus-0.7887473660550232">…</div> aka get_by_role("textbox", name="Post text")
    2) <div tabindex="0" role="textbox" spellcheck="true" aria-multiline="true" aria-label="Post text" contenteditable="true" aria-autocomplete="list" data-testid="tweetTextarea_0" no-focustrapview-refocus="true" aria-describedby="placeholder-c77cc" aria-controls="typeaheadDropdownWrapped-6" class="notranslate public-DraftEditor-content" aria-activedescendant="typeaheadFocus-0.5782926927736265">…</div> aka get_by_test_id("primaryColumn").get_by_test_id("tweetTextarea_0")

Call log:
  - waiting for locator("[data-testid=\"tweetTextarea_0\"]") to be visible

2025-07-25 17:46:08 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, Locator.wait_for: Error: strict mode violation: locator("[data-testid=\"tweetTextarea_0\"]") resolved to 2 elements:
    1) <div tabindex="0" role="textbox" spellcheck="true" aria-multiline="true" aria-label="Post text" contenteditable="true" aria-autocomplete="list" data-testid="tweetTextarea_0" no-focustrapview-refocus="true" aria-describedby="placeholder-6r22t" aria-controls="typeaheadDropdownWrapped-7" class="notranslate public-DraftEditor-content" aria-activedescendant="typeaheadFocus-0.7887473660550232">…</div> aka get_by_role("textbox", name="Post text")
    2) <div tabindex="0" role="textbox" spellcheck="true" aria-multiline="true" aria-label="Post text" contenteditable="true" aria-autocomplete="list" data-testid="tweetTextarea_0" no-focustrapview-refocus="true" aria-describedby="placeholder-c77cc" aria-controls="typeaheadDropdownWrapped-6" class="notranslate public-DraftEditor-content" aria-activedescendant="typeaheadFocus-0.5782926927736265">…</div> aka get_by_test_id("primaryColumn").get_by_test_id("tweetTextarea_0")

Call log:
  - waiting for locator("[data-testid=\"tweetTextarea_0\"]") to be visible

2025-07-25 17:46:37 | ERROR    | src.modules.posting.executor:_input_text_content:364 | 输入文本内容失败: Locator.wait_for: Error: strict mode violation: locator("[data-testid=\"tweetTextarea_0\"]") resolved to 2 elements:
    1) <div tabindex="0" role="textbox" spellcheck="true" aria-multiline="true" aria-label="Post text" contenteditable="true" aria-autocomplete="list" data-testid="tweetTextarea_0" no-focustrapview-refocus="true" aria-describedby="placeholder-4mesc" aria-controls="typeaheadDropdownWrapped-9" class="notranslate public-DraftEditor-content" aria-activedescendant="typeaheadFocus-0.18096618257532016">…</div> aka get_by_role("textbox", name="Post text")
    2) <div tabindex="0" role="textbox" spellcheck="true" aria-multiline="true" aria-label="Post text" contenteditable="true" aria-autocomplete="list" data-testid="tweetTextarea_0" no-focustrapview-refocus="true" aria-describedby="placeholder-bf4eo" aria-controls="typeaheadDropdownWrapped-8" class="notranslate public-DraftEditor-content" aria-activedescendant="typeaheadFocus-0.12133633962791812">…</div> aka get_by_test_id("primaryColumn").get_by_test_id("tweetTextarea_0")

Call log:
  - waiting for locator("[data-testid=\"tweetTextarea_0\"]") to be visible

2025-07-25 17:46:37 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, Locator.wait_for: Error: strict mode violation: locator("[data-testid=\"tweetTextarea_0\"]") resolved to 2 elements:
    1) <div tabindex="0" role="textbox" spellcheck="true" aria-multiline="true" aria-label="Post text" contenteditable="true" aria-autocomplete="list" data-testid="tweetTextarea_0" no-focustrapview-refocus="true" aria-describedby="placeholder-4mesc" aria-controls="typeaheadDropdownWrapped-9" class="notranslate public-DraftEditor-content" aria-activedescendant="typeaheadFocus-0.18096618257532016">…</div> aka get_by_role("textbox", name="Post text")
    2) <div tabindex="0" role="textbox" spellcheck="true" aria-multiline="true" aria-label="Post text" contenteditable="true" aria-autocomplete="list" data-testid="tweetTextarea_0" no-focustrapview-refocus="true" aria-describedby="placeholder-bf4eo" aria-controls="typeaheadDropdownWrapped-8" class="notranslate public-DraftEditor-content" aria-activedescendant="typeaheadFocus-0.12133633962791812">…</div> aka get_by_test_id("primaryColumn").get_by_test_id("tweetTextarea_0")

Call log:
  - waiting for locator("[data-testid=\"tweetTextarea_0\"]") to be visible

2025-07-25 17:46:49 | ERROR    | src.core.browser_manager:close_all:392 | 关闭浏览器失败: Event loop is closed
2025-07-25 17:51:11 | ERROR    | src.modules.posting.executor:_publish_post:436 | 发布帖子失败: Locator.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator("[data-testid=\"tweetButtonInline\"]")
    - locator resolved to <button disabled role="button" tabindex="-1" type="button" aria-disabled="true" data-testid="tweetButtonInline" class="css-175oi2r r-sdzlij r-1phboty r-rs99b7 r-lrvibr r-1cwvpvk r-2yi16 r-1qi8awa r-3pj75a r-o7ynqc r-6416eg r-icoktb r-1ny4l3l">…</button>
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is not enabled
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is not enabled
    - retrying click action
      - waiting 100ms
    57 × waiting for element to be visible, enabled and stable
       - element is not enabled
     - retrying click action
       - waiting 500ms

2025-07-25 17:51:11 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, Locator.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator("[data-testid=\"tweetButtonInline\"]")
    - locator resolved to <button disabled role="button" tabindex="-1" type="button" aria-disabled="true" data-testid="tweetButtonInline" class="css-175oi2r r-sdzlij r-1phboty r-rs99b7 r-lrvibr r-1cwvpvk r-2yi16 r-1qi8awa r-3pj75a r-o7ynqc r-6416eg r-icoktb r-1ny4l3l">…</button>
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is not enabled
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is not enabled
    - retrying click action
      - waiting 100ms
    57 × waiting for element to be visible, enabled and stable
       - element is not enabled
     - retrying click action
       - waiting 500ms

2025-07-25 17:52:14 | ERROR    | src.modules.posting.executor:_publish_post:436 | 发布帖子失败: Locator.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator("[data-testid=\"tweetButtonInline\"]")
    - locator resolved to <button disabled role="button" tabindex="-1" type="button" aria-disabled="true" data-testid="tweetButtonInline" class="css-175oi2r r-sdzlij r-1phboty r-rs99b7 r-lrvibr r-1cwvpvk r-2yi16 r-1qi8awa r-3pj75a r-o7ynqc r-6416eg r-icoktb r-1ny4l3l">…</button>
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is not enabled
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is not enabled
    - retrying click action
      - waiting 100ms
    58 × waiting for element to be visible, enabled and stable
       - element is not enabled
     - retrying click action
       - waiting 500ms

2025-07-25 17:52:14 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, Locator.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator("[data-testid=\"tweetButtonInline\"]")
    - locator resolved to <button disabled role="button" tabindex="-1" type="button" aria-disabled="true" data-testid="tweetButtonInline" class="css-175oi2r r-sdzlij r-1phboty r-rs99b7 r-lrvibr r-1cwvpvk r-2yi16 r-1qi8awa r-3pj75a r-o7ynqc r-6416eg r-icoktb r-1ny4l3l">…</button>
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is not enabled
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is not enabled
    - retrying click action
      - waiting 100ms
    58 × waiting for element to be visible, enabled and stable
       - element is not enabled
     - retrying click action
       - waiting 500ms

2025-07-25 17:54:04 | ERROR    | src.core.browser_manager:close_all:392 | 关闭浏览器失败: Event loop is closed
2025-07-25 18:15:57 | ERROR    | src.modules.posting.executor:_publish_post:465 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态
2025-07-25 18:15:57 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, 发布按钮在等待时间内未变为可用状态
2025-07-25 18:17:10 | ERROR    | src.modules.posting.executor:_publish_post:465 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态
2025-07-25 18:17:10 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, 发布按钮在等待时间内未变为可用状态
2025-07-25 18:18:15 | ERROR    | src.core.browser_manager:close_all:392 | 关闭浏览器失败: Event loop is closed
2025-07-25 22:38:54 | ERROR    | src.modules.posting.executor:_publish_post:511 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-25 22:38:54 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-25 22:40:00 | ERROR    | src.modules.posting.executor:_publish_post:511 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-25 22:40:00 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-25 22:41:00 | ERROR    | src.core.browser_manager:close_all:392 | 关闭浏览器失败: Event loop is closed
2025-07-26 03:38:15 | ERROR    | src.modules.posting.executor:_publish_post:511 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 03:38:15 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 03:39:28 | ERROR    | src.modules.posting.executor:_publish_post:511 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 03:39:28 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 03:42:22 | ERROR    | src.core.browser_manager:close_all:392 | 关闭浏览器失败: Event loop is closed
2025-07-26 03:42:53 | ERROR    | __main__:handle_exception:332 | 未捕获的异常: AttributeError: 'PostingWidget' object has no attribute 'update_status'
堆栈跟踪:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\小美ai助手的源码\yyu11\src\ui\widgets\posting_widget.py", line 382, in stop_posting
    self.update_status("正在停止发帖任务...")
AttributeError: 'PostingWidget' object has no attribute 'update_status'

2025-07-26 03:43:43 | ERROR    | src.modules.posting.executor:_publish_post:511 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 03:43:43 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 03:43:58 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:43:58 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:43:58 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:43:58 | ERROR    | src.core.browser_manager:get_browser_context:85 | 创建浏览器上下文最终失败: PaigeBaldw49027
2025-07-26 03:43:58 | ERROR    | src.core.browser_manager:get_page:408 | 无法获取浏览器上下文: PaigeBaldw49027
2025-07-26 03:43:58 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, 获取页面失败
2025-07-26 03:43:58 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:43:58 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:43:58 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:43:58 | ERROR    | src.core.browser_manager:get_browser_context:85 | 创建浏览器上下文最终失败: CharleneCa2057
2025-07-26 03:43:58 | ERROR    | src.core.browser_manager:get_page:408 | 无法获取浏览器上下文: CharleneCa2057
2025-07-26 03:43:58 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, 获取页面失败
2025-07-26 03:44:14 | ERROR    | src.core.browser_manager:close_all:392 | 关闭浏览器失败: Event loop is closed
2025-07-26 03:47:45 | ERROR    | src.modules.posting.executor:_publish_post:511 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 03:47:45 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 03:48:13 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:48:13 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:48:13 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:48:13 | ERROR    | src.core.browser_manager:get_browser_context:85 | 创建浏览器上下文最终失败: PaigeBaldw49027
2025-07-26 03:48:13 | ERROR    | src.core.browser_manager:get_page:419 | 无法获取浏览器上下文: PaigeBaldw49027
2025-07-26 03:48:13 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, 获取页面失败
2025-07-26 03:48:13 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:48:13 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:48:13 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:48:13 | ERROR    | src.core.browser_manager:get_browser_context:85 | 创建浏览器上下文最终失败: CharleneCa2057
2025-07-26 03:48:13 | ERROR    | src.core.browser_manager:get_page:419 | 无法获取浏览器上下文: CharleneCa2057
2025-07-26 03:48:13 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, 获取页面失败
2025-07-26 03:50:27 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:50:27 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:50:27 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:50:27 | ERROR    | src.core.browser_manager:get_browser_context:85 | 创建浏览器上下文最终失败: PaigeBaldw49027
2025-07-26 03:50:27 | ERROR    | src.core.browser_manager:get_page:419 | 无法获取浏览器上下文: PaigeBaldw49027
2025-07-26 03:50:27 | ERROR    | src.modules.posting.executor:execute_post:395 | 发帖失败: PaigeBaldw49027, 获取页面失败
2025-07-26 03:50:27 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:50:27 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:50:27 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:50:27 | ERROR    | src.core.browser_manager:get_browser_context:85 | 创建浏览器上下文最终失败: CharleneCa2057
2025-07-26 03:50:27 | ERROR    | src.core.browser_manager:get_page:419 | 无法获取浏览器上下文: CharleneCa2057
2025-07-26 03:50:27 | ERROR    | src.modules.posting.executor:execute_post:395 | 发帖失败: CharleneCa2057, 获取页面失败
2025-07-26 03:53:54 | ERROR    | src.modules.posting.executor:_publish_post:511 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 03:53:54 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 03:54:50 | ERROR    | src.modules.posting.executor:_publish_post:511 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 03:54:50 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 03:55:00 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:55:00 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:55:00 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:55:00 | ERROR    | src.core.browser_manager:get_browser_context:85 | 创建浏览器上下文最终失败: PaigeBaldw49027
2025-07-26 03:55:00 | ERROR    | src.core.browser_manager:get_page:408 | 无法获取浏览器上下文: PaigeBaldw49027
2025-07-26 03:55:00 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, 获取页面失败
2025-07-26 03:55:00 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:55:00 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:55:00 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:55:00 | ERROR    | src.core.browser_manager:get_browser_context:85 | 创建浏览器上下文最终失败: CharleneCa2057
2025-07-26 03:55:00 | ERROR    | src.core.browser_manager:get_page:408 | 无法获取浏览器上下文: CharleneCa2057
2025-07-26 03:55:00 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, 获取页面失败
2025-07-26 03:55:09 | ERROR    | src.core.browser_manager:close_all:392 | 关闭浏览器失败: Event loop is closed
2025-07-26 03:59:57 | ERROR    | src.modules.posting.executor:_publish_post:542 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 03:59:57 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 04:00:58 | ERROR    | src.modules.posting.executor:_publish_post:542 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 04:00:58 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 04:01:11 | ERROR    | src.core.browser_manager:_create_browser_context:200 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 04:01:11 | ERROR    | src.core.browser_manager:_create_browser_context:200 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 04:01:11 | ERROR    | src.core.browser_manager:_create_browser_context:200 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 04:01:11 | ERROR    | src.core.browser_manager:get_browser_context:102 | 创建浏览器上下文最终失败: PaigeBaldw49027
2025-07-26 04:01:11 | ERROR    | src.core.browser_manager:get_page:438 | 无法获取浏览器上下文: PaigeBaldw49027
2025-07-26 04:01:11 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, 获取页面失败
2025-07-26 04:01:11 | ERROR    | src.core.browser_manager:_create_browser_context:200 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 04:01:11 | ERROR    | src.core.browser_manager:_create_browser_context:200 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 04:01:11 | ERROR    | src.core.browser_manager:_create_browser_context:200 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 04:01:11 | ERROR    | src.core.browser_manager:get_browser_context:102 | 创建浏览器上下文最终失败: CharleneCa2057
2025-07-26 04:01:11 | ERROR    | src.core.browser_manager:get_page:438 | 无法获取浏览器上下文: CharleneCa2057
2025-07-26 04:01:11 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, 获取页面失败
2025-07-26 04:05:42 | ERROR    | src.modules.posting.executor:_publish_post:542 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 04:05:42 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 04:07:01 | ERROR    | src.modules.posting.executor:_publish_post:542 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 04:07:01 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 04:07:11 | ERROR    | src.core.browser_manager:_create_browser_context:215 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 04:07:11 | ERROR    | src.core.browser_manager:_create_browser_context:215 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 04:07:11 | ERROR    | src.core.browser_manager:_create_browser_context:215 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 04:07:11 | ERROR    | src.core.browser_manager:get_browser_context:117 | 创建浏览器上下文最终失败: PaigeBaldw49027
2025-07-26 04:07:11 | ERROR    | src.core.browser_manager:get_page:471 | 无法获取浏览器上下文: PaigeBaldw49027
2025-07-26 04:07:11 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, 获取页面失败
2025-07-26 04:07:11 | ERROR    | src.core.browser_manager:_create_browser_context:215 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 04:07:11 | ERROR    | src.core.browser_manager:_create_browser_context:215 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 04:07:11 | ERROR    | src.core.browser_manager:_create_browser_context:215 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 04:07:11 | ERROR    | src.core.browser_manager:get_browser_context:117 | 创建浏览器上下文最终失败: CharleneCa2057
2025-07-26 04:07:11 | ERROR    | src.core.browser_manager:get_page:471 | 无法获取浏览器上下文: CharleneCa2057
2025-07-26 04:07:11 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, 获取页面失败
2025-07-26 04:15:59 | ERROR    | src.modules.posting.executor:_publish_post:511 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 04:15:59 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 04:17:00 | ERROR    | src.modules.posting.executor:_publish_post:511 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 04:17:00 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 04:17:15 | ERROR    | src.core.browser_manager:_create_browser_context:188 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 04:17:15 | ERROR    | src.core.browser_manager:_create_browser_context:188 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 04:17:15 | ERROR    | src.core.browser_manager:_create_browser_context:188 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 04:17:15 | ERROR    | src.core.browser_manager:get_browser_context:90 | 创建浏览器上下文最终失败: PaigeBaldw49027
2025-07-26 04:17:15 | ERROR    | src.core.browser_manager:get_page:432 | 无法获取浏览器上下文: PaigeBaldw49027
2025-07-26 04:17:15 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, 获取页面失败
2025-07-26 04:17:15 | ERROR    | src.core.browser_manager:_create_browser_context:188 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 04:17:15 | ERROR    | src.core.browser_manager:_create_browser_context:188 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 04:17:15 | ERROR    | src.core.browser_manager:_create_browser_context:188 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 04:17:15 | ERROR    | src.core.browser_manager:get_browser_context:90 | 创建浏览器上下文最终失败: CharleneCa2057
2025-07-26 04:17:15 | ERROR    | src.core.browser_manager:get_page:432 | 无法获取浏览器上下文: CharleneCa2057
2025-07-26 04:17:15 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, 获取页面失败
2025-07-26 04:26:19 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: The future belongs to a different loop than the one specified as the loop argument
2025-07-26 04:26:19 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: The future belongs to a different loop than the one specified as the loop argument
2025-07-26 04:26:19 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: The future belongs to a different loop than the one specified as the loop argument
2025-07-26 04:26:19 | ERROR    | src.core.browser_manager:get_browser_context:85 | 创建浏览器上下文最终失败: PaigeBaldw49027
2025-07-26 04:26:19 | ERROR    | src.core.browser_manager:get_page:408 | 无法获取浏览器上下文: PaigeBaldw49027
2025-07-26 04:26:19 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, 获取页面失败
2025-07-26 04:26:19 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: The future belongs to a different loop than the one specified as the loop argument
2025-07-26 04:26:19 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: The future belongs to a different loop than the one specified as the loop argument
2025-07-26 04:26:19 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: The future belongs to a different loop than the one specified as the loop argument
2025-07-26 04:26:19 | ERROR    | src.core.browser_manager:get_browser_context:85 | 创建浏览器上下文最终失败: CharleneCa2057
2025-07-26 04:26:19 | ERROR    | src.core.browser_manager:get_page:408 | 无法获取浏览器上下文: CharleneCa2057
2025-07-26 04:26:19 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, 获取页面失败
2025-07-26 04:26:35 | ERROR    | src.core.browser_manager:close_all:392 | 关闭浏览器失败: Task <Task pending name='Task-6' coro=<BrowserPool.close_all() running at C:\Users\<USER>\Desktop\小美ai助手的源码\yyu11\src\core\browser_manager.py:384> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-07-26 04:27:19 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: The future belongs to a different loop than the one specified as the loop argument
2025-07-26 04:27:19 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: The future belongs to a different loop than the one specified as the loop argument
2025-07-26 04:27:19 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: The future belongs to a different loop than the one specified as the loop argument
2025-07-26 04:27:19 | ERROR    | src.core.browser_manager:get_browser_context:85 | 创建浏览器上下文最终失败: PaigeBaldw49027
2025-07-26 04:27:19 | ERROR    | src.core.browser_manager:get_page:408 | 无法获取浏览器上下文: PaigeBaldw49027
2025-07-26 04:27:19 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, 获取页面失败
2025-07-26 04:27:19 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: The future belongs to a different loop than the one specified as the loop argument
2025-07-26 04:27:19 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: The future belongs to a different loop than the one specified as the loop argument
2025-07-26 04:27:19 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: The future belongs to a different loop than the one specified as the loop argument
2025-07-26 04:27:19 | ERROR    | src.core.browser_manager:get_browser_context:85 | 创建浏览器上下文最终失败: CharleneCa2057
2025-07-26 04:27:19 | ERROR    | src.core.browser_manager:get_page:408 | 无法获取浏览器上下文: CharleneCa2057
2025-07-26 04:27:19 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, 获取页面失败
2025-07-26 04:27:30 | ERROR    | src.core.browser_manager:close_all:392 | 关闭浏览器失败: Task <Task pending name='Task-6' coro=<BrowserPool.close_all() running at C:\Users\<USER>\Desktop\小美ai助手的源码\yyu11\src\core\browser_manager.py:384> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-07-26 04:34:15 | ERROR    | src.modules.posting.executor:_publish_post:511 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 04:34:15 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 04:35:18 | ERROR    | src.modules.posting.executor:_publish_post:511 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 04:35:18 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 05:39:56 | ERROR    | src.modules.posting.content_editor_widget:init_content_system:139 | 新系统初始化失败: name 'DatabaseManager' is not defined
2025-07-26 05:41:15 | ERROR    | src.modules.posting.content_editor_widget:init_content_system:139 | 新系统初始化失败: name 'DatabaseManager' is not defined
2025-07-26 05:43:17 | ERROR    | src.modules.posting.content_editor_widget:init_content_system:139 | 新系统初始化失败: name 'DatabaseManager' is not defined
2025-07-26 05:46:17 | ERROR    | src.modules.posting.content_editor_widget:load_template_list:963 | 加载模板列表失败: 'TemplateSelector' object has no attribute 'get_all_templates'
2025-07-26 05:46:17 | ERROR    | src.modules.posting.content_editor_widget:load_personalization_settings:1056 | 加载个性化设置失败: a coroutine was expected, got {}
2025-07-26 05:48:42 | ERROR    | src.modules.posting.content_editor_widget:generate_smart_content:558 | 启动内容生成失败: __init__() got an unexpected keyword argument 'content_type'
2025-07-26 05:48:45 | ERROR    | src.modules.posting.content_editor_widget:preview_smart_content:633 | 预览失败: __init__() got an unexpected keyword argument 'content_type'
2025-07-26 05:48:53 | ERROR    | __main__:handle_exception:382 | 未捕获的异常: NameError: name 'QInputDialog' is not defined
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\小美ai助手的源码\yyu11\src\modules\posting\content_editor_widget.py", line 821, in insert_variable
    variable, ok = QInputDialog.getItem(
NameError: name 'QInputDialog' is not defined

2025-07-26 05:48:54 | ERROR    | __main__:handle_exception:382 | 未捕获的异常: NameError: name 'QInputDialog' is not defined
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\小美ai助手的源码\yyu11\src\modules\posting\content_editor_widget.py", line 821, in insert_variable
    variable, ok = QInputDialog.getItem(
NameError: name 'QInputDialog' is not defined

2025-07-26 05:48:54 | ERROR    | __main__:handle_exception:382 | 未捕获的异常: NameError: name 'QInputDialog' is not defined
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\小美ai助手的源码\yyu11\src\modules\posting\content_editor_widget.py", line 821, in insert_variable
    variable, ok = QInputDialog.getItem(
NameError: name 'QInputDialog' is not defined

2025-07-26 05:48:54 | ERROR    | __main__:handle_exception:382 | 未捕获的异常: NameError: name 'QInputDialog' is not defined
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\小美ai助手的源码\yyu11\src\modules\posting\content_editor_widget.py", line 821, in insert_variable
    variable, ok = QInputDialog.getItem(
NameError: name 'QInputDialog' is not defined

2025-07-26 05:48:55 | ERROR    | __main__:handle_exception:382 | 未捕获的异常: NameError: name 'QInputDialog' is not defined
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\小美ai助手的源码\yyu11\src\modules\posting\content_editor_widget.py", line 821, in insert_variable
    variable, ok = QInputDialog.getItem(
NameError: name 'QInputDialog' is not defined

2025-07-26 05:48:55 | ERROR    | __main__:handle_exception:382 | 未捕获的异常: NameError: name 'QInputDialog' is not defined
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\小美ai助手的源码\yyu11\src\modules\posting\content_editor_widget.py", line 821, in insert_variable
    variable, ok = QInputDialog.getItem(
NameError: name 'QInputDialog' is not defined

2025-07-26 05:48:57 | ERROR    | __main__:handle_exception:382 | 未捕获的异常: NameError: name 'QInputDialog' is not defined
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\小美ai助手的源码\yyu11\src\modules\posting\content_editor_widget.py", line 821, in insert_variable
    variable, ok = QInputDialog.getItem(
NameError: name 'QInputDialog' is not defined

2025-07-26 05:48:59 | ERROR    | src.modules.posting.quality_assessor:assess_content:271 | 内容质量评估失败: invalid group reference 1 at position 9
2025-07-26 05:48:59 | ERROR    | src.modules.posting.content_editor_widget:check_content_quality:874 | 质量检查失败: An asyncio.Future, a coroutine or an awaitable is required
2025-07-26 15:47:59 | ERROR    | src.ui.widgets.posting_widget:load_groups:441 | 加载分组失败: 'AccountManager' object has no attribute 'get_groups'
2025-07-26 15:49:50 | ERROR    | src.ui.widgets.posting_widget:load_groups:441 | 加载分组失败: 'AccountGroup' object has no attribute 'get'
2025-07-26 16:21:58 | ERROR    | src.ui.widgets.posting_widget:update_template_stats:582 | 更新文案库统计失败: 'TemplateManager' object has no attribute 'get_template_stats'
2025-07-26 16:22:03 | ERROR    | src.ui.widgets.posting_widget:open_template_management:556 | 打开文案库管理失败: 'TemplateLibraryDialog' object has no attribute 'template_updated'
2025-07-26 16:23:21 | ERROR    | src.ui.dialogs.template_library_dialog:load_templates:180 | 加载文案模板失败: 'PostingWidget' object has no attribute 'load_templates'
2025-07-26 16:24:42 | ERROR    | src.ui.dialogs.template_library_dialog:save_template:265 | 保存文案失败: 'PostingWidget' object has no attribute 'add_template'
2025-07-26 16:25:21 | ERROR    | src.ui.dialogs.template_library_dialog:load_templates:180 | 加载文案模板失败: 'PostingWidget' object has no attribute 'load_templates'
2025-07-26 17:10:12 | ERROR    | src.ui.main_window:open_variable_manager:384 | 打开变量管理失败: 'SimpleVariablesDialog' object has no attribute 'exec_'
2025-07-26 17:10:23 | ERROR    | src.ui.main_window:open_variable_manager:384 | 打开变量管理失败: 'SimpleVariablesDialog' object has no attribute 'exec_'
2025-07-26 21:20:01 | ERROR    | src.modules.posting.content_manager:process_template:81 | 处理模板失败: expected string or bytes-like object
2025-07-26 21:20:01 | ERROR    | src.modules.posting.workflow:_create_posting_task:209 | 创建发帖任务失败: 'dict' object has no attribute 'strip'
2025-07-26 21:20:01 | ERROR    | src.modules.posting.content_manager:process_template:81 | 处理模板失败: expected string or bytes-like object
2025-07-26 21:20:01 | ERROR    | src.modules.posting.workflow:_create_posting_task:209 | 创建发帖任务失败: 'dict' object has no attribute 'strip'
2025-07-26 21:21:05 | ERROR    | src.modules.posting.content_manager:process_template:81 | 处理模板失败: expected string or bytes-like object
2025-07-26 21:21:05 | ERROR    | src.modules.posting.workflow:_create_posting_task:209 | 创建发帖任务失败: 'dict' object has no attribute 'strip'
2025-07-26 21:21:05 | ERROR    | src.modules.posting.content_manager:process_template:81 | 处理模板失败: expected string or bytes-like object
2025-07-26 21:21:05 | ERROR    | src.modules.posting.workflow:_create_posting_task:209 | 创建发帖任务失败: 'dict' object has no attribute 'strip'
2025-07-26 23:36:26 | ERROR    | src.modules.posting.workflow:_execute_single_posting_task_by_id:366 | 执行发帖任务失败: name 'MediaFile' is not defined
2025-07-26 23:48:00 | ERROR    | src.modules.posting.executor:_upload_media_files:418 | 上传媒体文件失败: Locator.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator("[data-testid=\"attachments\"]")

2025-07-26 23:48:00 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, Locator.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator("[data-testid=\"attachments\"]")

2025-07-26 23:52:27 | ERROR    | src.modules.posting.executor:_publish_post:593 | 发布帖子失败: Locator.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator("[role=\"button\"]:has-text(\"Post\")").first
    - locator resolved to <button role="button" tabindex="-1" type="button" aria-hidden="true" aria-label="New posts are available. Push the period key to go to the them." class="css-175oi2r r-l5o3uw r-sdzlij r-y3da5r r-1777fci r-cnw61z r-3pj75a r-1loqt21 r-o7ynqc r-6416eg r-1ny4l3l">…</button>
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <div data-testid="mask" class="css-175oi2r r-1p0dtai r-1d2f490 r-1xcajam r-zchlnj r-ipm5af r-1ffj0ar"></div> from <div id="layers" class="r-zchlnj r-1d2f490 r-u8s1d r-ipm5af">…</div> subtree intercepts pointer events
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <div data-testid="mask" class="css-175oi2r r-1p0dtai r-1d2f490 r-1xcajam r-zchlnj r-ipm5af r-1ffj0ar"></div> from <div id="layers" class="r-zchlnj r-1d2f490 r-u8s1d r-ipm5af">…</div> subtree intercepts pointer events
    - retrying click action
      - waiting 100ms
    57 × waiting for element to be visible, enabled and stable
       - element is visible, enabled and stable
       - scrolling into view if needed
       - done scrolling
       - <div data-testid="mask" class="css-175oi2r r-1p0dtai r-1d2f490 r-1xcajam r-zchlnj r-ipm5af r-1ffj0ar"></div> from <div id="layers" class="r-zchlnj r-1d2f490 r-u8s1d r-ipm5af">…</div> subtree intercepts pointer events
     - retrying click action
       - waiting 500ms

2025-07-26 23:52:27 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, Locator.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator("[role=\"button\"]:has-text(\"Post\")").first
    - locator resolved to <button role="button" tabindex="-1" type="button" aria-hidden="true" aria-label="New posts are available. Push the period key to go to the them." class="css-175oi2r r-l5o3uw r-sdzlij r-y3da5r r-1777fci r-cnw61z r-3pj75a r-1loqt21 r-o7ynqc r-6416eg r-1ny4l3l">…</button>
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <div data-testid="mask" class="css-175oi2r r-1p0dtai r-1d2f490 r-1xcajam r-zchlnj r-ipm5af r-1ffj0ar"></div> from <div id="layers" class="r-zchlnj r-1d2f490 r-u8s1d r-ipm5af">…</div> subtree intercepts pointer events
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <div data-testid="mask" class="css-175oi2r r-1p0dtai r-1d2f490 r-1xcajam r-zchlnj r-ipm5af r-1ffj0ar"></div> from <div id="layers" class="r-zchlnj r-1d2f490 r-u8s1d r-ipm5af">…</div> subtree intercepts pointer events
    - retrying click action
      - waiting 100ms
    57 × waiting for element to be visible, enabled and stable
       - element is visible, enabled and stable
       - scrolling into view if needed
       - done scrolling
       - <div data-testid="mask" class="css-175oi2r r-1p0dtai r-1d2f490 r-1xcajam r-zchlnj r-ipm5af r-1ffj0ar"></div> from <div id="layers" class="r-zchlnj r-1d2f490 r-u8s1d r-ipm5af">…</div> subtree intercepts pointer events
     - retrying click action
       - waiting 500ms

2025-07-28 06:03:08 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x9d1af3+62339]
	GetHandleVerifier [0x0x9d1b34+62404]
	(No symbol) [0x0x812123]
	(No symbol) [0x0x839693]
	(No symbol) [0x0x83ae20]
	(No symbol) [0x0x835f5a]
	(No symbol) [0x0x889782]
	(No symbol) [0x0x88926c]
	(No symbol) [0x0x88a960]
	(No symbol) [0x0x88a76a]
	(No symbol) [0x0x87f1b6]
	(No symbol) [0x0x84e7a2]
	(No symbol) [0x0x84f644]
	GetHandleVerifier [0x0xc46683+2637587]
	GetHandleVerifier [0x0xc41a8a+2618138]
	GetHandleVerifier [0x0x9f856a+220666]
	GetHandleVerifier [0x0x9e8998+156200]
	GetHandleVerifier [0x0x9ef12d+182717]
	GetHandleVerifier [0x0x9d9a38+94920]
	GetHandleVerifier [0x0x9d9bc2+95314]
	GetHandleVerifier [0x0x9c4d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 06:03:09 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x9d1af3+62339]
	GetHandleVerifier [0x0x9d1b34+62404]
	(No symbol) [0x0x812123]
	(No symbol) [0x0x839693]
	(No symbol) [0x0x83ae20]
	(No symbol) [0x0x835f5a]
	(No symbol) [0x0x889782]
	(No symbol) [0x0x88926c]
	(No symbol) [0x0x88a960]
	(No symbol) [0x0x88a76a]
	(No symbol) [0x0x87f1b6]
	(No symbol) [0x0x84e7a2]
	(No symbol) [0x0x84f644]
	GetHandleVerifier [0x0xc46683+2637587]
	GetHandleVerifier [0x0xc41a8a+2618138]
	GetHandleVerifier [0x0x9f856a+220666]
	GetHandleVerifier [0x0x9e8998+156200]
	GetHandleVerifier [0x0x9ef12d+182717]
	GetHandleVerifier [0x0x9d9a38+94920]
	GetHandleVerifier [0x0x9d9bc2+95314]
	GetHandleVerifier [0x0x9c4d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 06:03:09 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x9d1af3+62339]
	GetHandleVerifier [0x0x9d1b34+62404]
	(No symbol) [0x0x812123]
	(No symbol) [0x0x839693]
	(No symbol) [0x0x83ae20]
	(No symbol) [0x0x835f5a]
	(No symbol) [0x0x889782]
	(No symbol) [0x0x88926c]
	(No symbol) [0x0x88a960]
	(No symbol) [0x0x88a76a]
	(No symbol) [0x0x87f1b6]
	(No symbol) [0x0x84e7a2]
	(No symbol) [0x0x84f644]
	GetHandleVerifier [0x0xc46683+2637587]
	GetHandleVerifier [0x0xc41a8a+2618138]
	GetHandleVerifier [0x0x9f856a+220666]
	GetHandleVerifier [0x0x9e8998+156200]
	GetHandleVerifier [0x0x9ef12d+182717]
	GetHandleVerifier [0x0x9d9a38+94920]
	GetHandleVerifier [0x0x9d9bc2+95314]
	GetHandleVerifier [0x0x9c4d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 06:03:09 | ERROR    | src.core.browser_manager:get_driver:147 | 创建WebDriver最终失败: PaigeBaldw49027
2025-07-28 06:03:09 | ERROR    | src.modules.posting.executor:execute_post:368 | 发帖失败: PaigeBaldw49027, 获取WebDriver失败
2025-07-28 16:33:08 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0xef1af3+62339]
	GetHandleVerifier [0x0xef1b34+62404]
	(No symbol) [0x0xd32123]
	(No symbol) [0x0xd59693]
	(No symbol) [0x0xd5ae20]
	(No symbol) [0x0xd55f5a]
	(No symbol) [0x0xda9782]
	(No symbol) [0x0xda926c]
	(No symbol) [0x0xdaa960]
	(No symbol) [0x0xdaa76a]
	(No symbol) [0x0xd9f1b6]
	(No symbol) [0x0xd6e7a2]
	(No symbol) [0x0xd6f644]
	GetHandleVerifier [0x0x1166683+2637587]
	GetHandleVerifier [0x0x1161a8a+2618138]
	GetHandleVerifier [0x0xf1856a+220666]
	GetHandleVerifier [0x0xf08998+156200]
	GetHandleVerifier [0x0xf0f12d+182717]
	GetHandleVerifier [0x0xef9a38+94920]
	GetHandleVerifier [0x0xef9bc2+95314]
	GetHandleVerifier [0x0xee4d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:33:09 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0xef1af3+62339]
	GetHandleVerifier [0x0xef1b34+62404]
	(No symbol) [0x0xd32123]
	(No symbol) [0x0xd59693]
	(No symbol) [0x0xd5ae20]
	(No symbol) [0x0xd55f5a]
	(No symbol) [0x0xda9782]
	(No symbol) [0x0xda926c]
	(No symbol) [0x0xdaa960]
	(No symbol) [0x0xdaa76a]
	(No symbol) [0x0xd9f1b6]
	(No symbol) [0x0xd6e7a2]
	(No symbol) [0x0xd6f644]
	GetHandleVerifier [0x0x1166683+2637587]
	GetHandleVerifier [0x0x1161a8a+2618138]
	GetHandleVerifier [0x0xf1856a+220666]
	GetHandleVerifier [0x0xf08998+156200]
	GetHandleVerifier [0x0xf0f12d+182717]
	GetHandleVerifier [0x0xef9a38+94920]
	GetHandleVerifier [0x0xef9bc2+95314]
	GetHandleVerifier [0x0xee4d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:33:09 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0xef1af3+62339]
	GetHandleVerifier [0x0xef1b34+62404]
	(No symbol) [0x0xd32123]
	(No symbol) [0x0xd59693]
	(No symbol) [0x0xd5ae20]
	(No symbol) [0x0xd55f5a]
	(No symbol) [0x0xda9782]
	(No symbol) [0x0xda926c]
	(No symbol) [0x0xdaa960]
	(No symbol) [0x0xdaa76a]
	(No symbol) [0x0xd9f1b6]
	(No symbol) [0x0xd6e7a2]
	(No symbol) [0x0xd6f644]
	GetHandleVerifier [0x0x1166683+2637587]
	GetHandleVerifier [0x0x1161a8a+2618138]
	GetHandleVerifier [0x0xf1856a+220666]
	GetHandleVerifier [0x0xf08998+156200]
	GetHandleVerifier [0x0xf0f12d+182717]
	GetHandleVerifier [0x0xef9a38+94920]
	GetHandleVerifier [0x0xef9bc2+95314]
	GetHandleVerifier [0x0xee4d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:33:09 | ERROR    | src.core.browser_manager:get_driver:147 | 创建WebDriver最终失败: PaigeBaldw49027
2025-07-28 16:33:09 | ERROR    | src.modules.posting.executor:execute_post:368 | 发帖失败: PaigeBaldw49027, 获取WebDriver失败
2025-07-28 16:38:31 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0xa81af3+62339]
	GetHandleVerifier [0x0xa81b34+62404]
	(No symbol) [0x0x8c2123]
	(No symbol) [0x0x8e9693]
	(No symbol) [0x0x8eae20]
	(No symbol) [0x0x8e5f5a]
	(No symbol) [0x0x939782]
	(No symbol) [0x0x93926c]
	(No symbol) [0x0x93a960]
	(No symbol) [0x0x93a76a]
	(No symbol) [0x0x92f1b6]
	(No symbol) [0x0x8fe7a2]
	(No symbol) [0x0x8ff644]
	GetHandleVerifier [0x0xcf6683+2637587]
	GetHandleVerifier [0x0xcf1a8a+2618138]
	GetHandleVerifier [0x0xaa856a+220666]
	GetHandleVerifier [0x0xa98998+156200]
	GetHandleVerifier [0x0xa9f12d+182717]
	GetHandleVerifier [0x0xa89a38+94920]
	GetHandleVerifier [0x0xa89bc2+95314]
	GetHandleVerifier [0x0xa74d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:38:31 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0xa81af3+62339]
	GetHandleVerifier [0x0xa81b34+62404]
	(No symbol) [0x0x8c2123]
	(No symbol) [0x0x8e9693]
	(No symbol) [0x0x8eae20]
	(No symbol) [0x0x8e5f5a]
	(No symbol) [0x0x939782]
	(No symbol) [0x0x93926c]
	(No symbol) [0x0x93a960]
	(No symbol) [0x0x93a76a]
	(No symbol) [0x0x92f1b6]
	(No symbol) [0x0x8fe7a2]
	(No symbol) [0x0x8ff644]
	GetHandleVerifier [0x0xcf6683+2637587]
	GetHandleVerifier [0x0xcf1a8a+2618138]
	GetHandleVerifier [0x0xaa856a+220666]
	GetHandleVerifier [0x0xa98998+156200]
	GetHandleVerifier [0x0xa9f12d+182717]
	GetHandleVerifier [0x0xa89a38+94920]
	GetHandleVerifier [0x0xa89bc2+95314]
	GetHandleVerifier [0x0xa74d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:38:32 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0xa81af3+62339]
	GetHandleVerifier [0x0xa81b34+62404]
	(No symbol) [0x0x8c2123]
	(No symbol) [0x0x8e9693]
	(No symbol) [0x0x8eae20]
	(No symbol) [0x0x8e5f5a]
	(No symbol) [0x0x939782]
	(No symbol) [0x0x93926c]
	(No symbol) [0x0x93a960]
	(No symbol) [0x0x93a76a]
	(No symbol) [0x0x92f1b6]
	(No symbol) [0x0x8fe7a2]
	(No symbol) [0x0x8ff644]
	GetHandleVerifier [0x0xcf6683+2637587]
	GetHandleVerifier [0x0xcf1a8a+2618138]
	GetHandleVerifier [0x0xaa856a+220666]
	GetHandleVerifier [0x0xa98998+156200]
	GetHandleVerifier [0x0xa9f12d+182717]
	GetHandleVerifier [0x0xa89a38+94920]
	GetHandleVerifier [0x0xa89bc2+95314]
	GetHandleVerifier [0x0xa74d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:38:32 | ERROR    | src.core.browser_manager:get_driver:147 | 创建WebDriver最终失败: PaigeBaldw49027
2025-07-28 16:38:32 | ERROR    | src.modules.posting.executor:execute_post:368 | 发帖失败: PaigeBaldw49027, 获取WebDriver失败
2025-07-28 16:38:52 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x551af3+62339]
	GetHandleVerifier [0x0x551b34+62404]
	(No symbol) [0x0x392123]
	(No symbol) [0x0x3b9693]
	(No symbol) [0x0x3bae20]
	(No symbol) [0x0x3b5f5a]
	(No symbol) [0x0x409782]
	(No symbol) [0x0x40926c]
	(No symbol) [0x0x40a960]
	(No symbol) [0x0x40a76a]
	(No symbol) [0x0x3ff1b6]
	(No symbol) [0x0x3ce7a2]
	(No symbol) [0x0x3cf644]
	GetHandleVerifier [0x0x7c6683+2637587]
	GetHandleVerifier [0x0x7c1a8a+2618138]
	GetHandleVerifier [0x0x57856a+220666]
	GetHandleVerifier [0x0x568998+156200]
	GetHandleVerifier [0x0x56f12d+182717]
	GetHandleVerifier [0x0x559a38+94920]
	GetHandleVerifier [0x0x559bc2+95314]
	GetHandleVerifier [0x0x544d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:38:53 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x551af3+62339]
	GetHandleVerifier [0x0x551b34+62404]
	(No symbol) [0x0x392123]
	(No symbol) [0x0x3b9693]
	(No symbol) [0x0x3bae20]
	(No symbol) [0x0x3b5f5a]
	(No symbol) [0x0x409782]
	(No symbol) [0x0x40926c]
	(No symbol) [0x0x40a960]
	(No symbol) [0x0x40a76a]
	(No symbol) [0x0x3ff1b6]
	(No symbol) [0x0x3ce7a2]
	(No symbol) [0x0x3cf644]
	GetHandleVerifier [0x0x7c6683+2637587]
	GetHandleVerifier [0x0x7c1a8a+2618138]
	GetHandleVerifier [0x0x57856a+220666]
	GetHandleVerifier [0x0x568998+156200]
	GetHandleVerifier [0x0x56f12d+182717]
	GetHandleVerifier [0x0x559a38+94920]
	GetHandleVerifier [0x0x559bc2+95314]
	GetHandleVerifier [0x0x544d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:38:53 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x551af3+62339]
	GetHandleVerifier [0x0x551b34+62404]
	(No symbol) [0x0x392123]
	(No symbol) [0x0x3b9693]
	(No symbol) [0x0x3bae20]
	(No symbol) [0x0x3b5f5a]
	(No symbol) [0x0x409782]
	(No symbol) [0x0x40926c]
	(No symbol) [0x0x40a960]
	(No symbol) [0x0x40a76a]
	(No symbol) [0x0x3ff1b6]
	(No symbol) [0x0x3ce7a2]
	(No symbol) [0x0x3cf644]
	GetHandleVerifier [0x0x7c6683+2637587]
	GetHandleVerifier [0x0x7c1a8a+2618138]
	GetHandleVerifier [0x0x57856a+220666]
	GetHandleVerifier [0x0x568998+156200]
	GetHandleVerifier [0x0x56f12d+182717]
	GetHandleVerifier [0x0x559a38+94920]
	GetHandleVerifier [0x0x559bc2+95314]
	GetHandleVerifier [0x0x544d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:38:53 | ERROR    | src.core.browser_manager:get_driver:147 | 创建WebDriver最终失败: CharleneCa2057
2025-07-28 16:38:53 | ERROR    | src.modules.posting.executor:execute_post:368 | 发帖失败: CharleneCa2057, 获取WebDriver失败
2025-07-28 16:47:34 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x561af3+62339]
	GetHandleVerifier [0x0x561b34+62404]
	(No symbol) [0x0x3a2123]
	(No symbol) [0x0x3c9693]
	(No symbol) [0x0x3cae20]
	(No symbol) [0x0x3c5f5a]
	(No symbol) [0x0x419782]
	(No symbol) [0x0x41926c]
	(No symbol) [0x0x41a960]
	(No symbol) [0x0x41a76a]
	(No symbol) [0x0x40f1b6]
	(No symbol) [0x0x3de7a2]
	(No symbol) [0x0x3df644]
	GetHandleVerifier [0x0x7d6683+2637587]
	GetHandleVerifier [0x0x7d1a8a+2618138]
	GetHandleVerifier [0x0x58856a+220666]
	GetHandleVerifier [0x0x578998+156200]
	GetHandleVerifier [0x0x57f12d+182717]
	GetHandleVerifier [0x0x569a38+94920]
	GetHandleVerifier [0x0x569bc2+95314]
	GetHandleVerifier [0x0x554d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:47:34 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x561af3+62339]
	GetHandleVerifier [0x0x561b34+62404]
	(No symbol) [0x0x3a2123]
	(No symbol) [0x0x3c9693]
	(No symbol) [0x0x3cae20]
	(No symbol) [0x0x3c5f5a]
	(No symbol) [0x0x419782]
	(No symbol) [0x0x41926c]
	(No symbol) [0x0x41a960]
	(No symbol) [0x0x41a76a]
	(No symbol) [0x0x40f1b6]
	(No symbol) [0x0x3de7a2]
	(No symbol) [0x0x3df644]
	GetHandleVerifier [0x0x7d6683+2637587]
	GetHandleVerifier [0x0x7d1a8a+2618138]
	GetHandleVerifier [0x0x58856a+220666]
	GetHandleVerifier [0x0x578998+156200]
	GetHandleVerifier [0x0x57f12d+182717]
	GetHandleVerifier [0x0x569a38+94920]
	GetHandleVerifier [0x0x569bc2+95314]
	GetHandleVerifier [0x0x554d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:47:34 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x561af3+62339]
	GetHandleVerifier [0x0x561b34+62404]
	(No symbol) [0x0x3a2123]
	(No symbol) [0x0x3c9693]
	(No symbol) [0x0x3cae20]
	(No symbol) [0x0x3c5f5a]
	(No symbol) [0x0x419782]
	(No symbol) [0x0x41926c]
	(No symbol) [0x0x41a960]
	(No symbol) [0x0x41a76a]
	(No symbol) [0x0x40f1b6]
	(No symbol) [0x0x3de7a2]
	(No symbol) [0x0x3df644]
	GetHandleVerifier [0x0x7d6683+2637587]
	GetHandleVerifier [0x0x7d1a8a+2618138]
	GetHandleVerifier [0x0x58856a+220666]
	GetHandleVerifier [0x0x578998+156200]
	GetHandleVerifier [0x0x57f12d+182717]
	GetHandleVerifier [0x0x569a38+94920]
	GetHandleVerifier [0x0x569bc2+95314]
	GetHandleVerifier [0x0x554d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:47:34 | ERROR    | src.core.browser_manager:get_driver:147 | 创建WebDriver最终失败: EWebb32471
2025-07-28 16:47:34 | ERROR    | src.modules.posting.executor:login_account:193 | 登录异常: EWebb32471 (尝试 1/3), 无法获取WebDriver
2025-07-28 16:47:44 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x561af3+62339]
	GetHandleVerifier [0x0x561b34+62404]
	(No symbol) [0x0x3a2123]
	(No symbol) [0x0x3c9693]
	(No symbol) [0x0x3cae20]
	(No symbol) [0x0x3c5f5a]
	(No symbol) [0x0x419782]
	(No symbol) [0x0x41926c]
	(No symbol) [0x0x41a960]
	(No symbol) [0x0x41a76a]
	(No symbol) [0x0x40f1b6]
	(No symbol) [0x0x3de7a2]
	(No symbol) [0x0x3df644]
	GetHandleVerifier [0x0x7d6683+2637587]
	GetHandleVerifier [0x0x7d1a8a+2618138]
	GetHandleVerifier [0x0x58856a+220666]
	GetHandleVerifier [0x0x578998+156200]
	GetHandleVerifier [0x0x57f12d+182717]
	GetHandleVerifier [0x0x569a38+94920]
	GetHandleVerifier [0x0x569bc2+95314]
	GetHandleVerifier [0x0x554d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:47:44 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x561af3+62339]
	GetHandleVerifier [0x0x561b34+62404]
	(No symbol) [0x0x3a2123]
	(No symbol) [0x0x3c9693]
	(No symbol) [0x0x3cae20]
	(No symbol) [0x0x3c5f5a]
	(No symbol) [0x0x419782]
	(No symbol) [0x0x41926c]
	(No symbol) [0x0x41a960]
	(No symbol) [0x0x41a76a]
	(No symbol) [0x0x40f1b6]
	(No symbol) [0x0x3de7a2]
	(No symbol) [0x0x3df644]
	GetHandleVerifier [0x0x7d6683+2637587]
	GetHandleVerifier [0x0x7d1a8a+2618138]
	GetHandleVerifier [0x0x58856a+220666]
	GetHandleVerifier [0x0x578998+156200]
	GetHandleVerifier [0x0x57f12d+182717]
	GetHandleVerifier [0x0x569a38+94920]
	GetHandleVerifier [0x0x569bc2+95314]
	GetHandleVerifier [0x0x554d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:47:45 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x561af3+62339]
	GetHandleVerifier [0x0x561b34+62404]
	(No symbol) [0x0x3a2123]
	(No symbol) [0x0x3c9693]
	(No symbol) [0x0x3cae20]
	(No symbol) [0x0x3c5f5a]
	(No symbol) [0x0x419782]
	(No symbol) [0x0x41926c]
	(No symbol) [0x0x41a960]
	(No symbol) [0x0x41a76a]
	(No symbol) [0x0x40f1b6]
	(No symbol) [0x0x3de7a2]
	(No symbol) [0x0x3df644]
	GetHandleVerifier [0x0x7d6683+2637587]
	GetHandleVerifier [0x0x7d1a8a+2618138]
	GetHandleVerifier [0x0x58856a+220666]
	GetHandleVerifier [0x0x578998+156200]
	GetHandleVerifier [0x0x57f12d+182717]
	GetHandleVerifier [0x0x569a38+94920]
	GetHandleVerifier [0x0x569bc2+95314]
	GetHandleVerifier [0x0x554d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:47:45 | ERROR    | src.core.browser_manager:get_driver:147 | 创建WebDriver最终失败: EWebb32471
2025-07-28 16:47:45 | ERROR    | src.modules.posting.executor:login_account:193 | 登录异常: EWebb32471 (尝试 2/3), 无法获取WebDriver
2025-07-28 16:47:55 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x561af3+62339]
	GetHandleVerifier [0x0x561b34+62404]
	(No symbol) [0x0x3a2123]
	(No symbol) [0x0x3c9693]
	(No symbol) [0x0x3cae20]
	(No symbol) [0x0x3c5f5a]
	(No symbol) [0x0x419782]
	(No symbol) [0x0x41926c]
	(No symbol) [0x0x41a960]
	(No symbol) [0x0x41a76a]
	(No symbol) [0x0x40f1b6]
	(No symbol) [0x0x3de7a2]
	(No symbol) [0x0x3df644]
	GetHandleVerifier [0x0x7d6683+2637587]
	GetHandleVerifier [0x0x7d1a8a+2618138]
	GetHandleVerifier [0x0x58856a+220666]
	GetHandleVerifier [0x0x578998+156200]
	GetHandleVerifier [0x0x57f12d+182717]
	GetHandleVerifier [0x0x569a38+94920]
	GetHandleVerifier [0x0x569bc2+95314]
	GetHandleVerifier [0x0x554d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:47:55 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x561af3+62339]
	GetHandleVerifier [0x0x561b34+62404]
	(No symbol) [0x0x3a2123]
	(No symbol) [0x0x3c9693]
	(No symbol) [0x0x3cae20]
	(No symbol) [0x0x3c5f5a]
	(No symbol) [0x0x419782]
	(No symbol) [0x0x41926c]
	(No symbol) [0x0x41a960]
	(No symbol) [0x0x41a76a]
	(No symbol) [0x0x40f1b6]
	(No symbol) [0x0x3de7a2]
	(No symbol) [0x0x3df644]
	GetHandleVerifier [0x0x7d6683+2637587]
	GetHandleVerifier [0x0x7d1a8a+2618138]
	GetHandleVerifier [0x0x58856a+220666]
	GetHandleVerifier [0x0x578998+156200]
	GetHandleVerifier [0x0x57f12d+182717]
	GetHandleVerifier [0x0x569a38+94920]
	GetHandleVerifier [0x0x569bc2+95314]
	GetHandleVerifier [0x0x554d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:47:55 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x561af3+62339]
	GetHandleVerifier [0x0x561b34+62404]
	(No symbol) [0x0x3a2123]
	(No symbol) [0x0x3c9693]
	(No symbol) [0x0x3cae20]
	(No symbol) [0x0x3c5f5a]
	(No symbol) [0x0x419782]
	(No symbol) [0x0x41926c]
	(No symbol) [0x0x41a960]
	(No symbol) [0x0x41a76a]
	(No symbol) [0x0x40f1b6]
	(No symbol) [0x0x3de7a2]
	(No symbol) [0x0x3df644]
	GetHandleVerifier [0x0x7d6683+2637587]
	GetHandleVerifier [0x0x7d1a8a+2618138]
	GetHandleVerifier [0x0x58856a+220666]
	GetHandleVerifier [0x0x578998+156200]
	GetHandleVerifier [0x0x57f12d+182717]
	GetHandleVerifier [0x0x569a38+94920]
	GetHandleVerifier [0x0x569bc2+95314]
	GetHandleVerifier [0x0x554d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:47:55 | ERROR    | src.core.browser_manager:get_driver:147 | 创建WebDriver最终失败: EWebb32471
2025-07-28 16:47:55 | ERROR    | src.modules.posting.executor:login_account:193 | 登录异常: EWebb32471 (尝试 3/3), 无法获取WebDriver
2025-07-28 16:53:58 | ERROR    | src.core.browser_manager:create_driver:251 | 创建Chrome实例失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: useAutomationExtension
Stacktrace:
	GetHandleVerifier [0x0x411af3+62339]
	GetHandleVerifier [0x0x411b34+62404]
	(No symbol) [0x0x252123]
	(No symbol) [0x0x279693]
	(No symbol) [0x0x27ae20]
	(No symbol) [0x0x275f5a]
	(No symbol) [0x0x2c9782]
	(No symbol) [0x0x2c926c]
	(No symbol) [0x0x2ca960]
	(No symbol) [0x0x2ca76a]
	(No symbol) [0x0x2bf1b6]
	(No symbol) [0x0x28e7a2]
	(No symbol) [0x0x28f644]
	GetHandleVerifier [0x0x686683+2637587]
	GetHandleVerifier [0x0x681a8a+2618138]
	GetHandleVerifier [0x0x43856a+220666]
	GetHandleVerifier [0x0x428998+156200]
	GetHandleVerifier [0x0x42f12d+182717]
	GetHandleVerifier [0x0x419a38+94920]
	GetHandleVerifier [0x0x419bc2+95314]
	GetHandleVerifier [0x0x404d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:53:58 | ERROR    | src.core.browser_manager:_create_driver:278 | 创建WebDriver失败: you cannot reuse the ChromeOptions object
2025-07-28 16:53:59 | ERROR    | src.core.browser_manager:create_driver:251 | 创建Chrome实例失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: useAutomationExtension
Stacktrace:
	GetHandleVerifier [0x0x411af3+62339]
	GetHandleVerifier [0x0x411b34+62404]
	(No symbol) [0x0x252123]
	(No symbol) [0x0x279693]
	(No symbol) [0x0x27ae20]
	(No symbol) [0x0x275f5a]
	(No symbol) [0x0x2c9782]
	(No symbol) [0x0x2c926c]
	(No symbol) [0x0x2ca960]
	(No symbol) [0x0x2ca76a]
	(No symbol) [0x0x2bf1b6]
	(No symbol) [0x0x28e7a2]
	(No symbol) [0x0x28f644]
	GetHandleVerifier [0x0x686683+2637587]
	GetHandleVerifier [0x0x681a8a+2618138]
	GetHandleVerifier [0x0x43856a+220666]
	GetHandleVerifier [0x0x428998+156200]
	GetHandleVerifier [0x0x42f12d+182717]
	GetHandleVerifier [0x0x419a38+94920]
	GetHandleVerifier [0x0x419bc2+95314]
	GetHandleVerifier [0x0x404d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:53:59 | ERROR    | src.core.browser_manager:_create_driver:278 | 创建WebDriver失败: you cannot reuse the ChromeOptions object
2025-07-28 16:53:59 | ERROR    | src.core.browser_manager:create_driver:251 | 创建Chrome实例失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: useAutomationExtension
Stacktrace:
	GetHandleVerifier [0x0x411af3+62339]
	GetHandleVerifier [0x0x411b34+62404]
	(No symbol) [0x0x252123]
	(No symbol) [0x0x279693]
	(No symbol) [0x0x27ae20]
	(No symbol) [0x0x275f5a]
	(No symbol) [0x0x2c9782]
	(No symbol) [0x0x2c926c]
	(No symbol) [0x0x2ca960]
	(No symbol) [0x0x2ca76a]
	(No symbol) [0x0x2bf1b6]
	(No symbol) [0x0x28e7a2]
	(No symbol) [0x0x28f644]
	GetHandleVerifier [0x0x686683+2637587]
	GetHandleVerifier [0x0x681a8a+2618138]
	GetHandleVerifier [0x0x43856a+220666]
	GetHandleVerifier [0x0x428998+156200]
	GetHandleVerifier [0x0x42f12d+182717]
	GetHandleVerifier [0x0x419a38+94920]
	GetHandleVerifier [0x0x419bc2+95314]
	GetHandleVerifier [0x0x404d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:53:59 | ERROR    | src.core.browser_manager:_create_driver:278 | 创建WebDriver失败: you cannot reuse the ChromeOptions object
2025-07-28 16:53:59 | ERROR    | src.core.browser_manager:get_driver:147 | 创建WebDriver最终失败: DavidMarti65523
2025-07-28 16:53:59 | ERROR    | src.modules.posting.executor:login_account:193 | 登录异常: DavidMarti65523 (尝试 1/3), 无法获取WebDriver
2025-07-28 16:54:04 | ERROR    | src.modules.posting.executor:login_account:193 | 登录异常: EWebb32471 (尝试 1/3), Task <Task pending name='Task-2' coro=<AccountLoginThread.run.<locals>.login_accounts() running at D:\yaoya\Documents\chonggou\yyy1 - 副本\src\ui\widgets\account_widget.py:130> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-07-28 16:54:05 | ERROR    | src.core.browser_manager:create_driver:251 | 创建Chrome实例失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: useAutomationExtension
Stacktrace:
	GetHandleVerifier [0x0x411af3+62339]
	GetHandleVerifier [0x0x411b34+62404]
	(No symbol) [0x0x252123]
	(No symbol) [0x0x279693]
	(No symbol) [0x0x27ae20]
	(No symbol) [0x0x275f5a]
	(No symbol) [0x0x2c9782]
	(No symbol) [0x0x2c926c]
	(No symbol) [0x0x2ca960]
	(No symbol) [0x0x2ca76a]
	(No symbol) [0x0x2bf1b6]
	(No symbol) [0x0x28e7a2]
	(No symbol) [0x0x28f644]
	GetHandleVerifier [0x0x686683+2637587]
	GetHandleVerifier [0x0x681a8a+2618138]
	GetHandleVerifier [0x0x43856a+220666]
	GetHandleVerifier [0x0x428998+156200]
	GetHandleVerifier [0x0x42f12d+182717]
	GetHandleVerifier [0x0x419a38+94920]
	GetHandleVerifier [0x0x419bc2+95314]
	GetHandleVerifier [0x0x404d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:54:05 | ERROR    | src.core.browser_manager:_create_driver:278 | 创建WebDriver失败: you cannot reuse the ChromeOptions object
2025-07-28 16:54:06 | ERROR    | src.core.browser_manager:create_driver:251 | 创建Chrome实例失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: useAutomationExtension
Stacktrace:
	GetHandleVerifier [0x0x411af3+62339]
	GetHandleVerifier [0x0x411b34+62404]
	(No symbol) [0x0x252123]
	(No symbol) [0x0x279693]
	(No symbol) [0x0x27ae20]
	(No symbol) [0x0x275f5a]
	(No symbol) [0x0x2c9782]
	(No symbol) [0x0x2c926c]
	(No symbol) [0x0x2ca960]
	(No symbol) [0x0x2ca76a]
	(No symbol) [0x0x2bf1b6]
	(No symbol) [0x0x28e7a2]
	(No symbol) [0x0x28f644]
	GetHandleVerifier [0x0x686683+2637587]
	GetHandleVerifier [0x0x681a8a+2618138]
	GetHandleVerifier [0x0x43856a+220666]
	GetHandleVerifier [0x0x428998+156200]
	GetHandleVerifier [0x0x42f12d+182717]
	GetHandleVerifier [0x0x419a38+94920]
	GetHandleVerifier [0x0x419bc2+95314]
	GetHandleVerifier [0x0x404d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:54:06 | ERROR    | src.core.browser_manager:_create_driver:278 | 创建WebDriver失败: you cannot reuse the ChromeOptions object
2025-07-28 16:54:07 | ERROR    | src.core.browser_manager:create_driver:251 | 创建Chrome实例失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: useAutomationExtension
Stacktrace:
	GetHandleVerifier [0x0x411af3+62339]
	GetHandleVerifier [0x0x411b34+62404]
	(No symbol) [0x0x252123]
	(No symbol) [0x0x279693]
	(No symbol) [0x0x27ae20]
	(No symbol) [0x0x275f5a]
	(No symbol) [0x0x2c9782]
	(No symbol) [0x0x2c926c]
	(No symbol) [0x0x2ca960]
	(No symbol) [0x0x2ca76a]
	(No symbol) [0x0x2bf1b6]
	(No symbol) [0x0x28e7a2]
	(No symbol) [0x0x28f644]
	GetHandleVerifier [0x0x686683+2637587]
	GetHandleVerifier [0x0x681a8a+2618138]
	GetHandleVerifier [0x0x43856a+220666]
	GetHandleVerifier [0x0x428998+156200]
	GetHandleVerifier [0x0x42f12d+182717]
	GetHandleVerifier [0x0x419a38+94920]
	GetHandleVerifier [0x0x419bc2+95314]
	GetHandleVerifier [0x0x404d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:54:07 | ERROR    | src.core.browser_manager:_create_driver:278 | 创建WebDriver失败: you cannot reuse the ChromeOptions object
2025-07-28 16:54:07 | ERROR    | src.core.browser_manager:get_driver:147 | 创建WebDriver最终失败: DavidMarti65523
2025-07-28 16:54:07 | ERROR    | src.modules.posting.executor:login_account:193 | 登录异常: DavidMarti65523 (尝试 2/3), 无法获取WebDriver
2025-07-28 16:54:12 | ERROR    | src.modules.posting.executor:login_account:193 | 登录异常: EWebb32471 (尝试 2/3), Task <Task pending name='Task-2' coro=<AccountLoginThread.run.<locals>.login_accounts() running at D:\yaoya\Documents\chonggou\yyy1 - 副本\src\ui\widgets\account_widget.py:130> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-07-28 16:54:14 | ERROR    | src.core.browser_manager:create_driver:251 | 创建Chrome实例失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: useAutomationExtension
Stacktrace:
	GetHandleVerifier [0x0x411af3+62339]
	GetHandleVerifier [0x0x411b34+62404]
	(No symbol) [0x0x252123]
	(No symbol) [0x0x279693]
	(No symbol) [0x0x27ae20]
	(No symbol) [0x0x275f5a]
	(No symbol) [0x0x2c9782]
	(No symbol) [0x0x2c926c]
	(No symbol) [0x0x2ca960]
	(No symbol) [0x0x2ca76a]
	(No symbol) [0x0x2bf1b6]
	(No symbol) [0x0x28e7a2]
	(No symbol) [0x0x28f644]
	GetHandleVerifier [0x0x686683+2637587]
	GetHandleVerifier [0x0x681a8a+2618138]
	GetHandleVerifier [0x0x43856a+220666]
	GetHandleVerifier [0x0x428998+156200]
	GetHandleVerifier [0x0x42f12d+182717]
	GetHandleVerifier [0x0x419a38+94920]
	GetHandleVerifier [0x0x419bc2+95314]
	GetHandleVerifier [0x0x404d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:54:14 | ERROR    | src.core.browser_manager:_create_driver:278 | 创建WebDriver失败: you cannot reuse the ChromeOptions object
2025-07-28 16:54:15 | ERROR    | src.core.browser_manager:create_driver:251 | 创建Chrome实例失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: useAutomationExtension
Stacktrace:
	GetHandleVerifier [0x0x411af3+62339]
	GetHandleVerifier [0x0x411b34+62404]
	(No symbol) [0x0x252123]
	(No symbol) [0x0x279693]
	(No symbol) [0x0x27ae20]
	(No symbol) [0x0x275f5a]
	(No symbol) [0x0x2c9782]
	(No symbol) [0x0x2c926c]
	(No symbol) [0x0x2ca960]
	(No symbol) [0x0x2ca76a]
	(No symbol) [0x0x2bf1b6]
	(No symbol) [0x0x28e7a2]
	(No symbol) [0x0x28f644]
	GetHandleVerifier [0x0x686683+2637587]
	GetHandleVerifier [0x0x681a8a+2618138]
	GetHandleVerifier [0x0x43856a+220666]
	GetHandleVerifier [0x0x428998+156200]
	GetHandleVerifier [0x0x42f12d+182717]
	GetHandleVerifier [0x0x419a38+94920]
	GetHandleVerifier [0x0x419bc2+95314]
	GetHandleVerifier [0x0x404d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:54:15 | ERROR    | src.core.browser_manager:_create_driver:278 | 创建WebDriver失败: you cannot reuse the ChromeOptions object
2025-07-28 16:54:16 | ERROR    | src.core.browser_manager:create_driver:251 | 创建Chrome实例失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: useAutomationExtension
Stacktrace:
	GetHandleVerifier [0x0x411af3+62339]
	GetHandleVerifier [0x0x411b34+62404]
	(No symbol) [0x0x252123]
	(No symbol) [0x0x279693]
	(No symbol) [0x0x27ae20]
	(No symbol) [0x0x275f5a]
	(No symbol) [0x0x2c9782]
	(No symbol) [0x0x2c926c]
	(No symbol) [0x0x2ca960]
	(No symbol) [0x0x2ca76a]
	(No symbol) [0x0x2bf1b6]
	(No symbol) [0x0x28e7a2]
	(No symbol) [0x0x28f644]
	GetHandleVerifier [0x0x686683+2637587]
	GetHandleVerifier [0x0x681a8a+2618138]
	GetHandleVerifier [0x0x43856a+220666]
	GetHandleVerifier [0x0x428998+156200]
	GetHandleVerifier [0x0x42f12d+182717]
	GetHandleVerifier [0x0x419a38+94920]
	GetHandleVerifier [0x0x419bc2+95314]
	GetHandleVerifier [0x0x404d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:54:16 | ERROR    | src.core.browser_manager:_create_driver:278 | 创建WebDriver失败: you cannot reuse the ChromeOptions object
2025-07-28 16:54:16 | ERROR    | src.core.browser_manager:get_driver:147 | 创建WebDriver最终失败: DavidMarti65523
2025-07-28 16:54:16 | ERROR    | src.modules.posting.executor:login_account:193 | 登录异常: DavidMarti65523 (尝试 3/3), 无法获取WebDriver
2025-07-28 16:54:20 | ERROR    | src.modules.posting.executor:login_account:193 | 登录异常: EWebb32471 (尝试 3/3), Task <Task pending name='Task-2' coro=<AccountLoginThread.run.<locals>.login_accounts() running at D:\yaoya\Documents\chonggou\yyy1 - 副本\src\ui\widgets\account_widget.py:130> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-07-28 17:00:05 | ERROR    | src.core.browser_manager:create_driver:275 | 创建Chrome实例失败: Message: session not created: cannot connect to chrome at 127.0.0.1:34566
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x10f1af3+62339]
	GetHandleVerifier [0x0x10f1b34+62404]
	(No symbol) [0x0xf31f80]
	(No symbol) [0x0xf25f2a]
	(No symbol) [0x0xf6adc6]
	(No symbol) [0x0xf6128f]
	(No symbol) [0x0xf610c6]
	(No symbol) [0x0xfaae77]
	(No symbol) [0x0xfaa76a]
	(No symbol) [0x0xf9f1b6]
	(No symbol) [0x0xf6e7a2]
	(No symbol) [0x0xf6f644]
	GetHandleVerifier [0x0x1366683+2637587]
	GetHandleVerifier [0x0x1361a8a+2618138]
	GetHandleVerifier [0x0x111856a+220666]
	GetHandleVerifier [0x0x1108998+156200]
	GetHandleVerifier [0x0x110f12d+182717]
	GetHandleVerifier [0x0x10f9a38+94920]
	GetHandleVerifier [0x0x10f9bc2+95314]
	GetHandleVerifier [0x0x10e4d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 17:00:05 | ERROR    | src.core.browser_manager:_create_driver:301 | 创建WebDriver失败: Message: session not created: cannot connect to chrome at 127.0.0.1:34566
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x10f1af3+62339]
	GetHandleVerifier [0x0x10f1b34+62404]
	(No symbol) [0x0xf31f80]
	(No symbol) [0x0xf25f2a]
	(No symbol) [0x0xf6adc6]
	(No symbol) [0x0xf6128f]
	(No symbol) [0x0xf610c6]
	(No symbol) [0x0xfaae77]
	(No symbol) [0x0xfaa76a]
	(No symbol) [0x0xf9f1b6]
	(No symbol) [0x0xf6e7a2]
	(No symbol) [0x0xf6f644]
	GetHandleVerifier [0x0x1366683+2637587]
	GetHandleVerifier [0x0x1361a8a+2618138]
	GetHandleVerifier [0x0x111856a+220666]
	GetHandleVerifier [0x0x1108998+156200]
	GetHandleVerifier [0x0x110f12d+182717]
	GetHandleVerifier [0x0x10f9a38+94920]
	GetHandleVerifier [0x0x10f9bc2+95314]
	GetHandleVerifier [0x0x10e4d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 17:34:56 | ERROR    | src.modules.posting.executor:login_account:193 | 登录异常: EWebb32471 (尝试 1/3), 无法找到下一步按钮
2025-07-28 17:35:11 | ERROR    | src.modules.posting.executor:login_account:193 | 登录异常: EWebb32471 (尝试 2/3), Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x1041af3+62339]
	GetHandleVerifier [0x0x1041b34+62404]
	(No symbol) [0x0xe81f80]
	(No symbol) [0x0xebda98]
	(No symbol) [0x0xeef276]
	(No symbol) [0x0xeeae75]
	(No symbol) [0x0xeea3f6]
	(No symbol) [0x0xe53a45]
	(No symbol) [0x0xe53f9e]
	(No symbol) [0x0xe5442d]
	GetHandleVerifier [0x0x12b6683+2637587]
	GetHandleVerifier [0x0x12b1a8a+2618138]
	GetHandleVerifier [0x0x106856a+220666]
	GetHandleVerifier [0x0x1058998+156200]
	GetHandleVerifier [0x0x105f12d+182717]
	(No symbol) [0x0xe53710]
	(No symbol) [0x0xe52f1d]
	GetHandleVerifier [0x0x13ea71c+3899308]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 17:35:59 | ERROR    | src.modules.posting.executor:login_account:193 | 登录异常: EWebb32471 (尝试 3/3), 无法找到下一步按钮
2025-07-28 17:44:26 | ERROR    | src.modules.posting.executor:login_account:193 | 登录异常: EWebb32471 (尝试 1/3), 无法找到密码输入框
2025-07-28 17:44:43 | ERROR    | src.modules.posting.executor:login_account:193 | 登录异常: EWebb32471 (尝试 2/3), Message: Driver已关闭

2025-07-28 17:50:35 | ERROR    | src.modules.posting.executor:login_account:251 | 登录异常: EWebb32471 (尝试 1/3), 无法找到密码输入框
2025-07-28 17:55:33 | ERROR    | src.modules.posting.executor:login_account:251 | 登录异常: EWebb32471 (尝试 2/3), 无法找到密码输入框
2025-07-28 17:56:47 | ERROR    | src.modules.posting.executor:login_account:251 | 登录异常: EWebb32471 (尝试 3/3), 无法找到密码输入框
2025-07-28 19:23:52 | ERROR    | src.core.browser_manager:create_driver:451 | 创建Chrome实例最终失败: you cannot reuse the ChromeOptions object
2025-07-28 19:23:52 | ERROR    | src.core.browser_manager:_create_driver:477 | 创建WebDriver失败: you cannot reuse the ChromeOptions object
2025-07-28 19:25:42 | ERROR    | src.core.browser_manager:create_driver:451 | 创建Chrome实例最终失败: you cannot reuse the ChromeOptions object
2025-07-28 19:25:42 | ERROR    | src.core.browser_manager:_create_driver:477 | 创建WebDriver失败: you cannot reuse the ChromeOptions object
2025-07-28 19:27:26 | ERROR    | src.core.browser_manager:create_driver:451 | 创建Chrome实例最终失败: you cannot reuse the ChromeOptions object
2025-07-28 19:27:26 | ERROR    | src.core.browser_manager:_create_driver:477 | 创建WebDriver失败: you cannot reuse the ChromeOptions object
2025-07-28 19:27:26 | ERROR    | src.core.browser_manager:get_driver:164 | 创建WebDriver最终失败: MercedesFl59831
2025-07-28 19:27:26 | ERROR    | src.modules.posting.executor:login_account:360 | 登录异常: MercedesFl59831 (尝试 1/3), 无法获取WebDriver
2025-07-28 19:29:18 | ERROR    | src.core.browser_manager:create_driver:451 | 创建Chrome实例最终失败: you cannot reuse the ChromeOptions object
2025-07-28 19:29:18 | ERROR    | src.core.browser_manager:_create_driver:477 | 创建WebDriver失败: you cannot reuse the ChromeOptions object
2025-07-28 19:30:40 | ERROR    | src.core.browser_manager:create_driver:451 | 创建Chrome实例最终失败: you cannot reuse the ChromeOptions object
2025-07-28 19:30:40 | ERROR    | src.core.browser_manager:_create_driver:477 | 创建WebDriver失败: you cannot reuse the ChromeOptions object
2025-07-28 19:30:40 | ERROR    | src.core.browser_manager:_create_driver:477 | 创建WebDriver失败: cannot schedule new futures after shutdown
2025-07-28 19:55:48 | ERROR    | src.modules.posting.executor:login_account:360 | 登录异常: MercedesFl59831 (尝试 1/3), Message: Driver已关闭

2025-07-28 22:30:44 | ERROR    | src.modules.posting.executor:_publish_post:1021 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态，已尝试所有选择器
2025-07-28 22:30:44 | ERROR    | src.modules.posting.executor:execute_post:636 | 发帖失败: MercedesFl59831, 发布按钮在等待时间内未变为可用状态，已尝试所有选择器
2025-07-28 22:51:09 | ERROR    | src.modules.posting.executor:_verify_media_upload:1020 | ❌ 没有检测到已上传的媒体文件
2025-07-28 22:51:58 | ERROR    | src.modules.posting.executor:_publish_post:1244 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态，已尝试所有选择器
2025-07-28 22:51:58 | ERROR    | src.modules.posting.executor:execute_post:646 | 发帖失败: MercedesFl59831, 发布按钮在等待时间内未变为可用状态，已尝试所有选择器
2025-07-29 01:01:17 | ERROR    | src.modules.posting.executor:_verify_media_upload:1356 | ❌ 没有检测到已上传的媒体文件
2025-07-29 01:54:03 | ERROR    | src.modules.posting.executor:_verify_media_upload:1381 | ❌ 没有检测到已上传的媒体文件
2025-07-29 01:55:34 | ERROR    | src.core.anti_detection:enhanced_safe_click:854 | 所有点击尝试都失败: [data-testid="tweetButton"]
2025-07-29 01:59:38 | ERROR    | src.modules.posting.executor:_verify_media_upload:1381 | ❌ 没有检测到已上传的媒体文件
2025-07-29 04:05:03 | ERROR    | src.modules.posting.executor:_upload_media_files:1376 | 上传媒体文件失败: 强制使用逐个上传模式
2025-07-29 04:05:03 | ERROR    | src.modules.posting.executor:execute_post:650 | 发帖失败: PaigeBaldw49027, 强制使用逐个上传模式
2025-07-29 04:07:18 | ERROR    | src.modules.posting.executor:_upload_media_files:1376 | 上传媒体文件失败: 强制使用逐个上传模式
2025-07-29 04:07:18 | ERROR    | src.modules.posting.executor:execute_post:650 | 发帖失败: PaigeBaldw49027, 强制使用逐个上传模式
2025-07-29 04:10:45 | ERROR    | src.modules.posting.executor:_navigate_to_compose_page:1776 | 导航到发帖页面失败: Message: Driver已关闭

2025-07-29 04:10:45 | ERROR    | src.modules.posting.executor:execute_post:650 | 发帖失败: PaigeBaldw49027, 无法导航到发帖页面
2025-07-29 15:42:26 | ERROR    | src.modules.posting.executor:_force_text_input:840 | ❌ 强力输入仍然失败: 预期'✨✨✨✨✨✨✨
你好啊，今天很好✨值得拥有', 实际'✨✨✨✨✨✨✨
你好啊，今天很好✨值得拥✨好啊，今天很好✨得拥有有'
2025-07-29 17:15:02 | ERROR    | src.core.browser_manager:create_driver:325 | Chrome启动超时（30秒）
2025-07-29 17:16:27 | ERROR    | src.core.browser_manager:create_driver:392 | 创建Chrome实例最终失败: you cannot reuse the ChromeOptions object
2025-07-29 17:16:27 | ERROR    | src.core.browser_manager:_create_driver:418 | 创建WebDriver失败: you cannot reuse the ChromeOptions object
2025-07-29 17:16:57 | ERROR    | src.core.browser_manager:create_driver:325 | Chrome启动超时（30秒）
2025-07-29 17:18:21 | ERROR    | src.core.browser_manager:create_driver:392 | 创建Chrome实例最终失败: you cannot reuse the ChromeOptions object
2025-07-29 23:57:46 | ERROR    | src.core.browser_manager:create_chrome_fast:495 | Chrome快速启动失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x651af3+62339]
	GetHandleVerifier [0x0x651b34+62404]
	(No symbol) [0x0x492123]
	(No symbol) [0x0x4b9693]
	(No symbol) [0x0x4bae20]
	(No symbol) [0x0x4b5f5a]
	(No symbol) [0x0x509782]
	(No symbol) [0x0x50926c]
	(No symbol) [0x0x50a960]
	(No symbol) [0x0x50a76a]
	(No symbol) [0x0x4ff1b6]
	(No symbol) [0x0x4ce7a2]
	(No symbol) [0x0x4cf644]
	GetHandleVerifier [0x0x8c6683+2637587]
	GetHandleVerifier [0x0x8c1a8a+2618138]
	GetHandleVerifier [0x0x67856a+220666]
	GetHandleVerifier [0x0x668998+156200]
	GetHandleVerifier [0x0x66f12d+182717]
	GetHandleVerifier [0x0x659a38+94920]
	GetHandleVerifier [0x0x659bc2+95314]
	GetHandleVerifier [0x0x644d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-29 23:57:46 | ERROR    | src.core.browser_manager:_fast_create_driver:519 | 快速创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x651af3+62339]
	GetHandleVerifier [0x0x651b34+62404]
	(No symbol) [0x0x492123]
	(No symbol) [0x0x4b9693]
	(No symbol) [0x0x4bae20]
	(No symbol) [0x0x4b5f5a]
	(No symbol) [0x0x509782]
	(No symbol) [0x0x50926c]
	(No symbol) [0x0x50a960]
	(No symbol) [0x0x50a76a]
	(No symbol) [0x0x4ff1b6]
	(No symbol) [0x0x4ce7a2]
	(No symbol) [0x0x4cf644]
	GetHandleVerifier [0x0x8c6683+2637587]
	GetHandleVerifier [0x0x8c1a8a+2618138]
	GetHandleVerifier [0x0x67856a+220666]
	GetHandleVerifier [0x0x668998+156200]
	GetHandleVerifier [0x0x66f12d+182717]
	GetHandleVerifier [0x0x659a38+94920]
	GetHandleVerifier [0x0x659bc2+95314]
	GetHandleVerifier [0x0x644d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-29 23:57:47 | ERROR    | src.core.browser_manager:create_driver:353 | Chrome创建失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x651af3+62339]
	GetHandleVerifier [0x0x651b34+62404]
	(No symbol) [0x0x492123]
	(No symbol) [0x0x4b9693]
	(No symbol) [0x0x4bae20]
	(No symbol) [0x0x4b5f5a]
	(No symbol) [0x0x509782]
	(No symbol) [0x0x50926c]
	(No symbol) [0x0x50a960]
	(No symbol) [0x0x50a76a]
	(No symbol) [0x0x4ff1b6]
	(No symbol) [0x0x4ce7a2]
	(No symbol) [0x0x4cf644]
	GetHandleVerifier [0x0x8c6683+2637587]
	GetHandleVerifier [0x0x8c1a8a+2618138]
	GetHandleVerifier [0x0x67856a+220666]
	GetHandleVerifier [0x0x668998+156200]
	GetHandleVerifier [0x0x66f12d+182717]
	GetHandleVerifier [0x0x659a38+94920]
	GetHandleVerifier [0x0x659bc2+95314]
	GetHandleVerifier [0x0x644d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-29 23:57:47 | ERROR    | src.core.browser_manager:_create_driver:379 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x651af3+62339]
	GetHandleVerifier [0x0x651b34+62404]
	(No symbol) [0x0x492123]
	(No symbol) [0x0x4b9693]
	(No symbol) [0x0x4bae20]
	(No symbol) [0x0x4b5f5a]
	(No symbol) [0x0x509782]
	(No symbol) [0x0x50926c]
	(No symbol) [0x0x50a960]
	(No symbol) [0x0x50a76a]
	(No symbol) [0x0x4ff1b6]
	(No symbol) [0x0x4ce7a2]
	(No symbol) [0x0x4cf644]
	GetHandleVerifier [0x0x8c6683+2637587]
	GetHandleVerifier [0x0x8c1a8a+2618138]
	GetHandleVerifier [0x0x67856a+220666]
	GetHandleVerifier [0x0x668998+156200]
	GetHandleVerifier [0x0x66f12d+182717]
	GetHandleVerifier [0x0x659a38+94920]
	GetHandleVerifier [0x0x659bc2+95314]
	GetHandleVerifier [0x0x644d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-29 23:57:49 | ERROR    | src.core.browser_manager:create_driver:353 | Chrome创建失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x651af3+62339]
	GetHandleVerifier [0x0x651b34+62404]
	(No symbol) [0x0x492123]
	(No symbol) [0x0x4b9693]
	(No symbol) [0x0x4bae20]
	(No symbol) [0x0x4b5f5a]
	(No symbol) [0x0x509782]
	(No symbol) [0x0x50926c]
	(No symbol) [0x0x50a960]
	(No symbol) [0x0x50a76a]
	(No symbol) [0x0x4ff1b6]
	(No symbol) [0x0x4ce7a2]
	(No symbol) [0x0x4cf644]
	GetHandleVerifier [0x0x8c6683+2637587]
	GetHandleVerifier [0x0x8c1a8a+2618138]
	GetHandleVerifier [0x0x67856a+220666]
	GetHandleVerifier [0x0x668998+156200]
	GetHandleVerifier [0x0x66f12d+182717]
	GetHandleVerifier [0x0x659a38+94920]
	GetHandleVerifier [0x0x659bc2+95314]
	GetHandleVerifier [0x0x644d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-29 23:57:49 | ERROR    | src.core.browser_manager:_create_driver:379 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x651af3+62339]
	GetHandleVerifier [0x0x651b34+62404]
	(No symbol) [0x0x492123]
	(No symbol) [0x0x4b9693]
	(No symbol) [0x0x4bae20]
	(No symbol) [0x0x4b5f5a]
	(No symbol) [0x0x509782]
	(No symbol) [0x0x50926c]
	(No symbol) [0x0x50a960]
	(No symbol) [0x0x50a76a]
	(No symbol) [0x0x4ff1b6]
	(No symbol) [0x0x4ce7a2]
	(No symbol) [0x0x4cf644]
	GetHandleVerifier [0x0x8c6683+2637587]
	GetHandleVerifier [0x0x8c1a8a+2618138]
	GetHandleVerifier [0x0x67856a+220666]
	GetHandleVerifier [0x0x668998+156200]
	GetHandleVerifier [0x0x66f12d+182717]
	GetHandleVerifier [0x0x659a38+94920]
	GetHandleVerifier [0x0x659bc2+95314]
	GetHandleVerifier [0x0x644d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-29 23:57:49 | ERROR    | src.core.browser_manager:get_driver:241 | 创建WebDriver最终失败: PaigeBaldw49027
2025-07-29 23:57:49 | ERROR    | src.modules.posting.executor:execute_post:650 | 发帖失败: PaigeBaldw49027, 获取WebDriver失败
2025-07-29 23:58:10 | ERROR    | src.core.browser_manager:create_chrome_fast:495 | Chrome快速启动失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x411af3+62339]
	GetHandleVerifier [0x0x411b34+62404]
	(No symbol) [0x0x252123]
	(No symbol) [0x0x279693]
	(No symbol) [0x0x27ae20]
	(No symbol) [0x0x275f5a]
	(No symbol) [0x0x2c9782]
	(No symbol) [0x0x2c926c]
	(No symbol) [0x0x2ca960]
	(No symbol) [0x0x2ca76a]
	(No symbol) [0x0x2bf1b6]
	(No symbol) [0x0x28e7a2]
	(No symbol) [0x0x28f644]
	GetHandleVerifier [0x0x686683+2637587]
	GetHandleVerifier [0x0x681a8a+2618138]
	GetHandleVerifier [0x0x43856a+220666]
	GetHandleVerifier [0x0x428998+156200]
	GetHandleVerifier [0x0x42f12d+182717]
	GetHandleVerifier [0x0x419a38+94920]
	GetHandleVerifier [0x0x419bc2+95314]
	GetHandleVerifier [0x0x404d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 00:43:28 | ERROR    | src.modules.posting.workflow:_create_posting_task:213 | 创建发帖任务失败: (sqlite3.OperationalError) table posting_tasks has no column named posted_group_id
[SQL: INSERT INTO posting_tasks (account_id, content, media_ids, post_url, status, scheduled_time, completed_time, error_message, posted_group_id, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: (9, '💯👍\n\n又是美好的一天\n\n你们在吗✨🎊', '[6, 9]', None, 'pending', None, None, None, None, '2025-07-30 00:43:28.976868')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-30 00:43:28 | ERROR    | src.modules.posting.workflow:_create_posting_task:213 | 创建发帖任务失败: (sqlite3.OperationalError) table posting_tasks has no column named posted_group_id
[SQL: INSERT INTO posting_tasks (account_id, content, media_ids, post_url, status, scheduled_time, completed_time, error_message, posted_group_id, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: (10, '👌🤗\n\n又是美好的一天\n\n你们在吗💫👍', '[4, 11]', None, 'pending', None, None, None, None, '2025-07-30 00:43:28.994140')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-30 01:33:37 | ERROR    | src.core.browser_manager:create_chrome_fast:510 | Chrome快速启动失败: Message: session not created: cannot connect to chrome at 127.0.0.1:34847
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x10c1af3+62339]
	GetHandleVerifier [0x0x10c1b34+62404]
	(No symbol) [0x0xf01f80]
	(No symbol) [0x0xef5f2a]
	(No symbol) [0x0xf3adc6]
	(No symbol) [0x0xf3128f]
	(No symbol) [0x0xf310c6]
	(No symbol) [0x0xf7ae77]
	(No symbol) [0x0xf7a76a]
	(No symbol) [0x0xf6f1b6]
	(No symbol) [0x0xf3e7a2]
	(No symbol) [0x0xf3f644]
	GetHandleVerifier [0x0x1336683+2637587]
	GetHandleVerifier [0x0x1331a8a+2618138]
	GetHandleVerifier [0x0x10e856a+220666]
	GetHandleVerifier [0x0x10d8998+156200]
	GetHandleVerifier [0x0x10df12d+182717]
	GetHandleVerifier [0x0x10c9a38+94920]
	GetHandleVerifier [0x0x10c9bc2+95314]
	GetHandleVerifier [0x0x10b4d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 01:33:37 | ERROR    | src.core.browser_manager:_fast_create_driver:534 | 快速创建WebDriver失败: Message: session not created: cannot connect to chrome at 127.0.0.1:34847
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x10c1af3+62339]
	GetHandleVerifier [0x0x10c1b34+62404]
	(No symbol) [0x0xf01f80]
	(No symbol) [0x0xef5f2a]
	(No symbol) [0x0xf3adc6]
	(No symbol) [0x0xf3128f]
	(No symbol) [0x0xf310c6]
	(No symbol) [0x0xf7ae77]
	(No symbol) [0x0xf7a76a]
	(No symbol) [0x0xf6f1b6]
	(No symbol) [0x0xf3e7a2]
	(No symbol) [0x0xf3f644]
	GetHandleVerifier [0x0x1336683+2637587]
	GetHandleVerifier [0x0x1331a8a+2618138]
	GetHandleVerifier [0x0x10e856a+220666]
	GetHandleVerifier [0x0x10d8998+156200]
	GetHandleVerifier [0x0x10df12d+182717]
	GetHandleVerifier [0x0x10c9a38+94920]
	GetHandleVerifier [0x0x10c9bc2+95314]
	GetHandleVerifier [0x0x10b4d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 04:16:11 | ERROR    | src.core.browser_manager:create_chrome_fast:510 | Chrome快速启动失败: Message: session not created: cannot connect to chrome at 127.0.0.1:25862
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x711af3+62339]
	GetHandleVerifier [0x0x711b34+62404]
	(No symbol) [0x0x551f80]
	(No symbol) [0x0x545f2a]
	(No symbol) [0x0x58adc6]
	(No symbol) [0x0x58128f]
	(No symbol) [0x0x5810c6]
	(No symbol) [0x0x5cae77]
	(No symbol) [0x0x5ca76a]
	(No symbol) [0x0x5bf1b6]
	(No symbol) [0x0x58e7a2]
	(No symbol) [0x0x58f644]
	GetHandleVerifier [0x0x986683+2637587]
	GetHandleVerifier [0x0x981a8a+2618138]
	GetHandleVerifier [0x0x73856a+220666]
	GetHandleVerifier [0x0x728998+156200]
	GetHandleVerifier [0x0x72f12d+182717]
	GetHandleVerifier [0x0x719a38+94920]
	GetHandleVerifier [0x0x719bc2+95314]
	GetHandleVerifier [0x0x704d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 04:16:11 | ERROR    | src.core.browser_manager:_fast_create_driver:534 | 快速创建WebDriver失败: Message: session not created: cannot connect to chrome at 127.0.0.1:25862
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x711af3+62339]
	GetHandleVerifier [0x0x711b34+62404]
	(No symbol) [0x0x551f80]
	(No symbol) [0x0x545f2a]
	(No symbol) [0x0x58adc6]
	(No symbol) [0x0x58128f]
	(No symbol) [0x0x5810c6]
	(No symbol) [0x0x5cae77]
	(No symbol) [0x0x5ca76a]
	(No symbol) [0x0x5bf1b6]
	(No symbol) [0x0x58e7a2]
	(No symbol) [0x0x58f644]
	GetHandleVerifier [0x0x986683+2637587]
	GetHandleVerifier [0x0x981a8a+2618138]
	GetHandleVerifier [0x0x73856a+220666]
	GetHandleVerifier [0x0x728998+156200]
	GetHandleVerifier [0x0x72f12d+182717]
	GetHandleVerifier [0x0x719a38+94920]
	GetHandleVerifier [0x0x719bc2+95314]
	GetHandleVerifier [0x0x704d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 04:17:48 | ERROR    | src.core.browser_manager:create_driver:353 | Chrome创建失败: Message: session not created: cannot connect to chrome at 127.0.0.1:26019
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x301af3+62339]
	GetHandleVerifier [0x0x301b34+62404]
	(No symbol) [0x0x141f80]
	(No symbol) [0x0x135f2a]
	(No symbol) [0x0x17adc6]
	(No symbol) [0x0x17128f]
	(No symbol) [0x0x1710c6]
	(No symbol) [0x0x1bae77]
	(No symbol) [0x0x1ba76a]
	(No symbol) [0x0x1af1b6]
	(No symbol) [0x0x17e7a2]
	(No symbol) [0x0x17f644]
	GetHandleVerifier [0x0x576683+2637587]
	GetHandleVerifier [0x0x571a8a+2618138]
	GetHandleVerifier [0x0x32856a+220666]
	GetHandleVerifier [0x0x318998+156200]
	GetHandleVerifier [0x0x31f12d+182717]
	GetHandleVerifier [0x0x309a38+94920]
	GetHandleVerifier [0x0x309bc2+95314]
	GetHandleVerifier [0x0x2f4d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]
	(No symbol) [0x0]

2025-07-30 04:17:48 | ERROR    | src.core.browser_manager:_create_driver:379 | 创建WebDriver失败: Message: session not created: cannot connect to chrome at 127.0.0.1:26019
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x301af3+62339]
	GetHandleVerifier [0x0x301b34+62404]
	(No symbol) [0x0x141f80]
	(No symbol) [0x0x135f2a]
	(No symbol) [0x0x17adc6]
	(No symbol) [0x0x17128f]
	(No symbol) [0x0x1710c6]
	(No symbol) [0x0x1bae77]
	(No symbol) [0x0x1ba76a]
	(No symbol) [0x0x1af1b6]
	(No symbol) [0x0x17e7a2]
	(No symbol) [0x0x17f644]
	GetHandleVerifier [0x0x576683+2637587]
	GetHandleVerifier [0x0x571a8a+2618138]
	GetHandleVerifier [0x0x32856a+220666]
	GetHandleVerifier [0x0x318998+156200]
	GetHandleVerifier [0x0x31f12d+182717]
	GetHandleVerifier [0x0x309a38+94920]
	GetHandleVerifier [0x0x309bc2+95314]
	GetHandleVerifier [0x0x2f4d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]
	(No symbol) [0x0]

2025-07-30 04:25:29 | ERROR    | src.core.browser_manager:_fast_create_driver:584 | 快速创建WebDriver失败: you cannot reuse the ChromeOptions object
2025-07-30 04:27:07 | ERROR    | src.core.browser_manager:create_driver:370 | Chrome创建失败: Message: session not created: cannot connect to chrome at 127.0.0.1:43987
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x1221af3+62339]
	GetHandleVerifier [0x0x1221b34+62404]
	(No symbol) [0x0x1061f80]
	(No symbol) [0x0x1055f2a]
	(No symbol) [0x0x109adc6]
	(No symbol) [0x0x109128f]
	(No symbol) [0x0x10910c6]
	(No symbol) [0x0x10dae77]
	(No symbol) [0x0x10da76a]
	(No symbol) [0x0x10cf1b6]
	(No symbol) [0x0x109e7a2]
	(No symbol) [0x0x109f644]
	GetHandleVerifier [0x0x1496683+2637587]
	GetHandleVerifier [0x0x1491a8a+2618138]
	GetHandleVerifier [0x0x124856a+220666]
	GetHandleVerifier [0x0x1238998+156200]
	GetHandleVerifier [0x0x123f12d+182717]
	GetHandleVerifier [0x0x1229a38+94920]
	GetHandleVerifier [0x0x1229bc2+95314]
	GetHandleVerifier [0x0x1214d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 04:27:07 | ERROR    | src.core.browser_manager:_create_driver:396 | 创建WebDriver失败: Message: session not created: cannot connect to chrome at 127.0.0.1:43987
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x1221af3+62339]
	GetHandleVerifier [0x0x1221b34+62404]
	(No symbol) [0x0x1061f80]
	(No symbol) [0x0x1055f2a]
	(No symbol) [0x0x109adc6]
	(No symbol) [0x0x109128f]
	(No symbol) [0x0x10910c6]
	(No symbol) [0x0x10dae77]
	(No symbol) [0x0x10da76a]
	(No symbol) [0x0x10cf1b6]
	(No symbol) [0x0x109e7a2]
	(No symbol) [0x0x109f644]
	GetHandleVerifier [0x0x1496683+2637587]
	GetHandleVerifier [0x0x1491a8a+2618138]
	GetHandleVerifier [0x0x124856a+220666]
	GetHandleVerifier [0x0x1238998+156200]
	GetHandleVerifier [0x0x123f12d+182717]
	GetHandleVerifier [0x0x1229a38+94920]
	GetHandleVerifier [0x0x1229bc2+95314]
	GetHandleVerifier [0x0x1214d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 04:28:46 | ERROR    | src.core.browser_manager:create_driver:370 | Chrome创建失败: Message: session not created: cannot connect to chrome at 127.0.0.1:20726
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x1191af3+62339]
	GetHandleVerifier [0x0x1191b34+62404]
	(No symbol) [0x0xfd1f80]
	(No symbol) [0x0xfc5f2a]
	(No symbol) [0x0x100adc6]
	(No symbol) [0x0x100128f]
	(No symbol) [0x0x10010c6]
	(No symbol) [0x0x104ae77]
	(No symbol) [0x0x104a76a]
	(No symbol) [0x0x103f1b6]
	(No symbol) [0x0x100e7a2]
	(No symbol) [0x0x100f644]
	GetHandleVerifier [0x0x1406683+2637587]
	GetHandleVerifier [0x0x1401a8a+2618138]
	GetHandleVerifier [0x0x11b856a+220666]
	GetHandleVerifier [0x0x11a8998+156200]
	GetHandleVerifier [0x0x11af12d+182717]
	GetHandleVerifier [0x0x1199a38+94920]
	GetHandleVerifier [0x0x1199bc2+95314]
	GetHandleVerifier [0x0x1184d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 04:28:46 | ERROR    | src.core.browser_manager:_create_driver:396 | 创建WebDriver失败: Message: session not created: cannot connect to chrome at 127.0.0.1:20726
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x1191af3+62339]
	GetHandleVerifier [0x0x1191b34+62404]
	(No symbol) [0x0xfd1f80]
	(No symbol) [0x0xfc5f2a]
	(No symbol) [0x0x100adc6]
	(No symbol) [0x0x100128f]
	(No symbol) [0x0x10010c6]
	(No symbol) [0x0x104ae77]
	(No symbol) [0x0x104a76a]
	(No symbol) [0x0x103f1b6]
	(No symbol) [0x0x100e7a2]
	(No symbol) [0x0x100f644]
	GetHandleVerifier [0x0x1406683+2637587]
	GetHandleVerifier [0x0x1401a8a+2618138]
	GetHandleVerifier [0x0x11b856a+220666]
	GetHandleVerifier [0x0x11a8998+156200]
	GetHandleVerifier [0x0x11af12d+182717]
	GetHandleVerifier [0x0x1199a38+94920]
	GetHandleVerifier [0x0x1199bc2+95314]
	GetHandleVerifier [0x0x1184d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 04:28:46 | ERROR    | src.core.browser_manager:get_driver:258 | 创建WebDriver最终失败: PaigeBaldw49027
2025-07-30 04:28:46 | ERROR    | src.modules.posting.executor:execute_post:650 | 发帖失败: PaigeBaldw49027, 获取WebDriver失败
2025-07-30 04:31:57 | ERROR    | src.modules.posting.executor:_navigate_to_compose_page:2368 | 导航到发帖页面失败: Message: Driver已关闭

2025-07-30 04:31:57 | ERROR    | src.modules.posting.executor:execute_post:650 | 发帖失败: CharleneCa2057, 无法导航到发帖页面
2025-07-30 04:37:59 | ERROR    | src.modules.posting.executor:_navigate_to_compose_page:2368 | 导航到发帖页面失败: Message: Driver已关闭

2025-07-30 04:37:59 | ERROR    | src.modules.posting.executor:execute_post:650 | 发帖失败: CharleneCa2057, 无法导航到发帖页面
2025-07-30 04:46:26 | ERROR    | src.modules.posting.executor:execute_post:695 | 发帖失败: CharleneCa2057, 'PostingExecutor' object has no attribute 'cookie_manager'
2025-07-30 04:50:37 | ERROR    | src.modules.posting.executor:_smart_click_with_recovery:1820 | 所有智能点击尝试都失败
2025-07-30 04:50:37 | ERROR    | src.modules.posting.executor:_publish_post:2194 | 发布帖子失败: 所有智能点击策略都失败了
2025-07-30 04:50:37 | ERROR    | src.modules.posting.executor:execute_post:696 | 发帖失败: CharleneCa2057, 所有智能点击策略都失败了
2025-07-30 05:27:41 | ERROR    | src.modules.posting.executor:_find_element_elegantly:669 | ❌ 无法找到元素: compose_button
2025-07-30 05:27:47 | ERROR    | src.modules.posting.executor:_find_element_elegantly:669 | ❌ 无法找到元素: compose_button
2025-07-30 05:27:53 | ERROR    | src.modules.posting.executor:_find_element_elegantly:669 | ❌ 无法找到元素: compose_button
2025-07-30 05:27:53 | ERROR    | src.modules.posting.executor:execute_post:748 | ❌ 发帖失败: 无法找到发帖按钮
2025-07-30 05:30:23 | ERROR    | src.modules.posting.executor:_find_element_elegantly:691 | ❌ 无法找到元素: compose_button
2025-07-30 05:30:30 | ERROR    | src.modules.posting.executor:_find_element_elegantly:691 | ❌ 无法找到元素: compose_button
2025-07-30 05:30:36 | ERROR    | src.modules.posting.executor:_find_element_elegantly:691 | ❌ 无法找到元素: compose_button
2025-07-30 05:30:36 | ERROR    | src.modules.posting.executor:execute_post:770 | ❌ 发帖失败: 无法找到发帖按钮
2025-07-30 05:38:49 | ERROR    | src.modules.posting.executor:_find_element_elegantly:699 | ❌ 无法找到元素: media_input
2025-07-30 05:39:09 | ERROR    | src.modules.posting.executor:_find_element_elegantly:699 | ❌ 无法找到元素: media_input
2025-07-30 05:39:33 | ERROR    | src.modules.posting.executor:_find_element_elegantly:699 | ❌ 无法找到元素: media_input
2025-07-30 05:39:33 | ERROR    | src.modules.posting.executor:execute_post:821 | ❌ 发帖失败: 媒体上传失败: 无法找到媒体输入框
2025-07-30 05:42:17 | ERROR    | src.modules.posting.executor:_elegant_upload_media:1040 | 媒体上传异常: Message: unknown error: path is not absolute: data/media/com_202507171332_5d1f129e677479f74f497a1c68949daa__percentage_cropped.jpg
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0xabba83+63395]
	GetHandleVerifier [0x0xabbac4+63460]
	(No symbol) [0x0x902113]
	(No symbol) [0x0x91101d]
	(No symbol) [0x0x943a53]
	(No symbol) [0x0x96f3ac]
	(No symbol) [0x0x93ffa4]
	(No symbol) [0x0x96f624]
	(No symbol) [0x0x9907ba]
	(No symbol) [0x0x96f1a6]
	(No symbol) [0x0x93e7b2]
	(No symbol) [0x0x93f654]
	GetHandleVerifier [0x0xd38883+2672035]
	GetHandleVerifier [0x0xd33cba+2652634]
	GetHandleVerifier [0x0xae2bca+223466]
	GetHandleVerifier [0x0xad2cb8+158168]
	GetHandleVerifier [0x0xad978d+185517]
	GetHandleVerifier [0x0xac3b78+96408]
	GetHandleVerifier [0x0xac3d02+96802]
	GetHandleVerifier [0x0xaae90a+9770]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 05:42:37 | ERROR    | src.modules.posting.executor:_elegant_upload_media:1040 | 媒体上传异常: Message: unknown error: path is not absolute: data/media/com_202507171332_5d1f129e677479f74f497a1c68949daa__percentage_cropped.jpg
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0xabba83+63395]
	GetHandleVerifier [0x0xabbac4+63460]
	(No symbol) [0x0x902113]
	(No symbol) [0x0x91101d]
	(No symbol) [0x0x943a53]
	(No symbol) [0x0x96f3ac]
	(No symbol) [0x0x93ffa4]
	(No symbol) [0x0x96f624]
	(No symbol) [0x0x9907ba]
	(No symbol) [0x0x96f1a6]
	(No symbol) [0x0x93e7b2]
	(No symbol) [0x0x93f654]
	GetHandleVerifier [0x0xd38883+2672035]
	GetHandleVerifier [0x0xd33cba+2652634]
	GetHandleVerifier [0x0xae2bca+223466]
	GetHandleVerifier [0x0xad2cb8+158168]
	GetHandleVerifier [0x0xad978d+185517]
	GetHandleVerifier [0x0xac3b78+96408]
	GetHandleVerifier [0x0xac3d02+96802]
	GetHandleVerifier [0x0xaae90a+9770]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 05:42:54 | ERROR    | src.modules.posting.executor:_elegant_upload_media:1040 | 媒体上传异常: Message: unknown error: path is not absolute: data/media/com_202507171332_5d1f129e677479f74f497a1c68949daa__percentage_cropped.jpg
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0xabba83+63395]
	GetHandleVerifier [0x0xabbac4+63460]
	(No symbol) [0x0x902113]
	(No symbol) [0x0x91101d]
	(No symbol) [0x0x943a53]
	(No symbol) [0x0x96f3ac]
	(No symbol) [0x0x93ffa4]
	(No symbol) [0x0x96f624]
	(No symbol) [0x0x9907ba]
	(No symbol) [0x0x96f1a6]
	(No symbol) [0x0x93e7b2]
	(No symbol) [0x0x93f654]
	GetHandleVerifier [0x0xd38883+2672035]
	GetHandleVerifier [0x0xd33cba+2652634]
	GetHandleVerifier [0x0xae2bca+223466]
	GetHandleVerifier [0x0xad2cb8+158168]
	GetHandleVerifier [0x0xad978d+185517]
	GetHandleVerifier [0x0xac3b78+96408]
	GetHandleVerifier [0x0xac3d02+96802]
	GetHandleVerifier [0x0xaae90a+9770]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 05:42:54 | ERROR    | src.modules.posting.executor:execute_post:821 | ❌ 发帖失败: 媒体上传失败: 媒体上传异常: Message: unknown error: path is not absolute: data/media/com_202507171332_5d1f129e677479f74f497a1c68949daa__percentage_cropped.jpg
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0xabba83+63395]
	GetHandleVerifier [0x0xabbac4+63460]
	(No symbol) [0x0x902113]
	(No symbol) [0x0x91101d]
	(No symbol) [0x0x943a53]
	(No symbol) [0x0x96f3ac]
	(No symbol) [0x0x93ffa4]
	(No symbol) [0x0x96f624]
	(No symbol) [0x0x9907ba]
	(No symbol) [0x0x96f1a6]
	(No symbol) [0x0x93e7b2]
	(No symbol) [0x0x93f654]
	GetHandleVerifier [0x0xd38883+2672035]
	GetHandleVerifier [0x0xd33cba+2652634]
	GetHandleVerifier [0x0xae2bca+223466]
	GetHandleVerifier [0x0xad2cb8+158168]
	GetHandleVerifier [0x0xad978d+185517]
	GetHandleVerifier [0x0xac3b78+96408]
	GetHandleVerifier [0x0xac3d02+96802]
	GetHandleVerifier [0x0xaae90a+9770]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 05:56:36 | ERROR    | src.modules.posting.executor:execute_post:828 | 发帖失败: CharleneCa2057, 'PostingExecutor' object has no attribute '_save_cookies'
2025-07-30 06:08:41 | ERROR    | src.modules.posting.executor:execute_post:828 | 发帖失败: CharleneCa2057, 'PostingExecutor' object has no attribute '_save_cookies'
2025-07-30 06:19:47 | ERROR    | src.modules.posting.executor:execute_post:828 | 发帖失败: CharleneCa2057, 'PostingExecutor' object has no attribute '_save_cookies'
2025-07-30 14:36:58 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:602 | Chrome启动彻底失败: Message: session not created: cannot connect to chrome at 127.0.0.1:20812
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x7ff617dee415+77285]
	GetHandleVerifier [0x0x7ff617dee470+77376]
	(No symbol) [0x0x7ff617bb989c]
	(No symbol) [0x0x7ff617baab71]
	(No symbol) [0x0x7ff617bfd0c2]
	(No symbol) [0x0x7ff617bf1eb9]
	(No symbol) [0x0x7ff617c4602e]
	(No symbol) [0x0x7ff617c457c0]
	(No symbol) [0x0x7ff617c383e3]
	(No symbol) [0x0x7ff617c01521]
	(No symbol) [0x0x7ff617c022b3]
	GetHandleVerifier [0x0x7ff6180d1efd+3107021]
	GetHandleVerifier [0x0x7ff6180cc29d+3083373]
	GetHandleVerifier [0x0x7ff6180ebedd+3213485]
	GetHandleVerifier [0x0x7ff617e0884e+184862]
	GetHandleVerifier [0x0x7ff617e1055f+216879]
	GetHandleVerifier [0x0x7ff617df7084+113236]
	GetHandleVerifier [0x0x7ff617df7239+113673]
	GetHandleVerifier [0x0x7ff617dde298+11368]
	BaseThreadInitThunk [0x0x7ff80818e8d7+23]
	RtlUserThreadStart [0x0x7ff808d1c34c+44]

2025-07-30 14:39:40 | ERROR    | src.modules.posting.executor:_input_text_content:1391 | 输入文本内容失败: Message: element click intercepted: Element <div aria-activedescendant="typeaheadFocus-0.08036443032719642" aria-autocomplete="list" aria-controls="typeaheadDropdownWrapped-1" aria-describedby="placeholder-7olj3" aria-label="Post text" aria-multiline="true" class="notranslate public-DraftEditor-content" contenteditable="true" data-testid="tweetTextarea_0" role="textbox" spellcheck="true" tabindex="0" no-focustrapview-refocus="true" style="outline: none; user-select: text; white-space: pre-wrap; overflow-wrap: break-word;">...</div> is not clickable at point (911, 14). Other element would receive the click: <a href="/home" role="tab" aria-selected="false" tabindex="-1" class="css-175oi2r r-1awozwy r-6koalj r-eqz5dr r-16y2uox r-1h3ijdo r-1777fci r-s8bhmr r-3pj75a r-o7ynqc r-6416eg r-1ny4l3l r-1loqt21">...</a>
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0xf7ba83+63395]
	GetHandleVerifier [0x0xf7bac4+63460]
	(No symbol) [0x0xdc2113]
	(No symbol) [0x0xe10ba0]
	(No symbol) [0x0xe0ef5a]
	(No symbol) [0x0xe0cab7]
	(No symbol) [0x0xe0bd6d]
	(No symbol) [0x0xe00515]
	(No symbol) [0x0xe2f3ac]
	(No symbol) [0x0xdfffa4]
	(No symbol) [0x0xe2f624]
	(No symbol) [0x0xe507ba]
	(No symbol) [0x0xe2f1a6]
	(No symbol) [0x0xdfe7b2]
	(No symbol) [0x0xdff654]
	GetHandleVerifier [0x0x11f8883+2672035]
	GetHandleVerifier [0x0x11f3cba+2652634]
	GetHandleVerifier [0x0xfa2bca+223466]
	GetHandleVerifier [0x0xf92cb8+158168]
	GetHandleVerifier [0x0xf9978d+185517]
	GetHandleVerifier [0x0xf83b78+96408]
	GetHandleVerifier [0x0xf83d02+96802]
	GetHandleVerifier [0x0xf6e90a+9770]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 16:00:31 | ERROR    | src.modules.posting.executor:_input_text_content:1391 | 输入文本内容失败: Message: element click intercepted: Element <div aria-activedescendant="typeaheadFocus-0.7917454240364509" aria-autocomplete="list" aria-controls="typeaheadDropdownWrapped-1" aria-describedby="placeholder-688jl" aria-label="Post text" aria-multiline="true" class="notranslate public-DraftEditor-content" contenteditable="true" data-testid="tweetTextarea_0" role="textbox" spellcheck="true" tabindex="0" no-focustrapview-refocus="true" style="outline: none; user-select: text; white-space: pre-wrap; overflow-wrap: break-word;">...</div> is not clickable at point (911, 90). Other element would receive the click: <div class="css-175oi2r r-1p0dtai r-1d2f490 r-1xcajam r-zchlnj r-ipm5af r-1ffj0ar" data-testid="mask"></div>
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x120ba83+63395]
	GetHandleVerifier [0x0x120bac4+63460]
	(No symbol) [0x0x1052113]
	(No symbol) [0x0x10a0ba0]
	(No symbol) [0x0x109ef5a]
	(No symbol) [0x0x109cab7]
	(No symbol) [0x0x109bd6d]
	(No symbol) [0x0x1090515]
	(No symbol) [0x0x10bf3ac]
	(No symbol) [0x0x108ffa4]
	(No symbol) [0x0x10bf624]
	(No symbol) [0x0x10e07ba]
	(No symbol) [0x0x10bf1a6]
	(No symbol) [0x0x108e7b2]
	(No symbol) [0x0x108f654]
	GetHandleVerifier [0x0x1488883+2672035]
	GetHandleVerifier [0x0x1483cba+2652634]
	GetHandleVerifier [0x0x1232bca+223466]
	GetHandleVerifier [0x0x1222cb8+158168]
	GetHandleVerifier [0x0x122978d+185517]
	GetHandleVerifier [0x0x1213b78+96408]
	GetHandleVerifier [0x0x1213d02+96802]
	GetHandleVerifier [0x0x11fe90a+9770]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 16:00:49 | ERROR    | src.modules.posting.executor:_input_text_content:1391 | 输入文本内容失败: Message: element click intercepted: Element <div aria-activedescendant="typeaheadFocus-0.8842456527121058" aria-autocomplete="list" aria-controls="typeaheadDropdownWrapped-1" aria-describedby="placeholder-9ppig" aria-label="Post text" aria-multiline="true" class="notranslate public-DraftEditor-content" contenteditable="true" data-testid="tweetTextarea_0" role="textbox" spellcheck="true" tabindex="0" no-focustrapview-refocus="true" style="outline: none; user-select: text; white-space: pre-wrap; overflow-wrap: break-word;">...</div> is not clickable at point (911, 14). Other element would receive the click: <a href="/home" role="tab" aria-selected="false" tabindex="-1" class="css-175oi2r r-1awozwy r-6koalj r-eqz5dr r-16y2uox r-1h3ijdo r-1777fci r-s8bhmr r-3pj75a r-o7ynqc r-6416eg r-1ny4l3l r-1loqt21">...</a>
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x120ba83+63395]
	GetHandleVerifier [0x0x120bac4+63460]
	(No symbol) [0x0x1052113]
	(No symbol) [0x0x10a0ba0]
	(No symbol) [0x0x109ef5a]
	(No symbol) [0x0x109cab7]
	(No symbol) [0x0x109bd6d]
	(No symbol) [0x0x1090515]
	(No symbol) [0x0x10bf3ac]
	(No symbol) [0x0x108ffa4]
	(No symbol) [0x0x10bf624]
	(No symbol) [0x0x10e07ba]
	(No symbol) [0x0x10bf1a6]
	(No symbol) [0x0x108e7b2]
	(No symbol) [0x0x108f654]
	GetHandleVerifier [0x0x1488883+2672035]
	GetHandleVerifier [0x0x1483cba+2652634]
	GetHandleVerifier [0x0x1232bca+223466]
	GetHandleVerifier [0x0x1222cb8+158168]
	GetHandleVerifier [0x0x122978d+185517]
	GetHandleVerifier [0x0x1213b78+96408]
	GetHandleVerifier [0x0x1213d02+96802]
	GetHandleVerifier [0x0x11fe90a+9770]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 16:01:12 | ERROR    | src.modules.posting.executor:execute_post:821 | ❌ 发帖失败: 发布按钮不可用
2025-07-30 18:12:02 | ERROR    | src.ui.main_window:create_tabs:144 | 创建标签页失败: name 'QGridLayout' is not defined
2025-07-31 21:16:26 | ERROR    | src.modules.interaction.executor:execute_interaction:152 | 互动异常: CharleneCa2057, like, Task <Task pending name='Task-2' coro=<InteractionExecutor._execute_concurrent_interactions.<locals>.execute_single_interaction_with_semaphore() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\interaction\executor.py:385> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-31 21:16:26 | ERROR    | src.modules.interaction.executor:execute_interaction:152 | 互动异常: MercedesFl59831, like, Task <Task pending name='Task-3' coro=<InteractionExecutor._execute_concurrent_interactions.<locals>.execute_single_interaction_with_semaphore() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\interaction\executor.py:385> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-31 23:36:22 | ERROR    | src.ui.main_window:create_tabs:144 | 创建标签页失败: name 'QFrame' is not defined
2025-08-01 00:11:08 | ERROR    | src.modules.posting.executor:execute_post:828 | 发帖失败: CharleneCa2057, Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\workflow.py:111> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 00:11:09 | ERROR    | src.modules.posting.executor:execute_post:828 | 发帖失败: EWebb32471, Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\workflow.py:111> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 00:11:09 | ERROR    | src.modules.posting.executor:execute_post:828 | 发帖失败: MercedesFl59831, Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\workflow.py:111> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 00:11:13 | ERROR    | src.ui.widgets.posting_widget:on_posting_completed:639 | 发帖失败详情: {'success_count': 0, 'failed_count': 3, 'total_count': 3, 'errors': ["Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\\yaoya\\Documents\\ceshishi\\yyy1 - 副本 - 副本 (9) - 副本 - 副本\\src\\modules\\posting\\workflow.py:111> cb=[_run_until_complete_cb() at D:\\python39\\lib\\asyncio\\base_events.py:184]> got Future <Future pending> attached to a different loop", "Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\\yaoya\\Documents\\ceshishi\\yyy1 - 副本 - 副本 (9) - 副本 - 副本\\src\\modules\\posting\\workflow.py:111> cb=[_run_until_complete_cb() at D:\\python39\\lib\\asyncio\\base_events.py:184]> got Future <Future pending> attached to a different loop", "Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\\yaoya\\Documents\\ceshishi\\yyy1 - 副本 - 副本 (9) - 副本 - 副本\\src\\modules\\posting\\workflow.py:111> cb=[_run_until_complete_cb() at D:\\python39\\lib\\asyncio\\base_events.py:184]> got Future <Future pending> attached to a different loop"]}
2025-08-01 00:11:17 | ERROR    | src.modules.posting.executor:execute_post:828 | 发帖失败: CharleneCa2057, Task <Task pending name='Task-3' coro=<PostingWorkflow.execute_batch_posting() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\workflow.py:111> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 00:11:17 | ERROR    | src.modules.posting.executor:execute_post:828 | 发帖失败: EWebb32471, Task <Task pending name='Task-3' coro=<PostingWorkflow.execute_batch_posting() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\workflow.py:111> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 00:11:17 | ERROR    | src.modules.posting.executor:execute_post:828 | 发帖失败: MercedesFl59831, Task <Task pending name='Task-3' coro=<PostingWorkflow.execute_batch_posting() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\workflow.py:111> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 00:11:21 | ERROR    | src.ui.widgets.posting_widget:on_posting_completed:639 | 发帖失败详情: {'success_count': 0, 'failed_count': 3, 'total_count': 3, 'errors': ["Task <Task pending name='Task-3' coro=<PostingWorkflow.execute_batch_posting() running at D:\\yaoya\\Documents\\ceshishi\\yyy1 - 副本 - 副本 (9) - 副本 - 副本\\src\\modules\\posting\\workflow.py:111> cb=[_run_until_complete_cb() at D:\\python39\\lib\\asyncio\\base_events.py:184]> got Future <Future pending> attached to a different loop", "Task <Task pending name='Task-3' coro=<PostingWorkflow.execute_batch_posting() running at D:\\yaoya\\Documents\\ceshishi\\yyy1 - 副本 - 副本 (9) - 副本 - 副本\\src\\modules\\posting\\workflow.py:111> cb=[_run_until_complete_cb() at D:\\python39\\lib\\asyncio\\base_events.py:184]> got Future <Future pending> attached to a different loop", "Task <Task pending name='Task-3' coro=<PostingWorkflow.execute_batch_posting() running at D:\\yaoya\\Documents\\ceshishi\\yyy1 - 副本 - 副本 (9) - 副本 - 副本\\src\\modules\\posting\\workflow.py:111> cb=[_run_until_complete_cb() at D:\\python39\\lib\\asyncio\\base_events.py:184]> got Future <Future pending> attached to a different loop"]}
2025-08-01 00:12:40 | ERROR    | src.modules.posting.executor:execute_post:828 | 发帖失败: CharleneCa2057, Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\workflow.py:111> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 00:12:40 | ERROR    | src.modules.posting.executor:execute_post:828 | 发帖失败: EWebb32471, Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\workflow.py:111> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 00:12:40 | ERROR    | src.modules.posting.executor:execute_post:828 | 发帖失败: MercedesFl59831, Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\workflow.py:111> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 00:12:46 | ERROR    | src.ui.widgets.posting_widget:on_posting_completed:639 | 发帖失败详情: {'success_count': 0, 'failed_count': 3, 'total_count': 3, 'errors': ["Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\\yaoya\\Documents\\ceshishi\\yyy1 - 副本 - 副本 (9) - 副本 - 副本\\src\\modules\\posting\\workflow.py:111> cb=[_run_until_complete_cb() at D:\\python39\\lib\\asyncio\\base_events.py:184]> got Future <Future pending> attached to a different loop", "Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\\yaoya\\Documents\\ceshishi\\yyy1 - 副本 - 副本 (9) - 副本 - 副本\\src\\modules\\posting\\workflow.py:111> cb=[_run_until_complete_cb() at D:\\python39\\lib\\asyncio\\base_events.py:184]> got Future <Future pending> attached to a different loop", "Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\\yaoya\\Documents\\ceshishi\\yyy1 - 副本 - 副本 (9) - 副本 - 副本\\src\\modules\\posting\\workflow.py:111> cb=[_run_until_complete_cb() at D:\\python39\\lib\\asyncio\\base_events.py:184]> got Future <Future pending> attached to a different loop"]}
2025-08-01 00:17:54 | ERROR    | src.modules.posting.executor:execute_post:828 | 发帖失败: CharleneCa2057, Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\workflow.py:111> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 00:17:54 | ERROR    | src.modules.posting.executor:execute_post:828 | 发帖失败: EWebb32471, Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\workflow.py:111> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 00:17:54 | ERROR    | src.modules.posting.executor:execute_post:828 | 发帖失败: MercedesFl59831, Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\workflow.py:111> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 00:17:59 | ERROR    | src.ui.widgets.posting_widget:on_posting_completed:683 | 发帖失败详情: {'success_count': 0, 'failed_count': 3, 'total_count': 3, 'errors': ["Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\\yaoya\\Documents\\ceshishi\\yyy1 - 副本 - 副本 (9) - 副本 - 副本\\src\\modules\\posting\\workflow.py:111> cb=[_run_until_complete_cb() at D:\\python39\\lib\\asyncio\\base_events.py:184]> got Future <Future pending> attached to a different loop", "Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\\yaoya\\Documents\\ceshishi\\yyy1 - 副本 - 副本 (9) - 副本 - 副本\\src\\modules\\posting\\workflow.py:111> cb=[_run_until_complete_cb() at D:\\python39\\lib\\asyncio\\base_events.py:184]> got Future <Future pending> attached to a different loop", "Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\\yaoya\\Documents\\ceshishi\\yyy1 - 副本 - 副本 (9) - 副本 - 副本\\src\\modules\\posting\\workflow.py:111> cb=[_run_until_complete_cb() at D:\\python39\\lib\\asyncio\\base_events.py:184]> got Future <Future pending> attached to a different loop"]}
2025-08-01 00:28:11 | ERROR    | src.modules.posting.executor:execute_post:829 | 发帖失败: CharleneCa2057, Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\workflow.py:111> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 00:28:11 | ERROR    | src.modules.posting.executor:execute_post:829 | 发帖失败: EWebb32471, Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\workflow.py:111> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 00:28:11 | ERROR    | src.modules.posting.executor:execute_post:829 | 发帖失败: MercedesFl59831, Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\workflow.py:111> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 00:28:13 | ERROR    | src.ui.widgets.posting_widget:on_posting_completed:683 | 发帖失败详情: {'success_count': 0, 'failed_count': 3, 'total_count': 3, 'errors': ["Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\\yaoya\\Documents\\ceshishi\\yyy1 - 副本 - 副本 (9) - 副本 - 副本\\src\\modules\\posting\\workflow.py:111> cb=[_run_until_complete_cb() at D:\\python39\\lib\\asyncio\\base_events.py:184]> got Future <Future pending> attached to a different loop", "Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\\yaoya\\Documents\\ceshishi\\yyy1 - 副本 - 副本 (9) - 副本 - 副本\\src\\modules\\posting\\workflow.py:111> cb=[_run_until_complete_cb() at D:\\python39\\lib\\asyncio\\base_events.py:184]> got Future <Future pending> attached to a different loop", "Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\\yaoya\\Documents\\ceshishi\\yyy1 - 副本 - 副本 (9) - 副本 - 副本\\src\\modules\\posting\\workflow.py:111> cb=[_run_until_complete_cb() at D:\\python39\\lib\\asyncio\\base_events.py:184]> got Future <Future pending> attached to a different loop"]}
2025-08-01 00:29:06 | ERROR    | src.modules.posting.executor:execute_post:829 | 发帖失败: CharleneCa2057, Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\workflow.py:111> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 00:29:06 | ERROR    | src.modules.posting.executor:execute_post:829 | 发帖失败: EWebb32471, Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\workflow.py:111> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 00:29:06 | ERROR    | src.modules.posting.executor:execute_post:829 | 发帖失败: MercedesFl59831, Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\workflow.py:111> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 00:29:18 | ERROR    | src.ui.widgets.posting_widget:on_posting_completed:683 | 发帖失败详情: {'success_count': 0, 'failed_count': 3, 'total_count': 3, 'errors': ["Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\\yaoya\\Documents\\ceshishi\\yyy1 - 副本 - 副本 (9) - 副本 - 副本\\src\\modules\\posting\\workflow.py:111> cb=[_run_until_complete_cb() at D:\\python39\\lib\\asyncio\\base_events.py:184]> got Future <Future pending> attached to a different loop", "Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\\yaoya\\Documents\\ceshishi\\yyy1 - 副本 - 副本 (9) - 副本 - 副本\\src\\modules\\posting\\workflow.py:111> cb=[_run_until_complete_cb() at D:\\python39\\lib\\asyncio\\base_events.py:184]> got Future <Future pending> attached to a different loop", "Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\\yaoya\\Documents\\ceshishi\\yyy1 - 副本 - 副本 (9) - 副本 - 副本\\src\\modules\\posting\\workflow.py:111> cb=[_run_until_complete_cb() at D:\\python39\\lib\\asyncio\\base_events.py:184]> got Future <Future pending> attached to a different loop"]}
2025-08-01 00:31:24 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:602 | Chrome启动彻底失败: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-01 00:33:25 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:602 | Chrome启动彻底失败: Message: session not created: cannot connect to chrome at 127.0.0.1:6737
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x7ff7cfc3e415+77285]
	GetHandleVerifier [0x0x7ff7cfc3e470+77376]
	(No symbol) [0x0x7ff7cfa0989c]
	(No symbol) [0x0x7ff7cf9fab71]
	(No symbol) [0x0x7ff7cfa4d0c2]
	(No symbol) [0x0x7ff7cfa41eb9]
	(No symbol) [0x0x7ff7cfa9602e]
	(No symbol) [0x0x7ff7cfa957c0]
	(No symbol) [0x0x7ff7cfa883e3]
	(No symbol) [0x0x7ff7cfa51521]
	(No symbol) [0x0x7ff7cfa522b3]
	GetHandleVerifier [0x0x7ff7cff21efd+3107021]
	GetHandleVerifier [0x0x7ff7cff1c29d+3083373]
	GetHandleVerifier [0x0x7ff7cff3bedd+3213485]
	GetHandleVerifier [0x0x7ff7cfc5884e+184862]
	GetHandleVerifier [0x0x7ff7cfc6055f+216879]
	GetHandleVerifier [0x0x7ff7cfc47084+113236]
	GetHandleVerifier [0x0x7ff7cfc47239+113673]
	GetHandleVerifier [0x0x7ff7cfc2e298+11368]
	BaseThreadInitThunk [0x0x7ffe2c43e8d7+23]
	RtlUserThreadStart [0x0x7ffe2ccfc34c+44]

2025-08-01 01:08:00 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:602 | Chrome启动彻底失败: Message: session not created: cannot connect to chrome at 127.0.0.1:3536
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x7ff7cfc3e415+77285]
	GetHandleVerifier [0x0x7ff7cfc3e470+77376]
	(No symbol) [0x0x7ff7cfa0989c]
	(No symbol) [0x0x7ff7cf9fab71]
	(No symbol) [0x0x7ff7cfa4d0c2]
	(No symbol) [0x0x7ff7cfa41eb9]
	(No symbol) [0x0x7ff7cfa9602e]
	(No symbol) [0x0x7ff7cfa957c0]
	(No symbol) [0x0x7ff7cfa883e3]
	(No symbol) [0x0x7ff7cfa51521]
	(No symbol) [0x0x7ff7cfa522b3]
	GetHandleVerifier [0x0x7ff7cff21efd+3107021]
	GetHandleVerifier [0x0x7ff7cff1c29d+3083373]
	GetHandleVerifier [0x0x7ff7cff3bedd+3213485]
	GetHandleVerifier [0x0x7ff7cfc5884e+184862]
	GetHandleVerifier [0x0x7ff7cfc6055f+216879]
	GetHandleVerifier [0x0x7ff7cfc47084+113236]
	GetHandleVerifier [0x0x7ff7cfc47239+113673]
	GetHandleVerifier [0x0x7ff7cfc2e298+11368]
	BaseThreadInitThunk [0x0x7ffe2c43e8d7+23]
	RtlUserThreadStart [0x0x7ffe2ccfc34c+44]

2025-08-01 01:22:10 | ERROR    | src.core.browser_manager:close_all:1346 | 关闭WebDriver失败: 'BrowserPool' object has no attribute 'page_pool'
2025-08-01 01:22:10 | ERROR    | src.core.browser_manager:close_all:1346 | 关闭WebDriver失败: 'BrowserPool' object has no attribute 'page_pool'
2025-08-01 01:28:47 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:602 | Chrome启动彻底失败: Message: session not created: cannot connect to chrome at 127.0.0.1:11179
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x7ff7cfc3e415+77285]
	GetHandleVerifier [0x0x7ff7cfc3e470+77376]
	(No symbol) [0x0x7ff7cfa0989c]
	(No symbol) [0x0x7ff7cf9fab71]
	(No symbol) [0x0x7ff7cfa4d0c2]
	(No symbol) [0x0x7ff7cfa41eb9]
	(No symbol) [0x0x7ff7cfa9602e]
	(No symbol) [0x0x7ff7cfa957c0]
	(No symbol) [0x0x7ff7cfa883e3]
	(No symbol) [0x0x7ff7cfa51521]
	(No symbol) [0x0x7ff7cfa522b3]
	GetHandleVerifier [0x0x7ff7cff21efd+3107021]
	GetHandleVerifier [0x0x7ff7cff1c29d+3083373]
	GetHandleVerifier [0x0x7ff7cff3bedd+3213485]
	GetHandleVerifier [0x0x7ff7cfc5884e+184862]
	GetHandleVerifier [0x0x7ff7cfc6055f+216879]
	GetHandleVerifier [0x0x7ff7cfc47084+113236]
	GetHandleVerifier [0x0x7ff7cfc47239+113673]
	GetHandleVerifier [0x0x7ff7cfc2e298+11368]
	BaseThreadInitThunk [0x0x7ffe2c43e8d7+23]
	RtlUserThreadStart [0x0x7ffe2ccfc34c+44]

2025-08-01 01:42:19 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:602 | Chrome启动彻底失败: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-01 01:45:47 | ERROR    | src.modules.posting.executor:execute_post:828 | 发帖失败: CharleneCa2057, Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\workflow.py:128> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 01:45:47 | ERROR    | src.modules.posting.executor:execute_post:828 | 发帖失败: EWebb32471, Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\workflow.py:128> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 01:45:47 | ERROR    | src.modules.posting.executor:execute_post:828 | 发帖失败: MercedesFl59831, Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\workflow.py:128> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 01:46:19 | ERROR    | src.modules.posting.executor:execute_post:828 | 发帖失败: CharleneCa2057, Task <Task pending name='Task-3' coro=<PostingWorkflow.execute_batch_posting() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\workflow.py:128> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 01:46:19 | ERROR    | src.modules.posting.executor:execute_post:828 | 发帖失败: EWebb32471, Task <Task pending name='Task-3' coro=<PostingWorkflow.execute_batch_posting() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\workflow.py:128> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 01:46:19 | ERROR    | src.modules.posting.executor:execute_post:828 | 发帖失败: MercedesFl59831, Task <Task pending name='Task-3' coro=<PostingWorkflow.execute_batch_posting() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\workflow.py:128> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 01:52:01 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:602 | Chrome启动彻底失败: Message: session not created: cannot connect to chrome at 127.0.0.1:3381
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x7ff7cfc3e415+77285]
	GetHandleVerifier [0x0x7ff7cfc3e470+77376]
	(No symbol) [0x0x7ff7cfa0989c]
	(No symbol) [0x0x7ff7cf9fab71]
	(No symbol) [0x0x7ff7cfa4d0c2]
	(No symbol) [0x0x7ff7cfa41eb9]
	(No symbol) [0x0x7ff7cfa9602e]
	(No symbol) [0x0x7ff7cfa957c0]
	(No symbol) [0x0x7ff7cfa883e3]
	(No symbol) [0x0x7ff7cfa51521]
	(No symbol) [0x0x7ff7cfa522b3]
	GetHandleVerifier [0x0x7ff7cff21efd+3107021]
	GetHandleVerifier [0x0x7ff7cff1c29d+3083373]
	GetHandleVerifier [0x0x7ff7cff3bedd+3213485]
	GetHandleVerifier [0x0x7ff7cfc5884e+184862]
	GetHandleVerifier [0x0x7ff7cfc6055f+216879]
	GetHandleVerifier [0x0x7ff7cfc47084+113236]
	GetHandleVerifier [0x0x7ff7cfc47239+113673]
	GetHandleVerifier [0x0x7ff7cfc2e298+11368]
	BaseThreadInitThunk [0x0x7ffe2c43e8d7+23]
	RtlUserThreadStart [0x0x7ffe2ccfc34c+44]

2025-08-01 01:52:49 | ERROR    | src.core.browser_manager:_fast_create_driver:681 | 快速创建WebDriver失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 01:52:52 | ERROR    | src.core.browser_manager:_create_driver:449 | 创建WebDriver失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 01:52:54 | ERROR    | src.core.browser_manager:_create_driver:449 | 创建WebDriver失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 01:52:54 | ERROR    | src.core.browser_manager:get_driver:302 | 创建WebDriver最终失败: CharleneCa2057
2025-08-01 01:52:54 | ERROR    | src.modules.posting.executor:execute_post:828 | 发帖失败: CharleneCa2057, 获取WebDriver失败
2025-08-01 01:52:55 | ERROR    | src.core.browser_manager:_fast_create_driver:681 | 快速创建WebDriver失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 01:52:58 | ERROR    | src.core.browser_manager:_create_driver:449 | 创建WebDriver失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 01:53:00 | ERROR    | src.core.browser_manager:_create_driver:449 | 创建WebDriver失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 01:53:00 | ERROR    | src.core.browser_manager:get_driver:302 | 创建WebDriver最终失败: EWebb32471
2025-08-01 01:53:00 | ERROR    | src.modules.posting.executor:execute_post:828 | 发帖失败: EWebb32471, 获取WebDriver失败
2025-08-01 01:53:01 | ERROR    | src.core.browser_manager:_fast_create_driver:681 | 快速创建WebDriver失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 01:53:04 | ERROR    | src.core.browser_manager:_create_driver:449 | 创建WebDriver失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 01:53:06 | ERROR    | src.core.browser_manager:_create_driver:449 | 创建WebDriver失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 01:53:06 | ERROR    | src.core.browser_manager:get_driver:302 | 创建WebDriver最终失败: MercedesFl59831
2025-08-01 01:53:06 | ERROR    | src.modules.posting.executor:execute_post:828 | 发帖失败: MercedesFl59831, 获取WebDriver失败
2025-08-01 01:53:27 | ERROR    | src.core.browser_manager:_fast_create_driver:681 | 快速创建WebDriver失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 01:53:29 | ERROR    | src.core.browser_manager:_create_driver:449 | 创建WebDriver失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 01:53:32 | ERROR    | src.core.browser_manager:_create_driver:449 | 创建WebDriver失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 01:53:32 | ERROR    | src.core.browser_manager:get_driver:302 | 创建WebDriver最终失败: CharleneCa2057
2025-08-01 01:53:32 | ERROR    | src.modules.posting.executor:execute_post:828 | 发帖失败: CharleneCa2057, 获取WebDriver失败
2025-08-01 01:54:30 | ERROR    | src.core.browser_manager:_fast_create_driver:681 | 快速创建WebDriver失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 01:54:36 | ERROR    | src.modules.posting.executor:execute_post:828 | 发帖失败: CharleneCa2057, Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\workflow.py:128> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 01:54:36 | ERROR    | src.modules.posting.executor:execute_post:828 | 发帖失败: EWebb32471, Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\workflow.py:128> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 01:54:36 | ERROR    | src.modules.posting.executor:execute_post:828 | 发帖失败: MercedesFl59831, Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\workflow.py:128> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 02:02:00 | ERROR    | src.modules.posting.executor:execute_post:828 | 发帖失败: CharleneCa2057, Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\workflow.py:128> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 02:02:00 | ERROR    | src.modules.posting.executor:execute_post:828 | 发帖失败: EWebb32471, Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\workflow.py:128> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 02:02:00 | ERROR    | src.modules.posting.executor:execute_post:828 | 发帖失败: MercedesFl59831, Task <Task pending name='Task-2' coro=<PostingWorkflow.execute_batch_posting() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\workflow.py:128> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 02:02:08 | ERROR    | src.modules.posting.executor:execute_post:828 | 发帖失败: CharleneCa2057, Task <Task pending name='Task-3' coro=<PostingWorkflow.execute_batch_posting() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\workflow.py:128> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 02:02:08 | ERROR    | src.modules.posting.executor:execute_post:828 | 发帖失败: EWebb32471, Task <Task pending name='Task-3' coro=<PostingWorkflow.execute_batch_posting() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\workflow.py:128> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 02:02:08 | ERROR    | src.modules.posting.executor:execute_post:828 | 发帖失败: MercedesFl59831, Task <Task pending name='Task-3' coro=<PostingWorkflow.execute_batch_posting() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\workflow.py:128> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 02:06:16 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:625 | Chrome启动彻底失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 02:11:33 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:642 | Chrome启动彻底失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 03:04:29 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:667 | Chrome启动彻底失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 03:15:00 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:642 | Chrome启动彻底失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 03:19:20 | ERROR    | src.modules.posting.executor:login_account:427 | 登录异常: AnthonyGon78058 (尝试 1/3), Task <Task pending name='Task-175' coro=<ConcurrentAccountLoginThread.run.<locals>.concurrent_login_accounts.<locals>.login_single_account() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py:231> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-08-01 03:19:20 | ERROR    | src.modules.posting.executor:login_account:427 | 登录异常: RAnderson1880 (尝试 1/3), Task <Task pending name='Task-176' coro=<ConcurrentAccountLoginThread.run.<locals>.concurrent_login_accounts.<locals>.login_single_account() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py:231> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-08-01 03:19:26 | ERROR    | src.modules.posting.executor:login_account:427 | 登录异常: AnthonyGon78058 (尝试 2/3), Task <Task pending name='Task-175' coro=<ConcurrentAccountLoginThread.run.<locals>.concurrent_login_accounts.<locals>.login_single_account() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py:231> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-08-01 03:19:27 | ERROR    | src.modules.posting.executor:login_account:427 | 登录异常: RAnderson1880 (尝试 2/3), Task <Task pending name='Task-176' coro=<ConcurrentAccountLoginThread.run.<locals>.concurrent_login_accounts.<locals>.login_single_account() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py:231> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-08-01 03:19:31 | ERROR    | src.modules.posting.executor:login_account:427 | 登录异常: AnthonyGon78058 (尝试 3/3), Task <Task pending name='Task-175' coro=<ConcurrentAccountLoginThread.run.<locals>.concurrent_login_accounts.<locals>.login_single_account() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py:231> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-08-01 03:19:34 | ERROR    | src.modules.posting.executor:login_account:427 | 登录异常: RAnderson1880 (尝试 3/3), Task <Task pending name='Task-176' coro=<ConcurrentAccountLoginThread.run.<locals>.concurrent_login_accounts.<locals>.login_single_account() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py:231> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-08-01 03:20:32 | ERROR    | src.modules.posting.executor:login_account:427 | 登录异常: JohnRamire36615 (尝试 1/3), Message: Driver已关闭

2025-08-01 03:20:42 | ERROR    | src.core.browser_manager:_fast_create_driver:698 | 快速创建WebDriver失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 03:29:55 | ERROR    | src.modules.posting.executor:login_account:445 | 登录异常: JohnRamire36615 (尝试 1/3), 无法找到用户名输入框
2025-08-01 03:53:30 | ERROR    | src.modules.posting.executor:login_account:445 | 登录异常: EliteSylph26626 (尝试 1/3), Task <Task pending name='Task-3' coro=<ConcurrentAccountLoginThread.run.<locals>.concurrent_login_accounts.<locals>.login_single_account() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py:231> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-08-01 03:53:30 | ERROR    | src.modules.posting.executor:login_account:445 | 登录异常: RAnderson1880 (尝试 1/3), Task <Task pending name='Task-4' coro=<ConcurrentAccountLoginThread.run.<locals>.concurrent_login_accounts.<locals>.login_single_account() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py:231> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-08-01 03:53:37 | ERROR    | src.modules.posting.executor:login_account:445 | 登录异常: RAnderson1880 (尝试 2/3), Task <Task pending name='Task-4' coro=<ConcurrentAccountLoginThread.run.<locals>.concurrent_login_accounts.<locals>.login_single_account() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py:231> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-08-01 03:53:40 | ERROR    | src.modules.posting.executor:login_account:445 | 登录异常: EliteSylph26626 (尝试 2/3), Task <Task pending name='Task-3' coro=<ConcurrentAccountLoginThread.run.<locals>.concurrent_login_accounts.<locals>.login_single_account() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py:231> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-08-01 03:53:44 | ERROR    | src.modules.posting.executor:login_account:445 | 登录异常: RAnderson1880 (尝试 3/3), Task <Task pending name='Task-4' coro=<ConcurrentAccountLoginThread.run.<locals>.concurrent_login_accounts.<locals>.login_single_account() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py:231> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-08-01 03:53:48 | ERROR    | src.modules.posting.executor:login_account:445 | 登录异常: EliteSylph26626 (尝试 3/3), Task <Task pending name='Task-3' coro=<ConcurrentAccountLoginThread.run.<locals>.concurrent_login_accounts.<locals>.login_single_account() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py:231> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-08-01 04:04:54 | ERROR    | src.modules.posting.executor:login_account:445 | 登录异常: SebastianS49417 (尝试 1/3), Task <Task pending name='Task-3' coro=<ConcurrentAccountLoginThread.run.<locals>.concurrent_login_accounts.<locals>.login_single_account() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py:238> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-08-01 04:04:55 | ERROR    | src.modules.posting.executor:login_account:445 | 登录异常: RAnderson1880 (尝试 1/3), Task <Task pending name='Task-4' coro=<ConcurrentAccountLoginThread.run.<locals>.concurrent_login_accounts.<locals>.login_single_account() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py:238> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-08-01 04:05:00 | ERROR    | src.modules.posting.executor:login_account:445 | 登录异常: SebastianS49417 (尝试 2/3), Task <Task pending name='Task-3' coro=<ConcurrentAccountLoginThread.run.<locals>.concurrent_login_accounts.<locals>.login_single_account() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py:238> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-08-01 04:05:03 | ERROR    | src.modules.posting.executor:login_account:445 | 登录异常: RAnderson1880 (尝试 2/3), Task <Task pending name='Task-4' coro=<ConcurrentAccountLoginThread.run.<locals>.concurrent_login_accounts.<locals>.login_single_account() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py:238> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-08-01 04:05:10 | ERROR    | src.modules.posting.executor:login_account:445 | 登录异常: SebastianS49417 (尝试 3/3), Task <Task pending name='Task-3' coro=<ConcurrentAccountLoginThread.run.<locals>.concurrent_login_accounts.<locals>.login_single_account() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py:238> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-08-01 04:05:13 | ERROR    | src.modules.posting.executor:login_account:445 | 登录异常: RAnderson1880 (尝试 3/3), Task <Task pending name='Task-4' coro=<ConcurrentAccountLoginThread.run.<locals>.concurrent_login_accounts.<locals>.login_single_account() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py:238> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-08-01 04:42:33 | ERROR    | src.modules.posting.executor:login_account:445 | 登录异常: loves_lul49315 (尝试 1/3), Task <Task pending name='Task-6' coro=<ConcurrentAccountLoginThread.run.<locals>.concurrent_login_accounts.<locals>.login_single_account() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py:231> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-08-01 05:53:05 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:757 | Chrome启动彻底失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 05:57:58 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:771 | Chrome启动彻底失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 06:06:54 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:771 | Chrome启动彻底失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 06:08:44 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:779 | 标准Selenium也失败: [WinError 193] %1 不是有效的 Win32 应用程序。
2025-08-01 06:08:49 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:777 | Chrome启动彻底失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 06:09:09 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:801 | Chrome启动彻底失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 06:15:59 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:779 | 标准Selenium也失败: [WinError 193] %1 不是有效的 Win32 应用程序。
2025-08-01 06:17:14 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:801 | Chrome启动彻底失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 06:21:26 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:796 | 标准Selenium也失败: Message: session not created: cannot connect to chrome at 127.0.0.1:9222
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0xa0ba83+63395]
	GetHandleVerifier [0x0xa0bac4+63460]
	(No symbol) [0x0x851f70]
	(No symbol) [0x0x845f1a]
	(No symbol) [0x0x88add6]
	(No symbol) [0x0x88128f]
	(No symbol) [0x0x8810c6]
	(No symbol) [0x0x8cae57]
	(No symbol) [0x0x8ca74a]
	(No symbol) [0x0x8bf1a6]
	(No symbol) [0x0x88e7b2]
	(No symbol) [0x0x88f654]
	GetHandleVerifier [0x0xc88883+2672035]
	GetHandleVerifier [0x0xc83cba+2652634]
	GetHandleVerifier [0x0xa32bca+223466]
	GetHandleVerifier [0x0xa22cb8+158168]
	GetHandleVerifier [0x0xa2978d+185517]
	GetHandleVerifier [0x0xa13b78+96408]
	GetHandleVerifier [0x0xa13d02+96802]
	GetHandleVerifier [0x0x9fe90a+9770]
	BaseThreadInitThunk [0x0x761e5d49+25]
	RtlInitializeExceptionChain [0x0x76fad1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x76fad131+561]

2025-08-01 06:24:26 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:818 | Chrome启动彻底失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 06:28:20 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:812 | 标准Selenium也失败: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-01 06:30:31 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:825 | 标准Selenium也失败: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-01 06:30:36 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:834 | Chrome启动彻底失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 06:30:57 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:825 | 标准Selenium也失败: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-01 06:31:23 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:825 | 标准Selenium也失败: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-01 06:31:27 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:847 | Chrome启动彻底失败: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-01 06:46:48 | ERROR    | src.modules.posting.executor:_input_text_content:1556 | 输入文本内容失败: Message: element click intercepted: Element <div aria-activedescendant="typeaheadFocus-0.1183077856683914" aria-autocomplete="list" aria-controls="typeaheadDropdownWrapped-2" aria-describedby="placeholder-24vi7" aria-label="Post text" aria-multiline="true" class="notranslate public-DraftEditor-content" contenteditable="true" data-testid="tweetTextarea_0" role="textbox" spellcheck="true" tabindex="0" no-focustrapview-refocus="true" style="outline: none; user-select: text; white-space: pre-wrap; overflow-wrap: break-word;">...</div> is not clickable at point (911, 14). Other element would receive the click: <a href="/home" role="tab" aria-selected="false" tabindex="-1" class="css-175oi2r r-1awozwy r-6koalj r-eqz5dr r-16y2uox r-1h3ijdo r-1777fci r-s8bhmr r-3pj75a r-o7ynqc r-6416eg r-1ny4l3l r-1loqt21">...</a>
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x4dba83+63395]
	GetHandleVerifier [0x0x4dbac4+63460]
	(No symbol) [0x0x322113]
	(No symbol) [0x0x370ba0]
	(No symbol) [0x0x36ef5a]
	(No symbol) [0x0x36cab7]
	(No symbol) [0x0x36bd6d]
	(No symbol) [0x0x360515]
	(No symbol) [0x0x38f3ac]
	(No symbol) [0x0x35ffa4]
	(No symbol) [0x0x38f624]
	(No symbol) [0x0x3b07ba]
	(No symbol) [0x0x38f1a6]
	(No symbol) [0x0x35e7b2]
	(No symbol) [0x0x35f654]
	GetHandleVerifier [0x0x758883+2672035]
	GetHandleVerifier [0x0x753cba+2652634]
	GetHandleVerifier [0x0x502bca+223466]
	GetHandleVerifier [0x0x4f2cb8+158168]
	GetHandleVerifier [0x0x4f978d+185517]
	GetHandleVerifier [0x0x4e3b78+96408]
	GetHandleVerifier [0x0x4e3d02+96802]
	GetHandleVerifier [0x0x4ce90a+9770]
	BaseThreadInitThunk [0x0x761e5d49+25]
	RtlInitializeExceptionChain [0x0x76fad1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x76fad131+561]

2025-08-01 06:47:00 | ERROR    | src.modules.posting.executor:_input_text_content:1556 | 输入文本内容失败: Message: element click intercepted: Element <div aria-activedescendant="typeaheadFocus-0.41717419905240427" aria-autocomplete="list" aria-controls="typeaheadDropdownWrapped-1" aria-describedby="placeholder-938ap" aria-label="Post text" aria-multiline="true" class="notranslate public-DraftEditor-content" contenteditable="true" data-testid="tweetTextarea_0" role="textbox" spellcheck="true" tabindex="0" no-focustrapview-refocus="true" style="outline: none; user-select: text; white-space: pre-wrap; overflow-wrap: break-word;">...</div> is not clickable at point (911, 90). Other element would receive the click: <div class="css-175oi2r r-1p0dtai r-1d2f490 r-1xcajam r-zchlnj r-ipm5af r-1ffj0ar" data-testid="mask"></div>
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x4dba83+63395]
	GetHandleVerifier [0x0x4dbac4+63460]
	(No symbol) [0x0x322113]
	(No symbol) [0x0x370ba0]
	(No symbol) [0x0x36ef5a]
	(No symbol) [0x0x36cab7]
	(No symbol) [0x0x36bd6d]
	(No symbol) [0x0x360515]
	(No symbol) [0x0x38f3ac]
	(No symbol) [0x0x35ffa4]
	(No symbol) [0x0x38f624]
	(No symbol) [0x0x3b07ba]
	(No symbol) [0x0x38f1a6]
	(No symbol) [0x0x35e7b2]
	(No symbol) [0x0x35f654]
	GetHandleVerifier [0x0x758883+2672035]
	GetHandleVerifier [0x0x753cba+2652634]
	GetHandleVerifier [0x0x502bca+223466]
	GetHandleVerifier [0x0x4f2cb8+158168]
	GetHandleVerifier [0x0x4f978d+185517]
	GetHandleVerifier [0x0x4e3b78+96408]
	GetHandleVerifier [0x0x4e3d02+96802]
	GetHandleVerifier [0x0x4ce90a+9770]
	BaseThreadInitThunk [0x0x761e5d49+25]
	RtlInitializeExceptionChain [0x0x76fad1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x76fad131+561]

2025-08-01 06:47:29 | ERROR    | src.modules.posting.executor:execute_post:839 | ❌ 发帖失败: 发布成功但未获取到推文ID
2025-08-01 06:50:02 | ERROR    | src.core.account_manager:delete_accounts:803 | 删除账号失败: (sqlite3.IntegrityError) NOT NULL constraint failed: posting_tasks.account_id
[SQL: UPDATE posting_tasks SET account_id=? WHERE posting_tasks.id = ?]
[parameters: [(None, 355), (None, 356)]]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-01 06:50:13 | ERROR    | src.core.account_manager:delete_accounts:803 | 删除账号失败: (sqlite3.IntegrityError) NOT NULL constraint failed: posting_tasks.account_id
[SQL: UPDATE posting_tasks SET account_id=? WHERE posting_tasks.id = ?]
[parameters: [(None, 355), (None, 356)]]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-01 06:51:07 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:757 | Chrome启动彻底失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 06:52:37 | ERROR    | src.modules.posting.executor:_input_text_content:1556 | 输入文本内容失败: Message: element click intercepted: Element <div aria-activedescendant="typeaheadFocus-0.****************" aria-autocomplete="list" aria-controls="typeaheadDropdownWrapped-2" aria-describedby="placeholder-8r1hu" aria-label="Post text" aria-multiline="true" class="notranslate public-DraftEditor-content" contenteditable="true" data-testid="tweetTextarea_0" role="textbox" spellcheck="true" tabindex="0" no-focustrapview-refocus="true" style="outline: none; user-select: text; white-space: pre-wrap; overflow-wrap: break-word;">...</div> is not clickable at point (911, 90). Other element would receive the click: <div class="css-175oi2r r-1p0dtai r-1d2f490 r-1xcajam r-zchlnj r-ipm5af r-1ffj0ar" data-testid="mask"></div>
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x32ba83+63395]
	GetHandleVerifier [0x0x32bac4+63460]
	(No symbol) [0x0x172113]
	(No symbol) [0x0x1c0ba0]
	(No symbol) [0x0x1bef5a]
	(No symbol) [0x0x1bcab7]
	(No symbol) [0x0x1bbd6d]
	(No symbol) [0x0x1b0515]
	(No symbol) [0x0x1df3ac]
	(No symbol) [0x0x1affa4]
	(No symbol) [0x0x1df624]
	(No symbol) [0x0x2007ba]
	(No symbol) [0x0x1df1a6]
	(No symbol) [0x0x1ae7b2]
	(No symbol) [0x0x1af654]
	GetHandleVerifier [0x0x5a8883+2672035]
	GetHandleVerifier [0x0x5a3cba+2652634]
	GetHandleVerifier [0x0x352bca+223466]
	GetHandleVerifier [0x0x342cb8+158168]
	GetHandleVerifier [0x0x34978d+185517]
	GetHandleVerifier [0x0x333b78+96408]
	GetHandleVerifier [0x0x333d02+96802]
	GetHandleVerifier [0x0x31e90a+9770]
	BaseThreadInitThunk [0x0x761e5d49+25]
	RtlInitializeExceptionChain [0x0x76fad1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x76fad131+561]
	(No symbol) [0x0]

2025-08-01 06:54:08 | ERROR    | src.modules.interaction.executor:_execute_retweet:218 | 执行转发失败: Message: 

2025-08-01 06:54:51 | ERROR    | src.modules.interaction.executor:_execute_retweet:218 | 执行转发失败: Message: 

2025-08-01 06:55:04 | ERROR    | src.modules.interaction.executor:_execute_comment:264 | 执行评论失败: Message: element click intercepted: Element <div aria-activedescendant="typeaheadFocus-0.8365614448288159" aria-autocomplete="list" aria-controls="typeaheadDropdownWrapped-1" aria-describedby="placeholder-etsk6" aria-label="Post text" aria-multiline="true" class="notranslate public-DraftEditor-content" contenteditable="true" data-testid="tweetTextarea_0" role="textbox" spellcheck="true" tabindex="0" no-focustrapview-refocus="true" style="outline: none; user-select: text; white-space: pre-wrap; overflow-wrap: break-word;">...</div> is not clickable at point (868, 679). Other element would receive the click: <div class="css-175oi2r r-1p0dtai r-1d2f490 r-1xcajam r-zchlnj r-ipm5af r-1ffj0ar" data-testid="mask"></div>
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x32ba83+63395]
	GetHandleVerifier [0x0x32bac4+63460]
	(No symbol) [0x0x172113]
	(No symbol) [0x0x1c0ba0]
	(No symbol) [0x0x1bef5a]
	(No symbol) [0x0x1bcab7]
	(No symbol) [0x0x1bbd6d]
	(No symbol) [0x0x1b0515]
	(No symbol) [0x0x1df3ac]
	(No symbol) [0x0x1affa4]
	(No symbol) [0x0x1df624]
	(No symbol) [0x0x2007ba]
	(No symbol) [0x0x1df1a6]
	(No symbol) [0x0x1ae7b2]
	(No symbol) [0x0x1af654]
	GetHandleVerifier [0x0x5a8883+2672035]
	GetHandleVerifier [0x0x5a3cba+2652634]
	GetHandleVerifier [0x0x352bca+223466]
	GetHandleVerifier [0x0x342cb8+158168]
	GetHandleVerifier [0x0x34978d+185517]
	GetHandleVerifier [0x0x333b78+96408]
	GetHandleVerifier [0x0x333d02+96802]
	GetHandleVerifier [0x0x31e90a+9770]
	BaseThreadInitThunk [0x0x761e5d49+25]
	RtlInitializeExceptionChain [0x0x76fad1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x76fad131+561]
	(No symbol) [0x0]

2025-08-01 06:55:20 | ERROR    | src.modules.interaction.executor:_execute_comment:264 | 执行评论失败: Message: 
Stacktrace:
	GetHandleVerifier [0x0x32ba83+63395]
	GetHandleVerifier [0x0x32bac4+63460]
	(No symbol) [0x0x172113]
	(No symbol) [0x0x1ba85e]
	(No symbol) [0x0x1babfb]
	(No symbol) [0x0x202f92]
	(No symbol) [0x0x1df3f4]
	(No symbol) [0x0x2007ba]
	(No symbol) [0x0x1df1a6]
	(No symbol) [0x0x1ae7b2]
	(No symbol) [0x0x1af654]
	GetHandleVerifier [0x0x5a8883+2672035]
	GetHandleVerifier [0x0x5a3cba+2652634]
	GetHandleVerifier [0x0x352bca+223466]
	GetHandleVerifier [0x0x342cb8+158168]
	GetHandleVerifier [0x0x34978d+185517]
	GetHandleVerifier [0x0x333b78+96408]
	GetHandleVerifier [0x0x333d02+96802]
	GetHandleVerifier [0x0x31e90a+9770]
	BaseThreadInitThunk [0x0x761e5d49+25]
	RtlInitializeExceptionChain [0x0x76fad1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x76fad131+561]
	(No symbol) [0x0]

2025-08-01 16:29:16 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:757 | Chrome启动彻底失败: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-01 16:29:17 | ERROR    | src.core.browser_manager:_fast_create_driver:820 | 快速创建WebDriver失败: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-01 16:30:35 | ERROR    | src.core.browser_manager:create_driver:515 | Chrome创建失败: Message: session not created: cannot connect to chrome at 127.0.0.1:37056
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x6aba83+63395]
	GetHandleVerifier [0x0x6abac4+63460]
	(No symbol) [0x0x4f1f70]
	(No symbol) [0x0x4e5f1a]
	(No symbol) [0x0x52add6]
	(No symbol) [0x0x52128f]
	(No symbol) [0x0x5210c6]
	(No symbol) [0x0x56ae57]
	(No symbol) [0x0x56a74a]
	(No symbol) [0x0x55f1a6]
	(No symbol) [0x0x52e7b2]
	(No symbol) [0x0x52f654]
	GetHandleVerifier [0x0x928883+2672035]
	GetHandleVerifier [0x0x923cba+2652634]
	GetHandleVerifier [0x0x6d2bca+223466]
	GetHandleVerifier [0x0x6c2cb8+158168]
	GetHandleVerifier [0x0x6c978d+185517]
	GetHandleVerifier [0x0x6b3b78+96408]
	GetHandleVerifier [0x0x6b3d02+96802]
	GetHandleVerifier [0x0x69e90a+9770]
	BaseThreadInitThunk [0x0x761e5d49+25]
	RtlInitializeExceptionChain [0x0x76fad1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x76fad131+561]

2025-08-01 16:30:35 | ERROR    | src.core.browser_manager:_create_driver:557 | 创建WebDriver失败: Message: session not created: cannot connect to chrome at 127.0.0.1:37056
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x6aba83+63395]
	GetHandleVerifier [0x0x6abac4+63460]
	(No symbol) [0x0x4f1f70]
	(No symbol) [0x0x4e5f1a]
	(No symbol) [0x0x52add6]
	(No symbol) [0x0x52128f]
	(No symbol) [0x0x5210c6]
	(No symbol) [0x0x56ae57]
	(No symbol) [0x0x56a74a]
	(No symbol) [0x0x55f1a6]
	(No symbol) [0x0x52e7b2]
	(No symbol) [0x0x52f654]
	GetHandleVerifier [0x0x928883+2672035]
	GetHandleVerifier [0x0x923cba+2652634]
	GetHandleVerifier [0x0x6d2bca+223466]
	GetHandleVerifier [0x0x6c2cb8+158168]
	GetHandleVerifier [0x0x6c978d+185517]
	GetHandleVerifier [0x0x6b3b78+96408]
	GetHandleVerifier [0x0x6b3d02+96802]
	GetHandleVerifier [0x0x69e90a+9770]
	BaseThreadInitThunk [0x0x761e5d49+25]
	RtlInitializeExceptionChain [0x0x76fad1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x76fad131+561]

2025-08-01 16:31:54 | ERROR    | src.core.browser_manager:create_driver:515 | Chrome创建失败: Message: session not created: cannot connect to chrome at 127.0.0.1:3861
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x6aba83+63395]
	GetHandleVerifier [0x0x6abac4+63460]
	(No symbol) [0x0x4f1f70]
	(No symbol) [0x0x4e5f1a]
	(No symbol) [0x0x52add6]
	(No symbol) [0x0x52128f]
	(No symbol) [0x0x5210c6]
	(No symbol) [0x0x56ae57]
	(No symbol) [0x0x56a74a]
	(No symbol) [0x0x55f1a6]
	(No symbol) [0x0x52e7b2]
	(No symbol) [0x0x52f654]
	GetHandleVerifier [0x0x928883+2672035]
	GetHandleVerifier [0x0x923cba+2652634]
	GetHandleVerifier [0x0x6d2bca+223466]
	GetHandleVerifier [0x0x6c2cb8+158168]
	GetHandleVerifier [0x0x6c978d+185517]
	GetHandleVerifier [0x0x6b3b78+96408]
	GetHandleVerifier [0x0x6b3d02+96802]
	GetHandleVerifier [0x0x69e90a+9770]
	BaseThreadInitThunk [0x0x761e5d49+25]
	RtlInitializeExceptionChain [0x0x76fad1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x76fad131+561]

2025-08-01 16:31:54 | ERROR    | src.core.browser_manager:_create_driver:557 | 创建WebDriver失败: Message: session not created: cannot connect to chrome at 127.0.0.1:3861
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x6aba83+63395]
	GetHandleVerifier [0x0x6abac4+63460]
	(No symbol) [0x0x4f1f70]
	(No symbol) [0x0x4e5f1a]
	(No symbol) [0x0x52add6]
	(No symbol) [0x0x52128f]
	(No symbol) [0x0x5210c6]
	(No symbol) [0x0x56ae57]
	(No symbol) [0x0x56a74a]
	(No symbol) [0x0x55f1a6]
	(No symbol) [0x0x52e7b2]
	(No symbol) [0x0x52f654]
	GetHandleVerifier [0x0x928883+2672035]
	GetHandleVerifier [0x0x923cba+2652634]
	GetHandleVerifier [0x0x6d2bca+223466]
	GetHandleVerifier [0x0x6c2cb8+158168]
	GetHandleVerifier [0x0x6c978d+185517]
	GetHandleVerifier [0x0x6b3b78+96408]
	GetHandleVerifier [0x0x6b3d02+96802]
	GetHandleVerifier [0x0x69e90a+9770]
	BaseThreadInitThunk [0x0x761e5d49+25]
	RtlInitializeExceptionChain [0x0x76fad1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x76fad131+561]

2025-08-01 16:31:54 | ERROR    | src.core.browser_manager:get_driver:401 | 创建WebDriver最终失败: PaigeBaldw49027
2025-08-01 16:31:54 | ERROR    | src.core.account_status_manager:get_or_create_driver:160 | 获取WebDriver失败 PaigeBaldw49027: 无法获取WebDriver
2025-08-01 16:31:54 | ERROR    | src.core.account_status_manager:check_account_status:80 | 检查账号状态失败 PaigeBaldw49027: 无法获取WebDriver
2025-08-01 17:05:20 | ERROR    | src.core.account_status_manager:_check_login_status:227 | 检查登录状态失败 EWebb32471: HTTPConnectionPool(host='localhost', port=29240): Max retries exceeded with url: /session/ef93147abd3dde3e00cbd4471cae37db/execute/sync (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000022101051250>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-08-01 17:06:59 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:757 | Chrome启动彻底失败: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-01 17:06:59 | ERROR    | src.core.browser_manager:_fast_create_driver:820 | 快速创建WebDriver失败: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-01 17:08:18 | ERROR    | src.core.browser_manager:create_driver:515 | Chrome创建失败: Message: session not created: cannot connect to chrome at 127.0.0.1:30085
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0xe0ba83+63395]
	GetHandleVerifier [0x0xe0bac4+63460]
	(No symbol) [0x0xc51f70]
	(No symbol) [0x0xc45f1a]
	(No symbol) [0x0xc8add6]
	(No symbol) [0x0xc8128f]
	(No symbol) [0x0xc810c6]
	(No symbol) [0x0xccae57]
	(No symbol) [0x0xcca74a]
	(No symbol) [0x0xcbf1a6]
	(No symbol) [0x0xc8e7b2]
	(No symbol) [0x0xc8f654]
	GetHandleVerifier [0x0x1088883+2672035]
	GetHandleVerifier [0x0x1083cba+2652634]
	GetHandleVerifier [0x0xe32bca+223466]
	GetHandleVerifier [0x0xe22cb8+158168]
	GetHandleVerifier [0x0xe2978d+185517]
	GetHandleVerifier [0x0xe13b78+96408]
	GetHandleVerifier [0x0xe13d02+96802]
	GetHandleVerifier [0x0xdfe90a+9770]
	BaseThreadInitThunk [0x0x761e5d49+25]
	RtlInitializeExceptionChain [0x0x76fad1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x76fad131+561]

2025-08-01 17:08:18 | ERROR    | src.core.browser_manager:_create_driver:557 | 创建WebDriver失败: Message: session not created: cannot connect to chrome at 127.0.0.1:30085
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0xe0ba83+63395]
	GetHandleVerifier [0x0xe0bac4+63460]
	(No symbol) [0x0xc51f70]
	(No symbol) [0x0xc45f1a]
	(No symbol) [0x0xc8add6]
	(No symbol) [0x0xc8128f]
	(No symbol) [0x0xc810c6]
	(No symbol) [0x0xccae57]
	(No symbol) [0x0xcca74a]
	(No symbol) [0x0xcbf1a6]
	(No symbol) [0x0xc8e7b2]
	(No symbol) [0x0xc8f654]
	GetHandleVerifier [0x0x1088883+2672035]
	GetHandleVerifier [0x0x1083cba+2652634]
	GetHandleVerifier [0x0xe32bca+223466]
	GetHandleVerifier [0x0xe22cb8+158168]
	GetHandleVerifier [0x0xe2978d+185517]
	GetHandleVerifier [0x0xe13b78+96408]
	GetHandleVerifier [0x0xe13d02+96802]
	GetHandleVerifier [0x0xdfe90a+9770]
	BaseThreadInitThunk [0x0x761e5d49+25]
	RtlInitializeExceptionChain [0x0x76fad1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x76fad131+561]

2025-08-01 17:08:37 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:757 | Chrome启动彻底失败: Message: session not created: cannot connect to chrome at 127.0.0.1:29788
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x7ff7cfc3e415+77285]
	GetHandleVerifier [0x0x7ff7cfc3e470+77376]
	(No symbol) [0x0x7ff7cfa0989c]
	(No symbol) [0x0x7ff7cf9fab71]
	(No symbol) [0x0x7ff7cfa4d0c2]
	(No symbol) [0x0x7ff7cfa41eb9]
	(No symbol) [0x0x7ff7cfa9602e]
	(No symbol) [0x0x7ff7cfa957c0]
	(No symbol) [0x0x7ff7cfa883e3]
	(No symbol) [0x0x7ff7cfa51521]
	(No symbol) [0x0x7ff7cfa522b3]
	GetHandleVerifier [0x0x7ff7cff21efd+3107021]
	GetHandleVerifier [0x0x7ff7cff1c29d+3083373]
	GetHandleVerifier [0x0x7ff7cff3bedd+3213485]
	GetHandleVerifier [0x0x7ff7cfc5884e+184862]
	GetHandleVerifier [0x0x7ff7cfc6055f+216879]
	GetHandleVerifier [0x0x7ff7cfc47084+113236]
	GetHandleVerifier [0x0x7ff7cfc47239+113673]
	GetHandleVerifier [0x0x7ff7cfc2e298+11368]
	BaseThreadInitThunk [0x0x7ffe2c43e8d7+23]
	RtlUserThreadStart [0x0x7ffe2ccfc34c+44]

2025-08-01 17:08:37 | ERROR    | src.core.browser_manager:_fast_create_driver:820 | 快速创建WebDriver失败: Message: session not created: cannot connect to chrome at 127.0.0.1:29788
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x7ff7cfc3e415+77285]
	GetHandleVerifier [0x0x7ff7cfc3e470+77376]
	(No symbol) [0x0x7ff7cfa0989c]
	(No symbol) [0x0x7ff7cf9fab71]
	(No symbol) [0x0x7ff7cfa4d0c2]
	(No symbol) [0x0x7ff7cfa41eb9]
	(No symbol) [0x0x7ff7cfa9602e]
	(No symbol) [0x0x7ff7cfa957c0]
	(No symbol) [0x0x7ff7cfa883e3]
	(No symbol) [0x0x7ff7cfa51521]
	(No symbol) [0x0x7ff7cfa522b3]
	GetHandleVerifier [0x0x7ff7cff21efd+3107021]
	GetHandleVerifier [0x0x7ff7cff1c29d+3083373]
	GetHandleVerifier [0x0x7ff7cff3bedd+3213485]
	GetHandleVerifier [0x0x7ff7cfc5884e+184862]
	GetHandleVerifier [0x0x7ff7cfc6055f+216879]
	GetHandleVerifier [0x0x7ff7cfc47084+113236]
	GetHandleVerifier [0x0x7ff7cfc47239+113673]
	GetHandleVerifier [0x0x7ff7cfc2e298+11368]
	BaseThreadInitThunk [0x0x7ffe2c43e8d7+23]
	RtlUserThreadStart [0x0x7ffe2ccfc34c+44]

2025-08-01 17:09:36 | ERROR    | src.core.browser_manager:create_driver:515 | Chrome创建失败: Message: session not created: cannot connect to chrome at 127.0.0.1:30618
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0xe0ba83+63395]
	GetHandleVerifier [0x0xe0bac4+63460]
	(No symbol) [0x0xc51f70]
	(No symbol) [0x0xc45f1a]
	(No symbol) [0x0xc8add6]
	(No symbol) [0x0xc8128f]
	(No symbol) [0x0xc810c6]
	(No symbol) [0x0xccae57]
	(No symbol) [0x0xcca74a]
	(No symbol) [0x0xcbf1a6]
	(No symbol) [0x0xc8e7b2]
	(No symbol) [0x0xc8f654]
	GetHandleVerifier [0x0x1088883+2672035]
	GetHandleVerifier [0x0x1083cba+2652634]
	GetHandleVerifier [0x0xe32bca+223466]
	GetHandleVerifier [0x0xe22cb8+158168]
	GetHandleVerifier [0x0xe2978d+185517]
	GetHandleVerifier [0x0xe13b78+96408]
	GetHandleVerifier [0x0xe13d02+96802]
	GetHandleVerifier [0x0xdfe90a+9770]
	BaseThreadInitThunk [0x0x761e5d49+25]
	RtlInitializeExceptionChain [0x0x76fad1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x76fad131+561]

2025-08-01 17:09:37 | ERROR    | src.core.browser_manager:_create_driver:557 | 创建WebDriver失败: Message: session not created: cannot connect to chrome at 127.0.0.1:30618
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0xe0ba83+63395]
	GetHandleVerifier [0x0xe0bac4+63460]
	(No symbol) [0x0xc51f70]
	(No symbol) [0x0xc45f1a]
	(No symbol) [0x0xc8add6]
	(No symbol) [0x0xc8128f]
	(No symbol) [0x0xc810c6]
	(No symbol) [0x0xccae57]
	(No symbol) [0x0xcca74a]
	(No symbol) [0x0xcbf1a6]
	(No symbol) [0x0xc8e7b2]
	(No symbol) [0x0xc8f654]
	GetHandleVerifier [0x0x1088883+2672035]
	GetHandleVerifier [0x0x1083cba+2652634]
	GetHandleVerifier [0x0xe32bca+223466]
	GetHandleVerifier [0x0xe22cb8+158168]
	GetHandleVerifier [0x0xe2978d+185517]
	GetHandleVerifier [0x0xe13b78+96408]
	GetHandleVerifier [0x0xe13d02+96802]
	GetHandleVerifier [0x0xdfe90a+9770]
	BaseThreadInitThunk [0x0x761e5d49+25]
	RtlInitializeExceptionChain [0x0x76fad1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x76fad131+561]

2025-08-01 17:09:37 | ERROR    | src.core.browser_manager:get_driver:401 | 创建WebDriver最终失败: CharleneCa2057
2025-08-01 17:09:37 | ERROR    | src.core.account_status_manager:get_or_create_driver:160 | 获取WebDriver失败 CharleneCa2057: 无法获取WebDriver
2025-08-01 17:09:37 | ERROR    | src.core.account_status_manager:check_account_status:80 | 检查账号状态失败 CharleneCa2057: 无法获取WebDriver
2025-08-01 17:09:57 | ERROR    | src.core.browser_manager:create_driver:515 | Chrome创建失败: Message: session not created: cannot connect to chrome at 127.0.0.1:31065
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0xe0ba83+63395]
	GetHandleVerifier [0x0xe0bac4+63460]
	(No symbol) [0x0xc51f70]
	(No symbol) [0x0xc45f1a]
	(No symbol) [0x0xc8add6]
	(No symbol) [0x0xc8128f]
	(No symbol) [0x0xc810c6]
	(No symbol) [0x0xccae57]
	(No symbol) [0x0xcca74a]
	(No symbol) [0x0xcbf1a6]
	(No symbol) [0x0xc8e7b2]
	(No symbol) [0x0xc8f654]
	GetHandleVerifier [0x0x1088883+2672035]
	GetHandleVerifier [0x0x1083cba+2652634]
	GetHandleVerifier [0x0xe32bca+223466]
	GetHandleVerifier [0x0xe22cb8+158168]
	GetHandleVerifier [0x0xe2978d+185517]
	GetHandleVerifier [0x0xe13b78+96408]
	GetHandleVerifier [0x0xe13d02+96802]
	GetHandleVerifier [0x0xdfe90a+9770]
	BaseThreadInitThunk [0x0x761e5d49+25]
	RtlInitializeExceptionChain [0x0x76fad1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x76fad131+561]

2025-08-01 17:09:57 | ERROR    | src.core.browser_manager:_create_driver:557 | 创建WebDriver失败: Message: session not created: cannot connect to chrome at 127.0.0.1:31065
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0xe0ba83+63395]
	GetHandleVerifier [0x0xe0bac4+63460]
	(No symbol) [0x0xc51f70]
	(No symbol) [0x0xc45f1a]
	(No symbol) [0x0xc8add6]
	(No symbol) [0x0xc8128f]
	(No symbol) [0x0xc810c6]
	(No symbol) [0x0xccae57]
	(No symbol) [0x0xcca74a]
	(No symbol) [0x0xcbf1a6]
	(No symbol) [0x0xc8e7b2]
	(No symbol) [0x0xc8f654]
	GetHandleVerifier [0x0x1088883+2672035]
	GetHandleVerifier [0x0x1083cba+2652634]
	GetHandleVerifier [0x0xe32bca+223466]
	GetHandleVerifier [0x0xe22cb8+158168]
	GetHandleVerifier [0x0xe2978d+185517]
	GetHandleVerifier [0x0xe13b78+96408]
	GetHandleVerifier [0x0xe13d02+96802]
	GetHandleVerifier [0x0xdfe90a+9770]
	BaseThreadInitThunk [0x0x761e5d49+25]
	RtlInitializeExceptionChain [0x0x76fad1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x76fad131+561]

2025-08-01 17:11:16 | ERROR    | src.core.browser_manager:create_driver:515 | Chrome创建失败: Message: session not created: cannot connect to chrome at 127.0.0.1:13620
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0xe0ba83+63395]
	GetHandleVerifier [0x0xe0bac4+63460]
	(No symbol) [0x0xc51f70]
	(No symbol) [0x0xc45f1a]
	(No symbol) [0x0xc8add6]
	(No symbol) [0x0xc8128f]
	(No symbol) [0x0xc810c6]
	(No symbol) [0x0xccae57]
	(No symbol) [0x0xcca74a]
	(No symbol) [0x0xcbf1a6]
	(No symbol) [0x0xc8e7b2]
	(No symbol) [0x0xc8f654]
	GetHandleVerifier [0x0x1088883+2672035]
	GetHandleVerifier [0x0x1083cba+2652634]
	GetHandleVerifier [0x0xe32bca+223466]
	GetHandleVerifier [0x0xe22cb8+158168]
	GetHandleVerifier [0x0xe2978d+185517]
	GetHandleVerifier [0x0xe13b78+96408]
	GetHandleVerifier [0x0xe13d02+96802]
	GetHandleVerifier [0x0xdfe90a+9770]
	BaseThreadInitThunk [0x0x761e5d49+25]
	RtlInitializeExceptionChain [0x0x76fad1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x76fad131+561]

2025-08-01 17:11:16 | ERROR    | src.core.browser_manager:_create_driver:557 | 创建WebDriver失败: Message: session not created: cannot connect to chrome at 127.0.0.1:13620
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0xe0ba83+63395]
	GetHandleVerifier [0x0xe0bac4+63460]
	(No symbol) [0x0xc51f70]
	(No symbol) [0x0xc45f1a]
	(No symbol) [0x0xc8add6]
	(No symbol) [0x0xc8128f]
	(No symbol) [0x0xc810c6]
	(No symbol) [0x0xccae57]
	(No symbol) [0x0xcca74a]
	(No symbol) [0x0xcbf1a6]
	(No symbol) [0x0xc8e7b2]
	(No symbol) [0x0xc8f654]
	GetHandleVerifier [0x0x1088883+2672035]
	GetHandleVerifier [0x0x1083cba+2652634]
	GetHandleVerifier [0x0xe32bca+223466]
	GetHandleVerifier [0x0xe22cb8+158168]
	GetHandleVerifier [0x0xe2978d+185517]
	GetHandleVerifier [0x0xe13b78+96408]
	GetHandleVerifier [0x0xe13d02+96802]
	GetHandleVerifier [0x0xdfe90a+9770]
	BaseThreadInitThunk [0x0x761e5d49+25]
	RtlInitializeExceptionChain [0x0x76fad1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x76fad131+561]

2025-08-01 17:11:16 | ERROR    | src.core.browser_manager:get_driver:401 | 创建WebDriver最终失败: PaigeBaldw49027
2025-08-01 17:11:16 | ERROR    | src.core.account_status_manager:get_or_create_driver:160 | 获取WebDriver失败 PaigeBaldw49027: 无法获取WebDriver
2025-08-01 17:11:16 | ERROR    | src.core.account_status_manager:check_account_status:80 | 检查账号状态失败 PaigeBaldw49027: 无法获取WebDriver
2025-08-01 17:33:07 | ERROR    | src.core.cookie_session_manager:_load_cookies_to_browser:194 | 加载Cookie到浏览器失败: Message: tab crashed
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0xffba83+63395]
	GetHandleVerifier [0x0xffbac4+63460]
	(No symbol) [0x0xe41f70]
	(No symbol) [0x0xe33eaa]
	(No symbol) [0x0xe33318]
	(No symbol) [0x0xe32e93]
	(No symbol) [0x0xe32ba1]
	(No symbol) [0x0xe30b44]
	(No symbol) [0x0xe3160d]
	(No symbol) [0x0xe3dda9]
	(No symbol) [0x0xe4f2b5]
	(No symbol) [0x0xe54cb6]
	(No symbol) [0x0xe31c4d]
	(No symbol) [0x0xe4eb43]
	(No symbol) [0x0xed0a9c]
	(No symbol) [0x0xeaf1a6]
	(No symbol) [0x0xe7e7b2]
	(No symbol) [0x0xe7f654]
	GetHandleVerifier [0x0x1278883+2672035]
	GetHandleVerifier [0x0x1273cba+2652634]
	GetHandleVerifier [0x0x1022bca+223466]
	GetHandleVerifier [0x0x1012cb8+158168]
	GetHandleVerifier [0x0x101978d+185517]
	GetHandleVerifier [0x0x1003b78+96408]
	GetHandleVerifier [0x0x1003d02+96802]
	GetHandleVerifier [0x0xfee90a+9770]
	BaseThreadInitThunk [0x0x761e5d49+25]
	RtlInitializeExceptionChain [0x0x76fad1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x76fad131+561]

2025-08-01 17:34:22 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:757 | Chrome启动彻底失败: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-01 17:34:22 | ERROR    | src.core.browser_manager:_fast_create_driver:820 | 快速创建WebDriver失败: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-01 17:34:37 | ERROR    | src.modules.posting.executor:login_account:452 | 登录异常: AbdullahAl16806 (尝试 1/3), HTTPConnectionPool(host='localhost', port=44097): Max retries exceeded with url: /session/b4bcc8a755b999fdab7b5f7c2b012f1e/url (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000221010CFD60>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-08-01 17:34:53 | ERROR    | src.core.browser_manager:_fast_create_driver:820 | 快速创建WebDriver失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 17:34:54 | ERROR    | src.core.browser_manager:create_driver:515 | Chrome创建失败: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-01 17:34:54 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:757 | Chrome启动彻底失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 17:34:54 | ERROR    | __main__:handle_exception:344 | 未捕获的异常: CancelledError: 
堆栈跟踪:
Traceback (most recent call last):
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py", line 131, in login_accounts
    # 执行登录 - LoginManager会自动管理浏览器和页面
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\executor.py", line 57, in login_account
    driver_wrapper = await self.browser_pool.get_driver(account)
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\core\browser_manager.py", line 373, in get_driver
    driver_wrapper = await self._create_driver(account, debug_port)
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\core\browser_manager.py", line 534, in _create_driver
    driver = await future
asyncio.exceptions.CancelledError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py", line 151, in run
    # 运行异步登录
  File "D:\python39\lib\asyncio\base_events.py", line 647, in run_until_complete
    return future.result()
asyncio.exceptions.CancelledError

2025-08-01 17:34:55 | ERROR    | src.core.browser_manager:_create_driver:557 | 创建WebDriver失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 17:35:00 | ERROR    | src.core.browser_manager:_create_driver:557 | 创建WebDriver失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 17:35:00 | ERROR    | src.core.browser_manager:get_driver:401 | 创建WebDriver最终失败: AbdullahAl16806
2025-08-01 17:35:00 | ERROR    | src.modules.posting.executor:login_account:452 | 登录异常: AbdullahAl16806 (尝试 2/3), 重新获取WebDriver失败
2025-08-01 17:35:10 | ERROR    | src.core.browser_manager:_fast_create_driver:820 | 快速创建WebDriver失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 17:35:13 | ERROR    | src.core.browser_manager:_create_driver:557 | 创建WebDriver失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 18:01:37 | ERROR    | src.core.account_status_manager:get_or_create_driver:170 | 获取WebDriver失败 EthPrice61351: Task <Task pending name='Task-5' coro=<AccountStatusManager.check_account_status() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\core\account_status_manager.py:68> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 18:01:37 | ERROR    | src.core.account_status_manager:check_account_status:90 | 检查账号状态失败 EthPrice61351: Task <Task pending name='Task-5' coro=<AccountStatusManager.check_account_status() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\core\account_status_manager.py:68> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 18:07:04 | ERROR    | src.core.account_status_manager:get_or_create_driver:170 | 获取WebDriver失败 MattikNeo21759: Task <Task pending name='Task-9' coro=<AccountStatusManager.check_account_status() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\core\account_status_manager.py:68> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 18:07:04 | ERROR    | src.core.account_status_manager:check_account_status:90 | 检查账号状态失败 MattikNeo21759: Task <Task pending name='Task-9' coro=<AccountStatusManager.check_account_status() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\core\account_status_manager.py:68> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 18:07:13 | ERROR    | src.core.account_status_manager:get_or_create_driver:170 | 获取WebDriver失败 MattikNeo21759: Task <Task pending name='Task-10' coro=<AccountStatusManager.check_account_status() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\core\account_status_manager.py:68> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 18:07:13 | ERROR    | src.core.account_status_manager:check_account_status:90 | 检查账号状态失败 MattikNeo21759: Task <Task pending name='Task-10' coro=<AccountStatusManager.check_account_status() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\core\account_status_manager.py:68> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 18:07:29 | ERROR    | src.modules.posting.executor:login_account:452 | 登录异常: MattikNeo21759 (尝试 1/3), Task <Task pending name='Task-11' coro=<AccountLoginThread.run.<locals>.login_accounts() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py:132> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 18:07:36 | ERROR    | src.modules.posting.executor:login_account:452 | 登录异常: MattikNeo21759 (尝试 2/3), Task <Task pending name='Task-11' coro=<AccountLoginThread.run.<locals>.login_accounts() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py:132> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 18:07:42 | ERROR    | src.modules.posting.executor:login_account:452 | 登录异常: MattikNeo21759 (尝试 3/3), Task <Task pending name='Task-11' coro=<AccountLoginThread.run.<locals>.login_accounts() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py:132> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 18:08:23 | ERROR    | src.modules.posting.executor:login_account:452 | 登录异常: MattikNeo21759 (尝试 1/3), Task <Task pending name='Task-16' coro=<AccountLoginThread.run.<locals>.login_accounts() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py:132> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 18:08:31 | ERROR    | src.modules.posting.executor:login_account:452 | 登录异常: MattikNeo21759 (尝试 2/3), Task <Task pending name='Task-16' coro=<AccountLoginThread.run.<locals>.login_accounts() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py:132> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 18:08:39 | ERROR    | src.modules.posting.executor:login_account:452 | 登录异常: MattikNeo21759 (尝试 3/3), Task <Task pending name='Task-16' coro=<AccountLoginThread.run.<locals>.login_accounts() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py:132> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 18:15:18 | ERROR    | __main__:handle_exception:344 | 未捕获的异常: SyntaxError: invalid syntax (account_status_manager.py, line 257)
堆栈跟踪:
Traceback (most recent call last):
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py", line 41, in run
    from src.core.account_status_manager import AccountStatusManager
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\core\account_status_manager.py", line 257
    except Exception as e:
    ^
SyntaxError: invalid syntax

2025-08-01 19:05:36 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:757 | Chrome启动彻底失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 21:00:45 | ERROR    | src.core.cookie_session_manager:_load_cookies_to_browser:194 | 加载Cookie到浏览器失败: Message: Driver已关闭

2025-08-01 21:00:55 | ERROR    | src.core.account_status_manager:get_or_create_driver:230 | 获取WebDriver失败 nft_pangol77741: Task <Task pending name='Task-11' coro=<AccountStatusManager.check_account_status() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\core\account_status_manager.py:125> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 21:00:55 | ERROR    | src.core.account_status_manager:check_account_status:150 | 检查账号状态失败 nft_pangol77741: Task <Task pending name='Task-11' coro=<AccountStatusManager.check_account_status() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\core\account_status_manager.py:125> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 21:00:55 | ERROR    | src.modules.posting.executor:login_account:452 | 登录异常: nft_pangol77741 (尝试 1/3), Message: Driver已关闭

2025-08-01 21:01:47 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:757 | Chrome启动彻底失败: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-01 21:01:47 | ERROR    | src.core.browser_manager:_fast_create_driver:820 | 快速创建WebDriver失败: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-01 21:03:05 | ERROR    | src.core.browser_manager:create_driver:515 | Chrome创建失败: Message: session not created: cannot connect to chrome at 127.0.0.1:33384
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x107ba83+63395]
	GetHandleVerifier [0x0x107bac4+63460]
	(No symbol) [0x0xec1f70]
	(No symbol) [0x0xeb5f1a]
	(No symbol) [0x0xefadd6]
	(No symbol) [0x0xef128f]
	(No symbol) [0x0xef10c6]
	(No symbol) [0x0xf3ae57]
	(No symbol) [0x0xf3a74a]
	(No symbol) [0x0xf2f1a6]
	(No symbol) [0x0xefe7b2]
	(No symbol) [0x0xeff654]
	GetHandleVerifier [0x0x12f8883+2672035]
	GetHandleVerifier [0x0x12f3cba+2652634]
	GetHandleVerifier [0x0x10a2bca+223466]
	GetHandleVerifier [0x0x1092cb8+158168]
	GetHandleVerifier [0x0x109978d+185517]
	GetHandleVerifier [0x0x1083b78+96408]
	GetHandleVerifier [0x0x1083d02+96802]
	GetHandleVerifier [0x0x106e90a+9770]
	BaseThreadInitThunk [0x0x761e5d49+25]
	RtlInitializeExceptionChain [0x0x76fad1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x76fad131+561]

2025-08-01 21:03:05 | ERROR    | src.core.browser_manager:_create_driver:557 | 创建WebDriver失败: Message: session not created: cannot connect to chrome at 127.0.0.1:33384
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x107ba83+63395]
	GetHandleVerifier [0x0x107bac4+63460]
	(No symbol) [0x0xec1f70]
	(No symbol) [0x0xeb5f1a]
	(No symbol) [0x0xefadd6]
	(No symbol) [0x0xef128f]
	(No symbol) [0x0xef10c6]
	(No symbol) [0x0xf3ae57]
	(No symbol) [0x0xf3a74a]
	(No symbol) [0x0xf2f1a6]
	(No symbol) [0x0xefe7b2]
	(No symbol) [0x0xeff654]
	GetHandleVerifier [0x0x12f8883+2672035]
	GetHandleVerifier [0x0x12f3cba+2652634]
	GetHandleVerifier [0x0x10a2bca+223466]
	GetHandleVerifier [0x0x1092cb8+158168]
	GetHandleVerifier [0x0x109978d+185517]
	GetHandleVerifier [0x0x1083b78+96408]
	GetHandleVerifier [0x0x1083d02+96802]
	GetHandleVerifier [0x0x106e90a+9770]
	BaseThreadInitThunk [0x0x761e5d49+25]
	RtlInitializeExceptionChain [0x0x76fad1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x76fad131+561]

2025-08-01 21:03:22 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:757 | Chrome启动彻底失败: Message: session not created: cannot connect to chrome at 127.0.0.1:33060
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x7ff7cfc3e415+77285]
	GetHandleVerifier [0x0x7ff7cfc3e470+77376]
	(No symbol) [0x0x7ff7cfa0989c]
	(No symbol) [0x0x7ff7cf9fab71]
	(No symbol) [0x0x7ff7cfa4d0c2]
	(No symbol) [0x0x7ff7cfa41eb9]
	(No symbol) [0x0x7ff7cfa9602e]
	(No symbol) [0x0x7ff7cfa957c0]
	(No symbol) [0x0x7ff7cfa883e3]
	(No symbol) [0x0x7ff7cfa51521]
	(No symbol) [0x0x7ff7cfa522b3]
	GetHandleVerifier [0x0x7ff7cff21efd+3107021]
	GetHandleVerifier [0x0x7ff7cff1c29d+3083373]
	GetHandleVerifier [0x0x7ff7cff3bedd+3213485]
	GetHandleVerifier [0x0x7ff7cfc5884e+184862]
	GetHandleVerifier [0x0x7ff7cfc6055f+216879]
	GetHandleVerifier [0x0x7ff7cfc47084+113236]
	GetHandleVerifier [0x0x7ff7cfc47239+113673]
	GetHandleVerifier [0x0x7ff7cfc2e298+11368]
	BaseThreadInitThunk [0x0x7ffe2c43e8d7+23]
	RtlUserThreadStart [0x0x7ffe2ccfc34c+44]

2025-08-01 21:03:22 | ERROR    | src.core.browser_manager:_fast_create_driver:820 | 快速创建WebDriver失败: Message: session not created: cannot connect to chrome at 127.0.0.1:33060
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x7ff7cfc3e415+77285]
	GetHandleVerifier [0x0x7ff7cfc3e470+77376]
	(No symbol) [0x0x7ff7cfa0989c]
	(No symbol) [0x0x7ff7cf9fab71]
	(No symbol) [0x0x7ff7cfa4d0c2]
	(No symbol) [0x0x7ff7cfa41eb9]
	(No symbol) [0x0x7ff7cfa9602e]
	(No symbol) [0x0x7ff7cfa957c0]
	(No symbol) [0x0x7ff7cfa883e3]
	(No symbol) [0x0x7ff7cfa51521]
	(No symbol) [0x0x7ff7cfa522b3]
	GetHandleVerifier [0x0x7ff7cff21efd+3107021]
	GetHandleVerifier [0x0x7ff7cff1c29d+3083373]
	GetHandleVerifier [0x0x7ff7cff3bedd+3213485]
	GetHandleVerifier [0x0x7ff7cfc5884e+184862]
	GetHandleVerifier [0x0x7ff7cfc6055f+216879]
	GetHandleVerifier [0x0x7ff7cfc47084+113236]
	GetHandleVerifier [0x0x7ff7cfc47239+113673]
	GetHandleVerifier [0x0x7ff7cfc2e298+11368]
	BaseThreadInitThunk [0x0x7ffe2c43e8d7+23]
	RtlUserThreadStart [0x0x7ffe2ccfc34c+44]

2025-08-01 21:04:24 | ERROR    | src.core.browser_manager:create_driver:515 | Chrome创建失败: Message: session not created: cannot connect to chrome at 127.0.0.1:34058
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x107ba83+63395]
	GetHandleVerifier [0x0x107bac4+63460]
	(No symbol) [0x0xec1f70]
	(No symbol) [0x0xeb5f1a]
	(No symbol) [0x0xefadd6]
	(No symbol) [0x0xef128f]
	(No symbol) [0x0xef10c6]
	(No symbol) [0x0xf3ae57]
	(No symbol) [0x0xf3a74a]
	(No symbol) [0x0xf2f1a6]
	(No symbol) [0x0xefe7b2]
	(No symbol) [0x0xeff654]
	GetHandleVerifier [0x0x12f8883+2672035]
	GetHandleVerifier [0x0x12f3cba+2652634]
	GetHandleVerifier [0x0x10a2bca+223466]
	GetHandleVerifier [0x0x1092cb8+158168]
	GetHandleVerifier [0x0x109978d+185517]
	GetHandleVerifier [0x0x1083b78+96408]
	GetHandleVerifier [0x0x1083d02+96802]
	GetHandleVerifier [0x0x106e90a+9770]
	BaseThreadInitThunk [0x0x761e5d49+25]
	RtlInitializeExceptionChain [0x0x76fad1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x76fad131+561]

2025-08-01 21:04:24 | ERROR    | src.core.browser_manager:_create_driver:557 | 创建WebDriver失败: Message: session not created: cannot connect to chrome at 127.0.0.1:34058
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x107ba83+63395]
	GetHandleVerifier [0x0x107bac4+63460]
	(No symbol) [0x0xec1f70]
	(No symbol) [0x0xeb5f1a]
	(No symbol) [0x0xefadd6]
	(No symbol) [0x0xef128f]
	(No symbol) [0x0xef10c6]
	(No symbol) [0x0xf3ae57]
	(No symbol) [0x0xf3a74a]
	(No symbol) [0x0xf2f1a6]
	(No symbol) [0x0xefe7b2]
	(No symbol) [0x0xeff654]
	GetHandleVerifier [0x0x12f8883+2672035]
	GetHandleVerifier [0x0x12f3cba+2652634]
	GetHandleVerifier [0x0x10a2bca+223466]
	GetHandleVerifier [0x0x1092cb8+158168]
	GetHandleVerifier [0x0x109978d+185517]
	GetHandleVerifier [0x0x1083b78+96408]
	GetHandleVerifier [0x0x1083d02+96802]
	GetHandleVerifier [0x0x106e90a+9770]
	BaseThreadInitThunk [0x0x761e5d49+25]
	RtlInitializeExceptionChain [0x0x76fad1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x76fad131+561]

2025-08-01 21:04:24 | ERROR    | src.core.browser_manager:get_driver:401 | 创建WebDriver最终失败: CharleneCa2057
2025-08-01 21:04:24 | ERROR    | src.modules.posting.executor:login_account:452 | 登录异常: CharleneCa2057 (尝试 1/3), 无法获取WebDriver
2025-08-01 21:04:40 | ERROR    | src.core.browser_manager:create_driver:515 | Chrome创建失败: Message: session not created: cannot connect to chrome at 127.0.0.1:34114
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x107ba83+63395]
	GetHandleVerifier [0x0x107bac4+63460]
	(No symbol) [0x0xec1f70]
	(No symbol) [0x0xeb5f1a]
	(No symbol) [0x0xefadd6]
	(No symbol) [0x0xef128f]
	(No symbol) [0x0xef10c6]
	(No symbol) [0x0xf3ae57]
	(No symbol) [0x0xf3a74a]
	(No symbol) [0x0xf2f1a6]
	(No symbol) [0x0xefe7b2]
	(No symbol) [0x0xeff654]
	GetHandleVerifier [0x0x12f8883+2672035]
	GetHandleVerifier [0x0x12f3cba+2652634]
	GetHandleVerifier [0x0x10a2bca+223466]
	GetHandleVerifier [0x0x1092cb8+158168]
	GetHandleVerifier [0x0x109978d+185517]
	GetHandleVerifier [0x0x1083b78+96408]
	GetHandleVerifier [0x0x1083d02+96802]
	GetHandleVerifier [0x0x106e90a+9770]
	BaseThreadInitThunk [0x0x761e5d49+25]
	RtlInitializeExceptionChain [0x0x76fad1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x76fad131+561]

2025-08-01 21:04:40 | ERROR    | src.core.browser_manager:_create_driver:557 | 创建WebDriver失败: Message: session not created: cannot connect to chrome at 127.0.0.1:34114
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x107ba83+63395]
	GetHandleVerifier [0x0x107bac4+63460]
	(No symbol) [0x0xec1f70]
	(No symbol) [0x0xeb5f1a]
	(No symbol) [0x0xefadd6]
	(No symbol) [0x0xef128f]
	(No symbol) [0x0xef10c6]
	(No symbol) [0x0xf3ae57]
	(No symbol) [0x0xf3a74a]
	(No symbol) [0x0xf2f1a6]
	(No symbol) [0x0xefe7b2]
	(No symbol) [0x0xeff654]
	GetHandleVerifier [0x0x12f8883+2672035]
	GetHandleVerifier [0x0x12f3cba+2652634]
	GetHandleVerifier [0x0x10a2bca+223466]
	GetHandleVerifier [0x0x1092cb8+158168]
	GetHandleVerifier [0x0x109978d+185517]
	GetHandleVerifier [0x0x1083b78+96408]
	GetHandleVerifier [0x0x1083d02+96802]
	GetHandleVerifier [0x0x106e90a+9770]
	BaseThreadInitThunk [0x0x761e5d49+25]
	RtlInitializeExceptionChain [0x0x76fad1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x76fad131+561]

2025-08-01 21:06:03 | ERROR    | src.core.browser_manager:create_driver:515 | Chrome创建失败: Message: session not created: cannot connect to chrome at 127.0.0.1:23170
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x107ba83+63395]
	GetHandleVerifier [0x0x107bac4+63460]
	(No symbol) [0x0xec1f70]
	(No symbol) [0x0xeb5f1a]
	(No symbol) [0x0xefadd6]
	(No symbol) [0x0xef128f]
	(No symbol) [0x0xef10c6]
	(No symbol) [0x0xf3ae57]
	(No symbol) [0x0xf3a74a]
	(No symbol) [0x0xf2f1a6]
	(No symbol) [0x0xefe7b2]
	(No symbol) [0x0xeff654]
	GetHandleVerifier [0x0x12f8883+2672035]
	GetHandleVerifier [0x0x12f3cba+2652634]
	GetHandleVerifier [0x0x10a2bca+223466]
	GetHandleVerifier [0x0x1092cb8+158168]
	GetHandleVerifier [0x0x109978d+185517]
	GetHandleVerifier [0x0x1083b78+96408]
	GetHandleVerifier [0x0x1083d02+96802]
	GetHandleVerifier [0x0x106e90a+9770]
	BaseThreadInitThunk [0x0x761e5d49+25]
	RtlInitializeExceptionChain [0x0x76fad1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x76fad131+561]

2025-08-01 21:06:03 | ERROR    | src.core.browser_manager:_create_driver:557 | 创建WebDriver失败: Message: session not created: cannot connect to chrome at 127.0.0.1:23170
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x107ba83+63395]
	GetHandleVerifier [0x0x107bac4+63460]
	(No symbol) [0x0xec1f70]
	(No symbol) [0x0xeb5f1a]
	(No symbol) [0x0xefadd6]
	(No symbol) [0x0xef128f]
	(No symbol) [0x0xef10c6]
	(No symbol) [0x0xf3ae57]
	(No symbol) [0x0xf3a74a]
	(No symbol) [0x0xf2f1a6]
	(No symbol) [0x0xefe7b2]
	(No symbol) [0x0xeff654]
	GetHandleVerifier [0x0x12f8883+2672035]
	GetHandleVerifier [0x0x12f3cba+2652634]
	GetHandleVerifier [0x0x10a2bca+223466]
	GetHandleVerifier [0x0x1092cb8+158168]
	GetHandleVerifier [0x0x109978d+185517]
	GetHandleVerifier [0x0x1083b78+96408]
	GetHandleVerifier [0x0x1083d02+96802]
	GetHandleVerifier [0x0x106e90a+9770]
	BaseThreadInitThunk [0x0x761e5d49+25]
	RtlInitializeExceptionChain [0x0x76fad1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x76fad131+561]

2025-08-01 21:06:03 | ERROR    | src.core.browser_manager:get_driver:401 | 创建WebDriver最终失败: PaigeBaldw49027
2025-08-01 21:06:03 | ERROR    | src.modules.posting.executor:login_account:452 | 登录异常: PaigeBaldw49027 (尝试 1/3), 无法获取WebDriver
2025-08-01 21:11:31 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:757 | Chrome启动彻底失败: Message: Service C:\Users\<USER>\.cache\selenium\chromedriver\win64\138.0.7204.183\chromedriver.exe unexpectedly exited. Status code was: **********

2025-08-01 21:11:31 | ERROR    | src.core.browser_manager:_fast_create_driver:820 | 快速创建WebDriver失败: Message: Service C:\Users\<USER>\.cache\selenium\chromedriver\win64\138.0.7204.183\chromedriver.exe unexpectedly exited. Status code was: **********

2025-08-01 21:12:18 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:757 | Chrome启动彻底失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 21:12:21 | ERROR    | __main__:handle_exception:344 | 未捕获的异常: CancelledError: 
堆栈跟踪:
Traceback (most recent call last):
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py", line 132, in login_accounts
    success = await login_manager.login_account(account)
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\executor.py", line 57, in login_account
    driver_wrapper = await self.browser_pool.get_driver(account)
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\core\browser_manager.py", line 370, in get_driver
    driver_wrapper = await self._fast_create_driver(account, debug_port)
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\core\browser_manager.py", line 778, in _fast_create_driver
    driver = await future
asyncio.exceptions.CancelledError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py", line 152, in run
    loop.run_until_complete(login_accounts())
  File "D:\python39\lib\asyncio\base_events.py", line 647, in run_until_complete
    return future.result()
asyncio.exceptions.CancelledError

2025-08-01 21:12:48 | ERROR    | src.core.browser_manager:create_driver:515 | Chrome创建失败: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-01 21:12:48 | ERROR    | __main__:handle_exception:344 | 未捕获的异常: CancelledError: 
堆栈跟踪:
Traceback (most recent call last):
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py", line 132, in login_accounts
    success = await login_manager.login_account(account)
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\executor.py", line 57, in login_account
    driver_wrapper = await self.browser_pool.get_driver(account)
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\core\browser_manager.py", line 373, in get_driver
    driver_wrapper = await self._create_driver(account, debug_port)
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\core\browser_manager.py", line 534, in _create_driver
    driver = await future
asyncio.exceptions.CancelledError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py", line 152, in run
    loop.run_until_complete(login_accounts())
  File "D:\python39\lib\asyncio\base_events.py", line 647, in run_until_complete
    return future.result()
asyncio.exceptions.CancelledError

2025-08-01 21:53:54 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:757 | Chrome启动彻底失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 21:53:54 | ERROR    | __main__:handle_exception:344 | 未捕获的异常: CancelledError: 
堆栈跟踪:
Traceback (most recent call last):
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\core\account_status_manager.py", line 125, in check_account_status
    driver_wrapper = await self.get_or_create_driver(account)
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\core\account_status_manager.py", line 222, in get_or_create_driver
    driver_wrapper = await browser_pool.get_driver(account)
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\core\browser_manager.py", line 370, in get_driver
    driver_wrapper = await self._fast_create_driver(account, debug_port)
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\core\browser_manager.py", line 778, in _fast_create_driver
    driver = await future
asyncio.exceptions.CancelledError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py", line 61, in run
    status = loop.run_until_complete(status_manager.check_account_status(account))
  File "D:\python39\lib\asyncio\base_events.py", line 647, in run_until_complete
    return future.result()
asyncio.exceptions.CancelledError

2025-08-01 21:53:56 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:757 | Chrome启动彻底失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 21:53:56 | ERROR    | __main__:handle_exception:344 | 未捕获的异常: CancelledError: 
堆栈跟踪:
Traceback (most recent call last):
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\core\account_status_manager.py", line 125, in check_account_status
    driver_wrapper = await self.get_or_create_driver(account)
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\core\account_status_manager.py", line 222, in get_or_create_driver
    driver_wrapper = await browser_pool.get_driver(account)
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\core\browser_manager.py", line 370, in get_driver
    driver_wrapper = await self._fast_create_driver(account, debug_port)
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\core\browser_manager.py", line 778, in _fast_create_driver
    driver = await future
asyncio.exceptions.CancelledError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py", line 61, in run
    status = loop.run_until_complete(status_manager.check_account_status(account))
  File "D:\python39\lib\asyncio\base_events.py", line 647, in run_until_complete
    return future.result()
asyncio.exceptions.CancelledError

2025-08-01 21:57:41 | ERROR    | src.core.account_status_manager:get_or_create_driver:230 | 获取WebDriver失败 MercedesFl59831: Task <Task pending name='Task-5' coro=<AccountStatusManager.check_account_status() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\core\account_status_manager.py:125> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 21:57:41 | ERROR    | src.core.account_status_manager:check_account_status:150 | 检查账号状态失败 MercedesFl59831: Task <Task pending name='Task-5' coro=<AccountStatusManager.check_account_status() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\core\account_status_manager.py:125> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 21:57:49 | ERROR    | src.core.account_status_manager:get_or_create_driver:230 | 获取WebDriver失败 MercedesFl59831: Task <Task pending name='Task-6' coro=<AccountStatusManager.check_account_status() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\core\account_status_manager.py:125> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 21:57:49 | ERROR    | src.core.account_status_manager:check_account_status:150 | 检查账号状态失败 MercedesFl59831: Task <Task pending name='Task-6' coro=<AccountStatusManager.check_account_status() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\core\account_status_manager.py:125> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 21:58:11 | ERROR    | src.core.account_status_manager:get_or_create_driver:230 | 获取WebDriver失败 MercedesFl59831: Task <Task pending name='Task-7' coro=<AccountStatusManager.check_account_status() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\core\account_status_manager.py:125> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 21:58:11 | ERROR    | src.core.account_status_manager:check_account_status:150 | 检查账号状态失败 MercedesFl59831: Task <Task pending name='Task-7' coro=<AccountStatusManager.check_account_status() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\core\account_status_manager.py:125> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 21:58:31 | ERROR    | src.modules.posting.executor:login_account:452 | 登录异常: MercedesFl59831 (尝试 1/3), Task <Task pending name='Task-8' coro=<AccountLoginThread.run.<locals>.login_accounts() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py:132> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 21:58:36 | ERROR    | src.modules.posting.executor:login_account:452 | 登录异常: MercedesFl59831 (尝试 2/3), Task <Task pending name='Task-8' coro=<AccountLoginThread.run.<locals>.login_accounts() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py:132> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 21:58:45 | ERROR    | src.modules.posting.executor:login_account:452 | 登录异常: MercedesFl59831 (尝试 3/3), Task <Task pending name='Task-8' coro=<AccountLoginThread.run.<locals>.login_accounts() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py:132> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-01 22:15:14 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:773 | Chrome启动彻底失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 22:15:14 | ERROR    | __main__:handle_exception:344 | 未捕获的异常: CancelledError: 
堆栈跟踪:
Traceback (most recent call last):
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py", line 132, in login_accounts
    success = await login_manager.login_account(account)
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\executor.py", line 57, in login_account
    driver_wrapper = await self.browser_pool.get_driver(account)
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\core\browser_manager.py", line 386, in get_driver
    driver_wrapper = await self._fast_create_driver(account, debug_port)
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\core\browser_manager.py", line 794, in _fast_create_driver
    # 🔧 修复：确保在正确的事件循环中执行
asyncio.exceptions.CancelledError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py", line 152, in run
    loop.run_until_complete(login_accounts())
  File "D:\python39\lib\asyncio\base_events.py", line 647, in run_until_complete
    return future.result()
asyncio.exceptions.CancelledError

2025-08-01 22:28:01 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:797 | Chrome启动彻底失败: 浏览器池已关闭，取消Chrome启动
2025-08-01 22:28:01 | ERROR    | __main__:handle_exception:344 | 未捕获的异常: CancelledError: 
堆栈跟踪:
Traceback (most recent call last):
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py", line 132, in login_accounts
    success = await login_manager.login_account(account)
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\posting\executor.py", line 57, in login_account
    driver_wrapper = await self.browser_pool.get_driver(account)
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\core\browser_manager.py", line 386, in get_driver
    driver_wrapper = await self._fast_create_driver(account, debug_port)
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\core\browser_manager.py", line 818, in _fast_create_driver
    driver = await future
asyncio.exceptions.CancelledError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py", line 152, in run
    loop.run_until_complete(login_accounts())
  File "D:\python39\lib\asyncio\base_events.py", line 647, in run_until_complete
    return future.result()
asyncio.exceptions.CancelledError

2025-08-01 23:05:28 | ERROR    | src.core.account_manager:delete_accounts:803 | 删除账号失败: (sqlite3.IntegrityError) NOT NULL constraint failed: posting_tasks.account_id
[SQL: UPDATE posting_tasks SET account_id=? WHERE posting_tasks.id = ?]
[parameters: [(None, 355), (None, 356)]]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-02 04:33:08 | ERROR    | src.modules.interaction.executor:_execute_like_fast:1217 | ❌ 未找到可用的点赞按钮: Gumball624003
2025-08-02 04:33:08 | ERROR    | src.modules.interaction.executor:_execute_single_interaction_fast:1177 | ❌ 互动失败: Gumball624003 - like
2025-08-02 04:33:08 | ERROR    | src.modules.interaction.executor:_execute_comment_fast:1370 | ❌ 未找到可用的评论按钮: Gumball624003
2025-08-02 04:33:08 | ERROR    | src.modules.interaction.executor:_execute_single_interaction_fast:1177 | ❌ 互动失败: Gumball624003 - comment
2025-08-02 04:46:45 | ERROR    | src.modules.interaction.executor:_execute_comment_fast:1451 | ❌ 未找到可用的输入框: Gumball624003
2025-08-02 04:46:45 | ERROR    | src.modules.interaction.executor:_execute_single_interaction_fast:1204 | ❌ 互动失败: Gumball624003 - comment
2025-08-02 04:54:28 | ERROR    | src.modules.interaction.executor:_execute_retweet_fast:1352 | ❌ 未找到可用的转发按钮: Gumball624003
2025-08-02 04:54:28 | ERROR    | src.modules.interaction.executor:_execute_single_interaction_fast:1204 | ❌ 互动失败: Gumball624003 - retweet
2025-08-02 04:54:28 | ERROR    | src.modules.interaction.executor:_execute_single_interaction_fast:1204 | ❌ 互动失败: Gumball624003 - comment
2025-08-02 05:00:36 | ERROR    | src.modules.interaction.executor:_execute_retweet_fast:1352 | ❌ 未找到可用的转发按钮: Gumball624003
2025-08-02 05:00:36 | ERROR    | src.modules.interaction.executor:_execute_single_interaction_fast:1204 | ❌ 互动失败: Gumball624003 - retweet
2025-08-02 05:00:36 | ERROR    | src.modules.interaction.executor:_execute_single_interaction_fast:1204 | ❌ 互动失败: Gumball624003 - comment
2025-08-02 05:06:29 | ERROR    | src.modules.interaction.executor:_execute_retweet_fast:1382 | ❌ 未找到可用的转发按钮: Gumball624003
2025-08-02 05:06:29 | ERROR    | src.modules.interaction.executor:_execute_single_interaction_fast:1204 | ❌ 互动失败: Gumball624003 - retweet
2025-08-02 05:26:24 | ERROR    | src.modules.interaction.executor:_execute_retweet_fast:1383 | ❌ 未找到可用的转发按钮: Gumball624003
2025-08-02 05:26:24 | ERROR    | src.modules.interaction.executor:_execute_single_interaction_fast:1204 | ❌ 互动失败: Gumball624003 - retweet
2025-08-02 05:26:24 | ERROR    | src.modules.interaction.executor:_execute_comment_fast:1532 | ❌ 评论输入或发送失败: Gumball624003 - Message: unknown error: ChromeDriver only supports characters in the BMP
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x3fba83+63395]
	GetHandleVerifier [0x0x3fbac4+63460]
	(No symbol) [0x0x242113]
	(No symbol) [0x0x2c6506]
	(No symbol) [0x0x28522b]
	(No symbol) [0x0x28484d]
	(No symbol) [0x0x2af3ac]
	(No symbol) [0x0x27ffa4]
	(No symbol) [0x0x2af624]
	(No symbol) [0x0x2d07ba]
	(No symbol) [0x0x2af1a6]
	(No symbol) [0x0x27e7b2]
	(No symbol) [0x0x27f654]
	GetHandleVerifier [0x0x678883+2672035]
	GetHandleVerifier [0x0x673cba+2652634]
	GetHandleVerifier [0x0x422bca+223466]
	GetHandleVerifier [0x0x412cb8+158168]
	GetHandleVerifier [0x0x41978d+185517]
	GetHandleVerifier [0x0x403b78+96408]
	GetHandleVerifier [0x0x403d02+96802]
	GetHandleVerifier [0x0x3ee90a+9770]
	BaseThreadInitThunk [0x0x761e5d49+25]
	RtlInitializeExceptionChain [0x0x76fad1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x76fad131+561]

2025-08-02 05:26:24 | ERROR    | src.modules.interaction.executor:_execute_single_interaction_fast:1204 | ❌ 互动失败: Gumball624003 - comment
2025-08-02 14:55:27 | ERROR    | src.modules.interaction.executor:_execute_single_interaction_fast:1209 | ❌ 互动执行异常: JohnRamire36615 - comment - Task <Task pending name='Task-24' coro=<InteractionExecutor._execute_single_interaction_fast() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\modules\interaction\executor.py:1117> cb=[_release_waiter(<Future pendi...2323F6CD0>()]>)() at D:\python39\lib\asyncio\tasks.py:416]> got Future <Future pending> attached to a different loop
2025-08-02 15:03:25 | ERROR    | src.modules.interaction.executor:_execute_comment_fast:1531 | ❌ 评论输入或发送失败: JohnRamire36615 - Message: unknown error: ChromeDriver only supports characters in the BMP
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0xf1ba83+63395]
	GetHandleVerifier [0x0xf1bac4+63460]
	(No symbol) [0x0xd62113]
	(No symbol) [0x0xde6506]
	(No symbol) [0x0xda522b]
	(No symbol) [0x0xda484d]
	(No symbol) [0x0xdcf3ac]
	(No symbol) [0x0xd9ffa4]
	(No symbol) [0x0xdcf624]
	(No symbol) [0x0xdf07ba]
	(No symbol) [0x0xdcf1a6]
	(No symbol) [0x0xd9e7b2]
	(No symbol) [0x0xd9f654]
	GetHandleVerifier [0x0x1198883+2672035]
	GetHandleVerifier [0x0x1193cba+2652634]
	GetHandleVerifier [0x0xf42bca+223466]
	GetHandleVerifier [0x0xf32cb8+158168]
	GetHandleVerifier [0x0xf3978d+185517]
	GetHandleVerifier [0x0xf23b78+96408]
	GetHandleVerifier [0x0xf23d02+96802]
	GetHandleVerifier [0x0xf0e90a+9770]
	BaseThreadInitThunk [0x0x761e5d49+25]
	RtlInitializeExceptionChain [0x0x76fad1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x76fad131+561]

2025-08-02 15:03:25 | ERROR    | src.modules.interaction.executor:_execute_single_interaction_fast:1204 | ❌ 互动失败: JohnRamire36615 - comment
2025-08-02 15:24:43 | ERROR    | src.modules.interaction.executor:_execute_comment_fast:1602 | ❌ 评论输入或发送失败: JohnRamire36615 - Message: unknown error: ChromeDriver only supports characters in the BMP
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x33ba83+63395]
	GetHandleVerifier [0x0x33bac4+63460]
	(No symbol) [0x0x182113]
	(No symbol) [0x0x206506]
	(No symbol) [0x0x1c522b]
	(No symbol) [0x0x1c484d]
	(No symbol) [0x0x1ef3ac]
	(No symbol) [0x0x1bffa4]
	(No symbol) [0x0x1ef624]
	(No symbol) [0x0x2107ba]
	(No symbol) [0x0x1ef1a6]
	(No symbol) [0x0x1be7b2]
	(No symbol) [0x0x1bf654]
	GetHandleVerifier [0x0x5b8883+2672035]
	GetHandleVerifier [0x0x5b3cba+2652634]
	GetHandleVerifier [0x0x362bca+223466]
	GetHandleVerifier [0x0x352cb8+158168]
	GetHandleVerifier [0x0x35978d+185517]
	GetHandleVerifier [0x0x343b78+96408]
	GetHandleVerifier [0x0x343d02+96802]
	GetHandleVerifier [0x0x32e90a+9770]
	BaseThreadInitThunk [0x0x761e5d49+25]
	RtlInitializeExceptionChain [0x0x76fad1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x76fad131+561]
	(No symbol) [0x0]

2025-08-02 15:24:43 | ERROR    | src.modules.interaction.executor:_execute_single_interaction_fast:1275 | ❌ 互动失败: JohnRamire36615 - comment
2025-08-02 15:24:43 | ERROR    | src.modules.interaction.executor:execute_account_tasks_sequentially:1112 | ❌ 任务失败 2/2: JohnRamire36615 - comment
2025-08-02 18:06:51 | ERROR    | src.modules.posting.executor:_input_text_content:1593 | 输入文本内容失败: Message: element click intercepted: Element <div aria-activedescendant="typeaheadFocus-0.****************" aria-autocomplete="list" aria-controls="typeaheadDropdownWrapped-1" aria-describedby="placeholder-17k7r" aria-label="Post text" aria-multiline="true" class="notranslate public-DraftEditor-content" contenteditable="true" data-testid="tweetTextarea_0" role="textbox" spellcheck="true" tabindex="0" no-focustrapview-refocus="true" style="outline: none; user-select: text; white-space: pre-wrap; overflow-wrap: break-word;">...</div> is not clickable at point (911, 90). Other element would receive the click: <div class="css-175oi2r r-1p0dtai r-1d2f490 r-1xcajam r-zchlnj r-ipm5af r-1ffj0ar" data-testid="mask"></div>
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0xfdba83+63395]
	GetHandleVerifier [0x0xfdbac4+63460]
	(No symbol) [0x0xe22113]
	(No symbol) [0x0xe70ba0]
	(No symbol) [0x0xe6ef5a]
	(No symbol) [0x0xe6cab7]
	(No symbol) [0x0xe6bd6d]
	(No symbol) [0x0xe60515]
	(No symbol) [0x0xe8f3ac]
	(No symbol) [0x0xe5ffa4]
	(No symbol) [0x0xe8f624]
	(No symbol) [0x0xeb07ba]
	(No symbol) [0x0xe8f1a6]
	(No symbol) [0x0xe5e7b2]
	(No symbol) [0x0xe5f654]
	GetHandleVerifier [0x0x1258883+2672035]
	GetHandleVerifier [0x0x1253cba+2652634]
	GetHandleVerifier [0x0x1002bca+223466]
	GetHandleVerifier [0x0xff2cb8+158168]
	GetHandleVerifier [0x0xff978d+185517]
	GetHandleVerifier [0x0xfe3b78+96408]
	GetHandleVerifier [0x0xfe3d02+96802]
	GetHandleVerifier [0x0xfce90a+9770]
	BaseThreadInitThunk [0x0x761e5d49+25]
	RtlInitializeExceptionChain [0x0x76fad1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x76fad131+561]

2025-08-02 18:07:08 | ERROR    | src.modules.posting.executor:_input_text_content:1593 | 输入文本内容失败: Message: element click intercepted: Element <div aria-activedescendant="typeaheadFocus-0.05024371849271703" aria-autocomplete="list" aria-controls="typeaheadDropdownWrapped-1" aria-describedby="placeholder-dmgvf" aria-label="Post text" aria-multiline="true" class="notranslate public-DraftEditor-content" contenteditable="true" data-testid="tweetTextarea_0" role="textbox" spellcheck="true" tabindex="0" no-focustrapview-refocus="true" style="outline: none; user-select: text; white-space: pre-wrap; overflow-wrap: break-word;">...</div> is not clickable at point (911, 90). Other element would receive the click: <div class="css-175oi2r r-1p0dtai r-1d2f490 r-1xcajam r-zchlnj r-ipm5af r-1ffj0ar" data-testid="mask"></div>
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0xfdba83+63395]
	GetHandleVerifier [0x0xfdbac4+63460]
	(No symbol) [0x0xe22113]
	(No symbol) [0x0xe70ba0]
	(No symbol) [0x0xe6ef5a]
	(No symbol) [0x0xe6cab7]
	(No symbol) [0x0xe6bd6d]
	(No symbol) [0x0xe60515]
	(No symbol) [0x0xe8f3ac]
	(No symbol) [0x0xe5ffa4]
	(No symbol) [0x0xe8f624]
	(No symbol) [0x0xeb07ba]
	(No symbol) [0x0xe8f1a6]
	(No symbol) [0x0xe5e7b2]
	(No symbol) [0x0xe5f654]
	GetHandleVerifier [0x0x1258883+2672035]
	GetHandleVerifier [0x0x1253cba+2652634]
	GetHandleVerifier [0x0x1002bca+223466]
	GetHandleVerifier [0x0xff2cb8+158168]
	GetHandleVerifier [0x0xff978d+185517]
	GetHandleVerifier [0x0xfe3b78+96408]
	GetHandleVerifier [0x0xfe3d02+96802]
	GetHandleVerifier [0x0xfce90a+9770]
	BaseThreadInitThunk [0x0x761e5d49+25]
	RtlInitializeExceptionChain [0x0x76fad1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x76fad131+561]

2025-08-02 18:07:53 | ERROR    | src.modules.interaction.executor:_execute_comment_fast:2228 | ❌ 未找到可用的输入框，已保存截图: debug_comment_input_JohnRamire36615_1754129273.png
2025-08-02 18:07:53 | ERROR    | src.modules.interaction.executor:_execute_single_interaction_fast:1458 | ❌ 互动失败: JohnRamire36615 - comment
2025-08-02 18:07:53 | ERROR    | src.modules.interaction.executor:execute_account_tasks_sequentially:1278 | ❌ 任务失败 1/1: JohnRamire36615 - comment
2025-08-02 18:08:31 | ERROR    | src.modules.posting.executor:_input_text_content:1593 | 输入文本内容失败: Message: element click intercepted: Element <div aria-activedescendant="typeaheadFocus-0.****************" aria-autocomplete="list" aria-controls="typeaheadDropdownWrapped-2" aria-describedby="placeholder-db25k" aria-label="Post text" aria-multiline="true" class="notranslate public-DraftEditor-content" contenteditable="true" data-testid="tweetTextarea_0" role="textbox" spellcheck="true" tabindex="0" no-focustrapview-refocus="true" style="outline: none; user-select: text; white-space: pre-wrap; overflow-wrap: break-word;">...</div> is not clickable at point (911, 90). Other element would receive the click: <div class="css-175oi2r r-1p0dtai r-1d2f490 r-1xcajam r-zchlnj r-ipm5af r-1ffj0ar" data-testid="mask"></div>
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0xfdba83+63395]
	GetHandleVerifier [0x0xfdbac4+63460]
	(No symbol) [0x0xe22113]
	(No symbol) [0x0xe70ba0]
	(No symbol) [0x0xe6ef5a]
	(No symbol) [0x0xe6cab7]
	(No symbol) [0x0xe6bd6d]
	(No symbol) [0x0xe60515]
	(No symbol) [0x0xe8f3ac]
	(No symbol) [0x0xe5ffa4]
	(No symbol) [0x0xe8f624]
	(No symbol) [0x0xeb07ba]
	(No symbol) [0x0xe8f1a6]
	(No symbol) [0x0xe5e7b2]
	(No symbol) [0x0xe5f654]
	GetHandleVerifier [0x0x1258883+2672035]
	GetHandleVerifier [0x0x1253cba+2652634]
	GetHandleVerifier [0x0x1002bca+223466]
	GetHandleVerifier [0x0xff2cb8+158168]
	GetHandleVerifier [0x0xff978d+185517]
	GetHandleVerifier [0x0xfe3b78+96408]
	GetHandleVerifier [0x0xfe3d02+96802]
	GetHandleVerifier [0x0xfce90a+9770]
	BaseThreadInitThunk [0x0x761e5d49+25]
	RtlInitializeExceptionChain [0x0x76fad1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x76fad131+561]

2025-08-02 18:09:21 | ERROR    | src.modules.posting.executor:execute_post:876 | ❌ 发帖失败: 媒体上传失败: 媒体上传验证失败
2025-08-02 19:08:22 | ERROR    | src.modules.interaction.executor:_execute_comment_fast:2271 | ❌ 未找到可用的评论按钮，已保存截图: debug_comment_failed_EWebb32471_1754132902.png
2025-08-02 19:08:22 | ERROR    | src.modules.interaction.executor:_execute_single_interaction_fast:1483 | ❌ 互动失败: EWebb32471 - comment
2025-08-02 19:08:22 | ERROR    | src.modules.interaction.executor:execute_account_tasks_sequentially:1278 | ❌ 任务失败 1/1: EWebb32471 - comment
2025-08-02 19:08:22 | ERROR    | src.modules.interaction.executor:_execute_like_fast:1946 | ❌ 未找到可用的点赞按钮，已保存截图: debug_like_failed_DavidMarti65523_1754132902.png
2025-08-02 19:08:22 | ERROR    | src.modules.interaction.executor:_execute_single_interaction_fast:1483 | ❌ 互动失败: DavidMarti65523 - like
2025-08-02 19:08:22 | ERROR    | src.modules.interaction.executor:execute_account_tasks_sequentially:1278 | ❌ 任务失败 1/1: DavidMarti65523 - like
2025-08-02 19:35:28 | ERROR    | src.modules.posting.executor:login_account:452 | 登录异常: DavidMarti65523 (尝试 1/3), Message: Driver已关闭

2025-08-02 19:37:21 | ERROR    | src.modules.posting.executor:login_account:452 | 登录异常: DavidMarti65523 (尝试 2/3), Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x126ba83+63395]
	GetHandleVerifier [0x0x126bac4+63460]
	(No symbol) [0x0x10b2113]
	(No symbol) [0x0x1090fb9]
	(No symbol) [0x0x1125ace]
	(No symbol) [0x0x1140099]
	(No symbol) [0x0x111f1a6]
	(No symbol) [0x0x10ee7b2]
	(No symbol) [0x0x10ef654]
	GetHandleVerifier [0x0x14e8883+2672035]
	GetHandleVerifier [0x0x14e3cba+2652634]
	GetHandleVerifier [0x0x1292bca+223466]
	GetHandleVerifier [0x0x1282cb8+158168]
	GetHandleVerifier [0x0x128978d+185517]
	GetHandleVerifier [0x0x1273b78+96408]
	GetHandleVerifier [0x0x1273d02+96802]
	GetHandleVerifier [0x0x125e90a+9770]
	BaseThreadInitThunk [0x0x761e5d49+25]
	RtlInitializeExceptionChain [0x0x76fad1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x76fad131+561]

2025-08-02 19:37:30 | ERROR    | src.modules.posting.executor:login_account:452 | 登录异常: DavidMarti65523 (尝试 3/3), Task <Task pending name='Task-38' coro=<AccountLoginThread.run.<locals>.login_accounts() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\account_widget.py:132> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-08-02 20:19:50 | ERROR    | src.modules.interaction.executor:batch_interact:1030 | 批量互动失败: local variable 'max_concurrent' referenced before assignment
2025-08-02 20:19:50 | ERROR    | src.modules.interaction.workflow:execute_post_interactions:72 | 帖子互动失败: 'details'
2025-08-02 20:32:57 | ERROR    | src.modules.interaction.executor:_execute_comment_fast:2572 | ❌ 评论输入或发送失败: MercedesFl59831 - Message: element click intercepted: Element <button role="button" class="css-175oi2r r-sdzlij r-1phboty r-rs99b7 r-lrvibr r-1cwvpvk r-2yi16 r-1qi8awa r-3pj75a r-1loqt21 r-o7ynqc r-6416eg r-1ny4l3l" data-testid="tweetButtonInline" type="button" style="background-color: rgb(15, 20, 25); border-color: rgba(0, 0, 0, 0);">...</button> is not clickable at point (1132, 780). Other element would receive the click: <div class="css-175oi2r r-1pi2tsx r-1d2f490 r-1xcajam r-ipm5af r-13qz1uu" data-testid="twc-cc-mask"></div>
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x93ba83+63395]
	GetHandleVerifier [0x0x93bac4+63460]
	(No symbol) [0x0x782113]
	(No symbol) [0x0x7d0ba0]
	(No symbol) [0x0x7cef5a]
	(No symbol) [0x0x7ccab7]
	(No symbol) [0x0x7cbd6d]
	(No symbol) [0x0x7c0515]
	(No symbol) [0x0x7ef3ac]
	(No symbol) [0x0x7bffa4]
	(No symbol) [0x0x7ef624]
	(No symbol) [0x0x8107ba]
	(No symbol) [0x0x7ef1a6]
	(No symbol) [0x0x7be7b2]
	(No symbol) [0x0x7bf654]
	GetHandleVerifier [0x0xbb8883+2672035]
	GetHandleVerifier [0x0xbb3cba+2652634]
	GetHandleVerifier [0x0x962bca+223466]
	GetHandleVerifier [0x0x952cb8+158168]
	GetHandleVerifier [0x0x95978d+185517]
	GetHandleVerifier [0x0x943b78+96408]
	GetHandleVerifier [0x0x943d02+96802]
	GetHandleVerifier [0x0x92e90a+9770]
	BaseThreadInitThunk [0x0x75fd5d49+25]
	RtlInitializeExceptionChain [0x0x7745d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7745d131+561]

2025-08-02 20:32:57 | ERROR    | src.modules.interaction.executor:_execute_single_interaction_fast:1536 | ❌ 互动失败: MercedesFl59831 - comment
2025-08-02 20:32:57 | ERROR    | src.modules.interaction.executor:execute_account_tasks_sequentially:1316 | ❌ 任务失败 1/1: MercedesFl59831 - comment
2025-08-02 20:33:26 | ERROR    | src.modules.posting.executor:_input_text_content:1593 | 输入文本内容失败: Message: element click intercepted: Element <div aria-activedescendant="typeaheadFocus-0.***************" aria-autocomplete="list" aria-controls="typeaheadDropdownWrapped-1" aria-describedby="placeholder-d6g4m" aria-label="Post text" aria-multiline="true" class="notranslate public-DraftEditor-content" contenteditable="true" data-testid="tweetTextarea_0" role="textbox" spellcheck="true" tabindex="0" no-focustrapview-refocus="true" style="outline: none; user-select: text; white-space: pre-wrap; overflow-wrap: break-word;">...</div> is not clickable at point (911, 90). Other element would receive the click: <div class="css-175oi2r r-1p0dtai r-1d2f490 r-1xcajam r-zchlnj r-ipm5af r-1ffj0ar" data-testid="mask"></div>
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x93ba83+63395]
	GetHandleVerifier [0x0x93bac4+63460]
	(No symbol) [0x0x782113]
	(No symbol) [0x0x7d0ba0]
	(No symbol) [0x0x7cef5a]
	(No symbol) [0x0x7ccab7]
	(No symbol) [0x0x7cbd6d]
	(No symbol) [0x0x7c0515]
	(No symbol) [0x0x7ef3ac]
	(No symbol) [0x0x7bffa4]
	(No symbol) [0x0x7ef624]
	(No symbol) [0x0x8107ba]
	(No symbol) [0x0x7ef1a6]
	(No symbol) [0x0x7be7b2]
	(No symbol) [0x0x7bf654]
	GetHandleVerifier [0x0xbb8883+2672035]
	GetHandleVerifier [0x0xbb3cba+2652634]
	GetHandleVerifier [0x0x962bca+223466]
	GetHandleVerifier [0x0x952cb8+158168]
	GetHandleVerifier [0x0x95978d+185517]
	GetHandleVerifier [0x0x943b78+96408]
	GetHandleVerifier [0x0x943d02+96802]
	GetHandleVerifier [0x0x92e90a+9770]
	BaseThreadInitThunk [0x0x75fd5d49+25]
	RtlInitializeExceptionChain [0x0x7745d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7745d131+561]

2025-08-02 20:33:42 | ERROR    | src.modules.posting.executor:_input_text_content:1593 | 输入文本内容失败: Message: element click intercepted: Element <div aria-activedescendant="typeaheadFocus-0.7082236159156141" aria-autocomplete="list" aria-controls="typeaheadDropdownWrapped-1" aria-describedby="placeholder-3dlnl" aria-label="Post text" aria-multiline="true" class="notranslate public-DraftEditor-content" contenteditable="true" data-testid="tweetTextarea_0" role="textbox" spellcheck="true" tabindex="0" no-focustrapview-refocus="true" style="outline: none; user-select: text; white-space: pre-wrap; overflow-wrap: break-word;">...</div> is not clickable at point (911, 90). Other element would receive the click: <div class="css-175oi2r r-1p0dtai r-1d2f490 r-1xcajam r-zchlnj r-ipm5af r-1ffj0ar" data-testid="mask"></div>
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x93ba83+63395]
	GetHandleVerifier [0x0x93bac4+63460]
	(No symbol) [0x0x782113]
	(No symbol) [0x0x7d0ba0]
	(No symbol) [0x0x7cef5a]
	(No symbol) [0x0x7ccab7]
	(No symbol) [0x0x7cbd6d]
	(No symbol) [0x0x7c0515]
	(No symbol) [0x0x7ef3ac]
	(No symbol) [0x0x7bffa4]
	(No symbol) [0x0x7ef624]
	(No symbol) [0x0x8107ba]
	(No symbol) [0x0x7ef1a6]
	(No symbol) [0x0x7be7b2]
	(No symbol) [0x0x7bf654]
	GetHandleVerifier [0x0xbb8883+2672035]
	GetHandleVerifier [0x0xbb3cba+2652634]
	GetHandleVerifier [0x0x962bca+223466]
	GetHandleVerifier [0x0x952cb8+158168]
	GetHandleVerifier [0x0x95978d+185517]
	GetHandleVerifier [0x0x943b78+96408]
	GetHandleVerifier [0x0x943d02+96802]
	GetHandleVerifier [0x0x92e90a+9770]
	BaseThreadInitThunk [0x0x75fd5d49+25]
	RtlInitializeExceptionChain [0x0x7745d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7745d131+561]

2025-08-02 20:33:58 | ERROR    | src.modules.posting.executor:_input_text_content:1593 | 输入文本内容失败: Message: element click intercepted: Element <div aria-activedescendant="typeaheadFocus-0.9974574733135328" aria-autocomplete="list" aria-controls="typeaheadDropdownWrapped-2" aria-describedby="placeholder-744tg" aria-label="Post text" aria-multiline="true" class="notranslate public-DraftEditor-content" contenteditable="true" data-testid="tweetTextarea_0" role="textbox" spellcheck="true" tabindex="0" no-focustrapview-refocus="true" style="outline: none; user-select: text; white-space: pre-wrap; overflow-wrap: break-word;">...</div> is not clickable at point (911, 90). Other element would receive the click: <div class="css-175oi2r r-1p0dtai r-1d2f490 r-1xcajam r-zchlnj r-ipm5af r-1ffj0ar" data-testid="mask"></div>
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x93ba83+63395]
	GetHandleVerifier [0x0x93bac4+63460]
	(No symbol) [0x0x782113]
	(No symbol) [0x0x7d0ba0]
	(No symbol) [0x0x7cef5a]
	(No symbol) [0x0x7ccab7]
	(No symbol) [0x0x7cbd6d]
	(No symbol) [0x0x7c0515]
	(No symbol) [0x0x7ef3ac]
	(No symbol) [0x0x7bffa4]
	(No symbol) [0x0x7ef624]
	(No symbol) [0x0x8107ba]
	(No symbol) [0x0x7ef1a6]
	(No symbol) [0x0x7be7b2]
	(No symbol) [0x0x7bf654]
	GetHandleVerifier [0x0xbb8883+2672035]
	GetHandleVerifier [0x0xbb3cba+2652634]
	GetHandleVerifier [0x0x962bca+223466]
	GetHandleVerifier [0x0x952cb8+158168]
	GetHandleVerifier [0x0x95978d+185517]
	GetHandleVerifier [0x0x943b78+96408]
	GetHandleVerifier [0x0x943d02+96802]
	GetHandleVerifier [0x0x92e90a+9770]
	BaseThreadInitThunk [0x0x75fd5d49+25]
	RtlInitializeExceptionChain [0x0x7745d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7745d131+561]

2025-08-02 20:33:58 | ERROR    | src.modules.posting.executor:execute_post:876 | ❌ 发帖失败: 内容输入失败: Message: element click intercepted: Element <div aria-activedescendant="typeaheadFocus-0.9974574733135328" aria-autocomplete="list" aria-controls="typeaheadDropdownWrapped-2" aria-describedby="placeholder-744tg" aria-label="Post text" aria-multiline="true" class="notranslate public-DraftEditor-content" contenteditable="true" data-testid="tweetTextarea_0" role="textbox" spellcheck="true" tabindex="0" no-focustrapview-refocus="true" style="outline: none; user-select: text; white-space: pre-wrap; overflow-wrap: break-word;">...</div> is not clickable at point (911, 90). Other element would receive the click: <div class="css-175oi2r r-1p0dtai r-1d2f490 r-1xcajam r-zchlnj r-ipm5af r-1ffj0ar" data-testid="mask"></div>
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x93ba83+63395]
	GetHandleVerifier [0x0x93bac4+63460]
	(No symbol) [0x0x782113]
	(No symbol) [0x0x7d0ba0]
	(No symbol) [0x0x7cef5a]
	(No symbol) [0x0x7ccab7]
	(No symbol) [0x0x7cbd6d]
	(No symbol) [0x0x7c0515]
	(No symbol) [0x0x7ef3ac]
	(No symbol) [0x0x7bffa4]
	(No symbol) [0x0x7ef624]
	(No symbol) [0x0x8107ba]
	(No symbol) [0x0x7ef1a6]
	(No symbol) [0x0x7be7b2]
	(No symbol) [0x0x7bf654]
	GetHandleVerifier [0x0xbb8883+2672035]
	GetHandleVerifier [0x0xbb3cba+2652634]
	GetHandleVerifier [0x0x962bca+223466]
	GetHandleVerifier [0x0x952cb8+158168]
	GetHandleVerifier [0x0x95978d+185517]
	GetHandleVerifier [0x0x943b78+96408]
	GetHandleVerifier [0x0x943d02+96802]
	GetHandleVerifier [0x0x92e90a+9770]
	BaseThreadInitThunk [0x0x75fd5d49+25]
	RtlInitializeExceptionChain [0x0x7745d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7745d131+561]

2025-08-02 20:59:11 | ERROR    | src.modules.interaction.executor:_execute_comment_fast:2722 | ❌ 评论输入或发送失败: EWebb32471 - Message: element click intercepted: Element <button role="button" class="css-175oi2r r-sdzlij r-1phboty r-rs99b7 r-lrvibr r-1cwvpvk r-2yi16 r-1qi8awa r-3pj75a r-1loqt21 r-o7ynqc r-6416eg r-1ny4l3l" data-testid="tweetButtonInline" type="button" style="background-color: rgb(15, 20, 25); border-color: rgba(0, 0, 0, 0);">...</button> is not clickable at point (1132, 804). Other element would receive the click: <div class="css-175oi2r r-1pi2tsx r-1d2f490 r-1xcajam r-ipm5af r-13qz1uu" data-testid="twc-cc-mask"></div>
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0xfaba83+63395]
	GetHandleVerifier [0x0xfabac4+63460]
	(No symbol) [0x0xdf2113]
	(No symbol) [0x0xe40ba0]
	(No symbol) [0x0xe3ef5a]
	(No symbol) [0x0xe3cab7]
	(No symbol) [0x0xe3bd6d]
	(No symbol) [0x0xe30515]
	(No symbol) [0x0xe5f3ac]
	(No symbol) [0x0xe2ffa4]
	(No symbol) [0x0xe5f624]
	(No symbol) [0x0xe807ba]
	(No symbol) [0x0xe5f1a6]
	(No symbol) [0x0xe2e7b2]
	(No symbol) [0x0xe2f654]
	GetHandleVerifier [0x0x1228883+2672035]
	GetHandleVerifier [0x0x1223cba+2652634]
	GetHandleVerifier [0x0xfd2bca+223466]
	GetHandleVerifier [0x0xfc2cb8+158168]
	GetHandleVerifier [0x0xfc978d+185517]
	GetHandleVerifier [0x0xfb3b78+96408]
	GetHandleVerifier [0x0xfb3d02+96802]
	GetHandleVerifier [0x0xf9e90a+9770]
	BaseThreadInitThunk [0x0x75fd5d49+25]
	RtlInitializeExceptionChain [0x0x7745d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7745d131+561]

2025-08-02 21:00:45 | ERROR    | src.modules.interaction.executor:_execute_comment_fast:2722 | ❌ 评论输入或发送失败: DavidMarti65523 - Message: element not interactable
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0xfaba83+63395]
	GetHandleVerifier [0x0xfabac4+63460]
	(No symbol) [0x0xdf1f70]
	(No symbol) [0x0xe3b9f4]
	(No symbol) [0x0xe30515]
	(No symbol) [0x0xe5f3ac]
	(No symbol) [0x0xe2ffa4]
	(No symbol) [0x0xe5f624]
	(No symbol) [0x0xe807ba]
	(No symbol) [0x0xe5f1a6]
	(No symbol) [0x0xe2e7b2]
	(No symbol) [0x0xe2f654]
	GetHandleVerifier [0x0x1228883+2672035]
	GetHandleVerifier [0x0x1223cba+2652634]
	GetHandleVerifier [0x0xfd2bca+223466]
	GetHandleVerifier [0x0xfc2cb8+158168]
	GetHandleVerifier [0x0xfc978d+185517]
	GetHandleVerifier [0x0xfb3b78+96408]
	GetHandleVerifier [0x0xfb3d02+96802]
	GetHandleVerifier [0x0xf9e90a+9770]
	BaseThreadInitThunk [0x0x75fd5d49+25]
	RtlInitializeExceptionChain [0x0x7745d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7745d131+561]

2025-08-02 21:50:13 | ERROR    | src.modules.interaction.executor:_execute_comment_fast:2837 | ❌ 未找到可用的输入框，已保存截图: debug_comment_input_DavidMarti65523_1754142613.png
2025-08-02 22:10:35 | ERROR    | src.modules.posting.executor:_input_text_content:1593 | 输入文本内容失败: Message: element click intercepted: Element <div aria-activedescendant="typeaheadFocus-0.9555896709695885" aria-autocomplete="list" aria-controls="typeaheadDropdownWrapped-1" aria-describedby="placeholder-3s0s1" aria-label="Post text" aria-multiline="true" class="notranslate public-DraftEditor-content" contenteditable="true" data-testid="tweetTextarea_0" role="textbox" spellcheck="true" tabindex="0" no-focustrapview-refocus="true" style="outline: none; user-select: text; white-space: pre-wrap; overflow-wrap: break-word;">...</div> is not clickable at point (911, 14). Other element would receive the click: <a href="/home" role="tab" aria-selected="false" tabindex="-1" class="css-175oi2r r-1awozwy r-6koalj r-eqz5dr r-16y2uox r-1h3ijdo r-1777fci r-s8bhmr r-3pj75a r-o7ynqc r-6416eg r-1ny4l3l r-1loqt21">...</a>
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x90ba83+63395]
	GetHandleVerifier [0x0x90bac4+63460]
	(No symbol) [0x0x752113]
	(No symbol) [0x0x7a0ba0]
	(No symbol) [0x0x79ef5a]
	(No symbol) [0x0x79cab7]
	(No symbol) [0x0x79bd6d]
	(No symbol) [0x0x790515]
	(No symbol) [0x0x7bf3ac]
	(No symbol) [0x0x78ffa4]
	(No symbol) [0x0x7bf624]
	(No symbol) [0x0x7e07ba]
	(No symbol) [0x0x7bf1a6]
	(No symbol) [0x0x78e7b2]
	(No symbol) [0x0x78f654]
	GetHandleVerifier [0x0xb88883+2672035]
	GetHandleVerifier [0x0xb83cba+2652634]
	GetHandleVerifier [0x0x932bca+223466]
	GetHandleVerifier [0x0x922cb8+158168]
	GetHandleVerifier [0x0x92978d+185517]
	GetHandleVerifier [0x0x913b78+96408]
	GetHandleVerifier [0x0x913d02+96802]
	GetHandleVerifier [0x0x8fe90a+9770]
	BaseThreadInitThunk [0x0x75fd5d49+25]
	RtlInitializeExceptionChain [0x0x7745d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7745d131+561]

2025-08-02 22:52:23 | ERROR    | src.modules.interaction.executor:_execute_comment_fast:2839 | ❌ 评论输入或发送失败: MercedesFl59831 - Message: element not interactable
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x53ba83+63395]
	GetHandleVerifier [0x0x53bac4+63460]
	(No symbol) [0x0x381f70]
	(No symbol) [0x0x3cb9f4]
	(No symbol) [0x0x3c0515]
	(No symbol) [0x0x3ef3ac]
	(No symbol) [0x0x3bffa4]
	(No symbol) [0x0x3ef624]
	(No symbol) [0x0x4107ba]
	(No symbol) [0x0x3ef1a6]
	(No symbol) [0x0x3be7b2]
	(No symbol) [0x0x3bf654]
	GetHandleVerifier [0x0x7b8883+2672035]
	GetHandleVerifier [0x0x7b3cba+2652634]
	GetHandleVerifier [0x0x562bca+223466]
	GetHandleVerifier [0x0x552cb8+158168]
	GetHandleVerifier [0x0x55978d+185517]
	GetHandleVerifier [0x0x543b78+96408]
	GetHandleVerifier [0x0x543d02+96802]
	GetHandleVerifier [0x0x52e90a+9770]
	BaseThreadInitThunk [0x0x75fd5d49+25]
	RtlInitializeExceptionChain [0x0x7745d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7745d131+561]

2025-08-02 22:52:23 | ERROR    | src.modules.interaction.executor:_execute_comment_fast:2839 | ❌ 评论输入或发送失败: EWebb32471 - Message: element not interactable
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x53ba83+63395]
	GetHandleVerifier [0x0x53bac4+63460]
	(No symbol) [0x0x381f70]
	(No symbol) [0x0x3cb9f4]
	(No symbol) [0x0x3c0515]
	(No symbol) [0x0x3ef3ac]
	(No symbol) [0x0x3bffa4]
	(No symbol) [0x0x3ef624]
	(No symbol) [0x0x4107ba]
	(No symbol) [0x0x3ef1a6]
	(No symbol) [0x0x3be7b2]
	(No symbol) [0x0x3bf654]
	GetHandleVerifier [0x0x7b8883+2672035]
	GetHandleVerifier [0x0x7b3cba+2652634]
	GetHandleVerifier [0x0x562bca+223466]
	GetHandleVerifier [0x0x552cb8+158168]
	GetHandleVerifier [0x0x55978d+185517]
	GetHandleVerifier [0x0x543b78+96408]
	GetHandleVerifier [0x0x543d02+96802]
	GetHandleVerifier [0x0x52e90a+9770]
	BaseThreadInitThunk [0x0x75fd5d49+25]
	RtlInitializeExceptionChain [0x0x7745d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7745d131+561]

2025-08-02 22:52:52 | ERROR    | src.modules.interaction.executor:_execute_comment_fast:2839 | ❌ 评论输入或发送失败: MercedesFl59831 - Message: element not interactable
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x53ba83+63395]
	GetHandleVerifier [0x0x53bac4+63460]
	(No symbol) [0x0x381f70]
	(No symbol) [0x0x3cb9f4]
	(No symbol) [0x0x3c0515]
	(No symbol) [0x0x3ef3ac]
	(No symbol) [0x0x3bffa4]
	(No symbol) [0x0x3ef624]
	(No symbol) [0x0x4107ba]
	(No symbol) [0x0x3ef1a6]
	(No symbol) [0x0x3be7b2]
	(No symbol) [0x0x3bf654]
	GetHandleVerifier [0x0x7b8883+2672035]
	GetHandleVerifier [0x0x7b3cba+2652634]
	GetHandleVerifier [0x0x562bca+223466]
	GetHandleVerifier [0x0x552cb8+158168]
	GetHandleVerifier [0x0x55978d+185517]
	GetHandleVerifier [0x0x543b78+96408]
	GetHandleVerifier [0x0x543d02+96802]
	GetHandleVerifier [0x0x52e90a+9770]
	BaseThreadInitThunk [0x0x75fd5d49+25]
	RtlInitializeExceptionChain [0x0x7745d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7745d131+561]

2025-08-02 22:52:52 | ERROR    | src.modules.interaction.executor:_execute_single_interaction_fast:1568 | ❌ 互动最终失败: MercedesFl59831 - comment
2025-08-02 22:52:52 | ERROR    | src.modules.interaction.executor:execute_account_tasks_sequentially:1321 | ❌ 任务失败 1/1: MercedesFl59831 - comment
2025-08-02 23:02:58 | ERROR    | src.modules.interaction.executor:_natural_typing_input:1929 | ❌ 自然输入失败: Message: unknown error: ChromeDriver only supports characters in the BMP
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7cba83+63395]
	GetHandleVerifier [0x0x7cbac4+63460]
	(No symbol) [0x0x612113]
	(No symbol) [0x0x696506]
	(No symbol) [0x0x65522b]
	(No symbol) [0x0x65484d]
	(No symbol) [0x0x67f3ac]
	(No symbol) [0x0x64ffa4]
	(No symbol) [0x0x67f624]
	(No symbol) [0x0x6a07ba]
	(No symbol) [0x0x67f1a6]
	(No symbol) [0x0x64e7b2]
	(No symbol) [0x0x64f654]
	GetHandleVerifier [0x0xa48883+2672035]
	GetHandleVerifier [0x0xa43cba+2652634]
	GetHandleVerifier [0x0x7f2bca+223466]
	GetHandleVerifier [0x0x7e2cb8+158168]
	GetHandleVerifier [0x0x7e978d+185517]
	GetHandleVerifier [0x0x7d3b78+96408]
	GetHandleVerifier [0x0x7d3d02+96802]
	GetHandleVerifier [0x0x7be90a+9770]
	BaseThreadInitThunk [0x0x75fd5d49+25]
	RtlInitializeExceptionChain [0x0x7745d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7745d131+561]

2025-08-02 23:02:58 | ERROR    | src.modules.interaction.executor:_natural_typing_input:1938 | ❌ 简单输入降级也失败: Message: unknown error: ChromeDriver only supports characters in the BMP
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7cba83+63395]
	GetHandleVerifier [0x0x7cbac4+63460]
	(No symbol) [0x0x612113]
	(No symbol) [0x0x696506]
	(No symbol) [0x0x65522b]
	(No symbol) [0x0x65484d]
	(No symbol) [0x0x67f3ac]
	(No symbol) [0x0x64ffa4]
	(No symbol) [0x0x67f624]
	(No symbol) [0x0x6a07ba]
	(No symbol) [0x0x67f1a6]
	(No symbol) [0x0x64e7b2]
	(No symbol) [0x0x64f654]
	GetHandleVerifier [0x0xa48883+2672035]
	GetHandleVerifier [0x0xa43cba+2652634]
	GetHandleVerifier [0x0x7f2bca+223466]
	GetHandleVerifier [0x0x7e2cb8+158168]
	GetHandleVerifier [0x0x7e978d+185517]
	GetHandleVerifier [0x0x7d3b78+96408]
	GetHandleVerifier [0x0x7d3d02+96802]
	GetHandleVerifier [0x0x7be90a+9770]
	BaseThreadInitThunk [0x0x75fd5d49+25]
	RtlInitializeExceptionChain [0x0x7745d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7745d131+561]

2025-08-02 23:02:58 | ERROR    | src.modules.interaction.executor:_execute_comment_fast:2805 | ❌ 评论内容输入失败: DavidMarti65523
2025-08-02 23:08:23 | ERROR    | src.modules.interaction.executor:_natural_typing_input:1929 | ❌ 自然输入失败: Message: unknown error: ChromeDriver only supports characters in the BMP
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7cba83+63395]
	GetHandleVerifier [0x0x7cbac4+63460]
	(No symbol) [0x0x612113]
	(No symbol) [0x0x696506]
	(No symbol) [0x0x65522b]
	(No symbol) [0x0x65484d]
	(No symbol) [0x0x67f3ac]
	(No symbol) [0x0x64ffa4]
	(No symbol) [0x0x67f624]
	(No symbol) [0x0x6a07ba]
	(No symbol) [0x0x67f1a6]
	(No symbol) [0x0x64e7b2]
	(No symbol) [0x0x64f654]
	GetHandleVerifier [0x0xa48883+2672035]
	GetHandleVerifier [0x0xa43cba+2652634]
	GetHandleVerifier [0x0x7f2bca+223466]
	GetHandleVerifier [0x0x7e2cb8+158168]
	GetHandleVerifier [0x0x7e978d+185517]
	GetHandleVerifier [0x0x7d3b78+96408]
	GetHandleVerifier [0x0x7d3d02+96802]
	GetHandleVerifier [0x0x7be90a+9770]
	BaseThreadInitThunk [0x0x75fd5d49+25]
	RtlInitializeExceptionChain [0x0x7745d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7745d131+561]

2025-08-02 23:08:23 | ERROR    | src.modules.interaction.executor:_natural_typing_input:1938 | ❌ 简单输入降级也失败: Message: unknown error: ChromeDriver only supports characters in the BMP
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7cba83+63395]
	GetHandleVerifier [0x0x7cbac4+63460]
	(No symbol) [0x0x612113]
	(No symbol) [0x0x696506]
	(No symbol) [0x0x65522b]
	(No symbol) [0x0x65484d]
	(No symbol) [0x0x67f3ac]
	(No symbol) [0x0x64ffa4]
	(No symbol) [0x0x67f624]
	(No symbol) [0x0x6a07ba]
	(No symbol) [0x0x67f1a6]
	(No symbol) [0x0x64e7b2]
	(No symbol) [0x0x64f654]
	GetHandleVerifier [0x0xa48883+2672035]
	GetHandleVerifier [0x0xa43cba+2652634]
	GetHandleVerifier [0x0x7f2bca+223466]
	GetHandleVerifier [0x0x7e2cb8+158168]
	GetHandleVerifier [0x0x7e978d+185517]
	GetHandleVerifier [0x0x7d3b78+96408]
	GetHandleVerifier [0x0x7d3d02+96802]
	GetHandleVerifier [0x0x7be90a+9770]
	BaseThreadInitThunk [0x0x75fd5d49+25]
	RtlInitializeExceptionChain [0x0x7745d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7745d131+561]

2025-08-02 23:08:23 | ERROR    | src.modules.interaction.executor:_execute_comment_fast:2805 | ❌ 评论内容输入失败: EWebb32471
2025-08-02 23:08:48 | ERROR    | src.modules.interaction.executor:_natural_typing_input:1929 | ❌ 自然输入失败: Message: unknown error: ChromeDriver only supports characters in the BMP
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7cba83+63395]
	GetHandleVerifier [0x0x7cbac4+63460]
	(No symbol) [0x0x612113]
	(No symbol) [0x0x696506]
	(No symbol) [0x0x65522b]
	(No symbol) [0x0x65484d]
	(No symbol) [0x0x67f3ac]
	(No symbol) [0x0x64ffa4]
	(No symbol) [0x0x67f624]
	(No symbol) [0x0x6a07ba]
	(No symbol) [0x0x67f1a6]
	(No symbol) [0x0x64e7b2]
	(No symbol) [0x0x64f654]
	GetHandleVerifier [0x0xa48883+2672035]
	GetHandleVerifier [0x0xa43cba+2652634]
	GetHandleVerifier [0x0x7f2bca+223466]
	GetHandleVerifier [0x0x7e2cb8+158168]
	GetHandleVerifier [0x0x7e978d+185517]
	GetHandleVerifier [0x0x7d3b78+96408]
	GetHandleVerifier [0x0x7d3d02+96802]
	GetHandleVerifier [0x0x7be90a+9770]
	BaseThreadInitThunk [0x0x75fd5d49+25]
	RtlInitializeExceptionChain [0x0x7745d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7745d131+561]

2025-08-02 23:08:48 | ERROR    | src.modules.interaction.executor:_natural_typing_input:1938 | ❌ 简单输入降级也失败: Message: unknown error: ChromeDriver only supports characters in the BMP
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7cba83+63395]
	GetHandleVerifier [0x0x7cbac4+63460]
	(No symbol) [0x0x612113]
	(No symbol) [0x0x696506]
	(No symbol) [0x0x65522b]
	(No symbol) [0x0x65484d]
	(No symbol) [0x0x67f3ac]
	(No symbol) [0x0x64ffa4]
	(No symbol) [0x0x67f624]
	(No symbol) [0x0x6a07ba]
	(No symbol) [0x0x67f1a6]
	(No symbol) [0x0x64e7b2]
	(No symbol) [0x0x64f654]
	GetHandleVerifier [0x0xa48883+2672035]
	GetHandleVerifier [0x0xa43cba+2652634]
	GetHandleVerifier [0x0x7f2bca+223466]
	GetHandleVerifier [0x0x7e2cb8+158168]
	GetHandleVerifier [0x0x7e978d+185517]
	GetHandleVerifier [0x0x7d3b78+96408]
	GetHandleVerifier [0x0x7d3d02+96802]
	GetHandleVerifier [0x0x7be90a+9770]
	BaseThreadInitThunk [0x0x75fd5d49+25]
	RtlInitializeExceptionChain [0x0x7745d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7745d131+561]

2025-08-02 23:08:48 | ERROR    | src.modules.interaction.executor:_execute_comment_fast:2805 | ❌ 评论内容输入失败: EWebb32471
2025-08-02 23:08:48 | ERROR    | src.modules.interaction.executor:_execute_single_interaction_fast:1568 | ❌ 互动最终失败: EWebb32471 - comment
2025-08-02 23:08:48 | ERROR    | src.modules.interaction.executor:execute_account_tasks_sequentially:1321 | ❌ 任务失败 1/1: EWebb32471 - comment
2025-08-02 23:10:29 | ERROR    | src.modules.interaction.executor:_natural_typing_input:1929 | ❌ 自然输入失败: Message: unknown error: ChromeDriver only supports characters in the BMP
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7cba83+63395]
	GetHandleVerifier [0x0x7cbac4+63460]
	(No symbol) [0x0x612113]
	(No symbol) [0x0x696506]
	(No symbol) [0x0x65522b]
	(No symbol) [0x0x65484d]
	(No symbol) [0x0x67f3ac]
	(No symbol) [0x0x64ffa4]
	(No symbol) [0x0x67f624]
	(No symbol) [0x0x6a07ba]
	(No symbol) [0x0x67f1a6]
	(No symbol) [0x0x64e7b2]
	(No symbol) [0x0x64f654]
	GetHandleVerifier [0x0xa48883+2672035]
	GetHandleVerifier [0x0xa43cba+2652634]
	GetHandleVerifier [0x0x7f2bca+223466]
	GetHandleVerifier [0x0x7e2cb8+158168]
	GetHandleVerifier [0x0x7e978d+185517]
	GetHandleVerifier [0x0x7d3b78+96408]
	GetHandleVerifier [0x0x7d3d02+96802]
	GetHandleVerifier [0x0x7be90a+9770]
	BaseThreadInitThunk [0x0x75fd5d49+25]
	RtlInitializeExceptionChain [0x0x7745d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7745d131+561]

2025-08-02 23:10:29 | ERROR    | src.modules.interaction.executor:_natural_typing_input:1938 | ❌ 简单输入降级也失败: Message: unknown error: ChromeDriver only supports characters in the BMP
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7cba83+63395]
	GetHandleVerifier [0x0x7cbac4+63460]
	(No symbol) [0x0x612113]
	(No symbol) [0x0x696506]
	(No symbol) [0x0x65522b]
	(No symbol) [0x0x65484d]
	(No symbol) [0x0x67f3ac]
	(No symbol) [0x0x64ffa4]
	(No symbol) [0x0x67f624]
	(No symbol) [0x0x6a07ba]
	(No symbol) [0x0x67f1a6]
	(No symbol) [0x0x64e7b2]
	(No symbol) [0x0x64f654]
	GetHandleVerifier [0x0xa48883+2672035]
	GetHandleVerifier [0x0xa43cba+2652634]
	GetHandleVerifier [0x0x7f2bca+223466]
	GetHandleVerifier [0x0x7e2cb8+158168]
	GetHandleVerifier [0x0x7e978d+185517]
	GetHandleVerifier [0x0x7d3b78+96408]
	GetHandleVerifier [0x0x7d3d02+96802]
	GetHandleVerifier [0x0x7be90a+9770]
	BaseThreadInitThunk [0x0x75fd5d49+25]
	RtlInitializeExceptionChain [0x0x7745d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7745d131+561]

2025-08-02 23:10:29 | ERROR    | src.modules.interaction.executor:_execute_comment_fast:2805 | ❌ 评论内容输入失败: DavidMarti65523
2025-08-02 23:10:53 | ERROR    | src.modules.interaction.executor:_natural_typing_input:1929 | ❌ 自然输入失败: Message: unknown error: ChromeDriver only supports characters in the BMP
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7cba83+63395]
	GetHandleVerifier [0x0x7cbac4+63460]
	(No symbol) [0x0x612113]
	(No symbol) [0x0x696506]
	(No symbol) [0x0x65522b]
	(No symbol) [0x0x65484d]
	(No symbol) [0x0x67f3ac]
	(No symbol) [0x0x64ffa4]
	(No symbol) [0x0x67f624]
	(No symbol) [0x0x6a07ba]
	(No symbol) [0x0x67f1a6]
	(No symbol) [0x0x64e7b2]
	(No symbol) [0x0x64f654]
	GetHandleVerifier [0x0xa48883+2672035]
	GetHandleVerifier [0x0xa43cba+2652634]
	GetHandleVerifier [0x0x7f2bca+223466]
	GetHandleVerifier [0x0x7e2cb8+158168]
	GetHandleVerifier [0x0x7e978d+185517]
	GetHandleVerifier [0x0x7d3b78+96408]
	GetHandleVerifier [0x0x7d3d02+96802]
	GetHandleVerifier [0x0x7be90a+9770]
	BaseThreadInitThunk [0x0x75fd5d49+25]
	RtlInitializeExceptionChain [0x0x7745d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7745d131+561]

2025-08-02 23:10:53 | ERROR    | src.modules.interaction.executor:_natural_typing_input:1938 | ❌ 简单输入降级也失败: Message: unknown error: ChromeDriver only supports characters in the BMP
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7cba83+63395]
	GetHandleVerifier [0x0x7cbac4+63460]
	(No symbol) [0x0x612113]
	(No symbol) [0x0x696506]
	(No symbol) [0x0x65522b]
	(No symbol) [0x0x65484d]
	(No symbol) [0x0x67f3ac]
	(No symbol) [0x0x64ffa4]
	(No symbol) [0x0x67f624]
	(No symbol) [0x0x6a07ba]
	(No symbol) [0x0x67f1a6]
	(No symbol) [0x0x64e7b2]
	(No symbol) [0x0x64f654]
	GetHandleVerifier [0x0xa48883+2672035]
	GetHandleVerifier [0x0xa43cba+2652634]
	GetHandleVerifier [0x0x7f2bca+223466]
	GetHandleVerifier [0x0x7e2cb8+158168]
	GetHandleVerifier [0x0x7e978d+185517]
	GetHandleVerifier [0x0x7d3b78+96408]
	GetHandleVerifier [0x0x7d3d02+96802]
	GetHandleVerifier [0x0x7be90a+9770]
	BaseThreadInitThunk [0x0x75fd5d49+25]
	RtlInitializeExceptionChain [0x0x7745d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7745d131+561]

2025-08-02 23:10:53 | ERROR    | src.modules.interaction.executor:_execute_comment_fast:2805 | ❌ 评论内容输入失败: DavidMarti65523
2025-08-02 23:10:53 | ERROR    | src.modules.interaction.executor:_execute_single_interaction_fast:1568 | ❌ 互动最终失败: DavidMarti65523 - comment
2025-08-02 23:10:53 | ERROR    | src.modules.interaction.executor:execute_account_tasks_sequentially:1321 | ❌ 任务失败 1/1: DavidMarti65523 - comment
2025-08-03 01:12:26 | ERROR    | src.modules.interaction.executor:_execute_retweet_fast:2509 | ❌ 未找到可用的转发按钮: EWebb32471
2025-08-03 01:39:41 | ERROR    | src.modules.interaction.executor:_execute_comment_fast:2824 | ❌ 评论输入或发送失败: MercedesFl59831 - Message: element click intercepted: Element <button role="button" class="css-175oi2r r-sdzlij r-1phboty r-rs99b7 r-lrvibr r-1cwvpvk r-2yi16 r-1qi8awa r-3pj75a r-1loqt21 r-o7ynqc r-6416eg r-1ny4l3l" data-testid="tweetButtonInline" type="button" style="background-color: rgb(15, 20, 25); border-color: rgba(0, 0, 0, 0);">...</button> is not clickable at point (1132, 780). Other element would receive the click: <div class="css-175oi2r r-1pi2tsx r-1d2f490 r-1xcajam r-ipm5af r-13qz1uu" data-testid="twc-cc-mask"></div>
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0xa2ba83+63395]
	GetHandleVerifier [0x0xa2bac4+63460]
	(No symbol) [0x0x872113]
	(No symbol) [0x0x8c0ba0]
	(No symbol) [0x0x8bef5a]
	(No symbol) [0x0x8bcab7]
	(No symbol) [0x0x8bbd6d]
	(No symbol) [0x0x8b0515]
	(No symbol) [0x0x8df3ac]
	(No symbol) [0x0x8affa4]
	(No symbol) [0x0x8df624]
	(No symbol) [0x0x9007ba]
	(No symbol) [0x0x8df1a6]
	(No symbol) [0x0x8ae7b2]
	(No symbol) [0x0x8af654]
	GetHandleVerifier [0x0xca8883+2672035]
	GetHandleVerifier [0x0xca3cba+2652634]
	GetHandleVerifier [0x0xa52bca+223466]
	GetHandleVerifier [0x0xa42cb8+158168]
	GetHandleVerifier [0x0xa4978d+185517]
	GetHandleVerifier [0x0xa33b78+96408]
	GetHandleVerifier [0x0xa33d02+96802]
	GetHandleVerifier [0x0xa1e90a+9770]
	BaseThreadInitThunk [0x0x75fd5d49+25]
	RtlInitializeExceptionChain [0x0x7745d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7745d131+561]

2025-08-03 01:41:25 | ERROR    | src.modules.interaction.executor:_execute_comment_fast:2824 | ❌ 评论输入或发送失败: DavidMarti65523 - Message: element click intercepted: Element <button role="button" class="css-175oi2r r-sdzlij r-1phboty r-rs99b7 r-lrvibr r-1cwvpvk r-2yi16 r-1qi8awa r-3pj75a r-1loqt21 r-o7ynqc r-6416eg r-1ny4l3l" data-testid="tweetButtonInline" type="button" style="background-color: rgb(15, 20, 25); border-color: rgba(0, 0, 0, 0);">...</button> is not clickable at point (1132, 780). Other element would receive the click: <div class="css-175oi2r r-1pi2tsx r-1d2f490 r-1xcajam r-ipm5af r-13qz1uu" data-testid="twc-cc-mask"></div>
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0xa2ba83+63395]
	GetHandleVerifier [0x0xa2bac4+63460]
	(No symbol) [0x0x872113]
	(No symbol) [0x0x8c0ba0]
	(No symbol) [0x0x8bef5a]
	(No symbol) [0x0x8bcab7]
	(No symbol) [0x0x8bbd6d]
	(No symbol) [0x0x8b0515]
	(No symbol) [0x0x8df3ac]
	(No symbol) [0x0x8affa4]
	(No symbol) [0x0x8df624]
	(No symbol) [0x0x9007ba]
	(No symbol) [0x0x8df1a6]
	(No symbol) [0x0x8ae7b2]
	(No symbol) [0x0x8af654]
	GetHandleVerifier [0x0xca8883+2672035]
	GetHandleVerifier [0x0xca3cba+2652634]
	GetHandleVerifier [0x0xa52bca+223466]
	GetHandleVerifier [0x0xa42cb8+158168]
	GetHandleVerifier [0x0xa4978d+185517]
	GetHandleVerifier [0x0xa33b78+96408]
	GetHandleVerifier [0x0xa33d02+96802]
	GetHandleVerifier [0x0xa1e90a+9770]
	BaseThreadInitThunk [0x0x75fd5d49+25]
	RtlInitializeExceptionChain [0x0x7745d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7745d131+561]

2025-08-03 01:50:18 | ERROR    | src.modules.interaction.executor:_execute_comment_fast:2824 | ❌ 评论输入或发送失败: EWebb32471 - Message: element not interactable
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x2eba83+63395]
	GetHandleVerifier [0x0x2ebac4+63460]
	(No symbol) [0x0x131f70]
	(No symbol) [0x0x17b9f4]
	(No symbol) [0x0x170515]
	(No symbol) [0x0x19f3ac]
	(No symbol) [0x0x16ffa4]
	(No symbol) [0x0x19f624]
	(No symbol) [0x0x1c07ba]
	(No symbol) [0x0x19f1a6]
	(No symbol) [0x0x16e7b2]
	(No symbol) [0x0x16f654]
	GetHandleVerifier [0x0x568883+2672035]
	GetHandleVerifier [0x0x563cba+2652634]
	GetHandleVerifier [0x0x312bca+223466]
	GetHandleVerifier [0x0x302cb8+158168]
	GetHandleVerifier [0x0x30978d+185517]
	GetHandleVerifier [0x0x2f3b78+96408]
	GetHandleVerifier [0x0x2f3d02+96802]
	GetHandleVerifier [0x0x2de90a+9770]
	BaseThreadInitThunk [0x0x75fd5d49+25]
	RtlInitializeExceptionChain [0x0x7745d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7745d131+561]
	(No symbol) [0x0]

2025-08-03 03:20:12 | ERROR    | src.ui.main_window:__init__:54 | 这是一条ERROR日志
2025-08-03 03:21:10 | ERROR    | src.ui.main_window:__init__:59 | 这是一条ERROR日志
2025-08-03 03:21:25 | ERROR    | src.ui.main_window:generate_test_log:441 | [网络连接] 请求超时，正在重试
2025-08-03 03:21:31 | ERROR    | src.ui.main_window:generate_test_log:441 | [网络连接] 请求超时，正在重试
2025-08-03 03:22:07 | ERROR    | src.ui.main_window:generate_test_log:441 | [网络连接] 请求超时，正在重试
2025-08-03 03:22:16 | ERROR    | src.ui.main_window:generate_test_log:441 | [数据库] 连接池已满
2025-08-03 03:22:31 | ERROR    | src.ui.main_window:generate_test_log:441 | [数据库] 连接池已满
2025-08-03 03:22:37 | ERROR    | src.ui.main_window:generate_test_log:441 | [网络连接] 请求超时，正在重试
2025-08-03 03:22:46 | ERROR    | src.ui.main_window:generate_test_log:441 | [网络连接] 请求超时，正在重试
2025-08-03 03:23:16 | ERROR    | src.ui.main_window:generate_test_log:441 | [数据库] 连接池已满
2025-08-03 03:23:22 | ERROR    | src.ui.main_window:generate_test_log:441 | [网络连接] 请求超时，正在重试
2025-08-03 03:23:31 | ERROR    | src.ui.main_window:generate_test_log:441 | [数据库] 连接池已满
2025-08-03 03:23:37 | ERROR    | src.ui.main_window:generate_test_log:441 | [网络连接] 请求超时，正在重试
2025-08-03 03:23:46 | ERROR    | src.ui.main_window:generate_test_log:441 | [数据库] 连接池已满
2025-08-03 03:23:55 | ERROR    | src.ui.main_window:generate_test_log:441 | [数据库] 连接池已满
2025-08-03 03:23:58 | ERROR    | src.ui.main_window:generate_test_log:441 | [网络连接] 请求超时，正在重试
2025-08-03 03:24:01 | ERROR    | src.ui.main_window:generate_test_log:441 | [数据库] 连接池已满
2025-08-03 03:24:19 | ERROR    | src.ui.main_window:generate_test_log:441 | [网络连接] 请求超时，正在重试
2025-08-03 03:24:37 | ERROR    | src.ui.main_window:generate_test_log:441 | [网络连接] 请求超时，正在重试
2025-08-03 03:24:46 | ERROR    | src.ui.main_window:generate_test_log:441 | [数据库] 连接池已满
2025-08-03 03:24:52 | ERROR    | src.ui.main_window:generate_test_log:441 | [网络连接] 请求超时，正在重试
2025-08-03 03:24:55 | ERROR    | src.ui.main_window:generate_test_log:441 | [网络连接] 请求超时，正在重试
2025-08-03 03:25:19 | ERROR    | src.ui.main_window:generate_test_log:441 | [数据库] 连接池已满
2025-08-03 03:32:53 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:32:58 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:33:03 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:33:08 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:33:13 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:33:18 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:33:24 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:33:28 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:33:33 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:33:38 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:33:43 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:33:48 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:33:54 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:33:58 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:34:03 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:34:08 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:34:13 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:34:18 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:34:24 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:34:28 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:34:32 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:34:33 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:34:37 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:34:42 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:34:42 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:34:47 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:34:52 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:34:57 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:35:02 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:35:07 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:35:12 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:35:17 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:35:22 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:35:27 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:35:32 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:35:37 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:35:42 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:35:47 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:35:52 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 03:36:00 | ERROR    | src.ui.widgets.posting_log_widget:load_posting_logs:188 | 加载发帖日志失败: 'QDate' object has no attribute 'toPython'
2025-08-03 04:03:04 | ERROR    | __main__:handle_exception:344 | 未捕获的异常: AttributeError: 'LogWidget' object has no attribute 'clear_logs'
堆栈跟踪:
Traceback (most recent call last):
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\log_widget.py", line 161, in switch_log_mode
    self.clear_logs()
AttributeError: 'LogWidget' object has no attribute 'clear_logs'

2025-08-03 04:03:40 | ERROR    | __main__:handle_exception:344 | 未捕获的异常: AttributeError: 'LogWidget' object has no attribute 'clear_logs'
堆栈跟踪:
Traceback (most recent call last):
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\log_widget.py", line 161, in switch_log_mode
    self.clear_logs()
AttributeError: 'LogWidget' object has no attribute 'clear_logs'

2025-08-03 04:03:51 | ERROR    | __main__:handle_exception:344 | 未捕获的异常: AttributeError: 'LogWidget' object has no attribute 'clear_logs'
堆栈跟踪:
Traceback (most recent call last):
  File "D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\src\ui\widgets\log_widget.py", line 161, in switch_log_mode
    self.clear_logs()
AttributeError: 'LogWidget' object has no attribute 'clear_logs'

2025-08-07 15:52:41 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:843 | Chrome启动彻底失败: 浏览器池已关闭，取消Chrome启动
2025-08-07 16:02:51 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:843 | Chrome启动彻底失败: 浏览器池已关闭，取消Chrome启动
2025-08-07 16:02:51 | ERROR    | __main__:handle_exception:344 | 未捕获的异常: CancelledError: 
堆栈跟踪:
Traceback (most recent call last):
  File "D:\yaoya\Documents\ceshishi\yyy14 - 副本 (3)\src\core\account_status_manager.py", line 125, in check_account_status
    driver_wrapper = await self.get_or_create_driver(account)
  File "D:\yaoya\Documents\ceshishi\yyy14 - 副本 (3)\src\core\account_status_manager.py", line 236, in get_or_create_driver
    driver_wrapper = await browser_pool.get_driver(account)
  File "D:\yaoya\Documents\ceshishi\yyy14 - 副本 (3)\src\core\browser_manager.py", line 405, in get_driver
    driver_wrapper = await self._fast_create_driver(account, debug_port)
  File "D:\yaoya\Documents\ceshishi\yyy14 - 副本 (3)\src\core\browser_manager.py", line 864, in _fast_create_driver
    driver = await future
asyncio.exceptions.CancelledError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\yaoya\Documents\ceshishi\yyy14 - 副本 (3)\src\ui\widgets\account_widget.py", line 61, in run
    status = loop.run_until_complete(status_manager.check_account_status(account))
  File "D:\python39\lib\asyncio\base_events.py", line 647, in run_until_complete
    return future.result()
asyncio.exceptions.CancelledError

2025-08-07 16:06:40 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:903 | Chrome启动彻底失败: 浏览器池已关闭，取消Chrome启动
2025-08-07 16:16:27 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:843 | Chrome启动彻底失败: 浏览器池已关闭，取消Chrome启动
2025-08-07 16:29:50 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:888 | Chrome启动彻底失败: 浏览器池已关闭，取消Chrome启动
2025-08-07 16:49:03 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:888 | Chrome启动彻底失败: 浏览器池已关闭，取消Chrome启动
2025-08-07 17:12:55 | ERROR    | src.ui.main_window:create_tabs:188 | 创建标签页失败: arguments did not match any overloaded call:
  QVBoxLayout(): too many arguments
  QVBoxLayout(parent: QWidget): argument 1 has unexpected type 'QWidget'
2025-08-07 17:13:56 | ERROR    | src.ui.main_window:create_tabs:189 | 创建标签页失败: setLayout(self, a0: Optional[QLayout]): argument 1 has unexpected type 'QVBoxLayout'
2025-08-07 17:20:38 | ERROR    | src.ui.main_window:create_tabs:191 | 创建标签页失败: setLayout(self, a0: Optional[QLayout]): argument 1 has unexpected type 'QVBoxLayout'
2025-08-07 17:21:21 | ERROR    | src.ui.main_window:create_tabs:190 | 创建标签页失败: arguments did not match any overloaded call:
  QVBoxLayout(): too many arguments
  QVBoxLayout(parent: QWidget): argument 1 has unexpected type 'QWidget'
2025-08-07 17:22:11 | ERROR    | src.ui.main_window:create_tabs:191 | 创建标签页失败: setLayout(self, a0: Optional[QLayout]): argument 1 has unexpected type 'QVBoxLayout'
2025-08-08 17:40:39 | ERROR    | src.modules.posting.executor:_input_text_content:1593 | 输入文本内容失败: Message: element click intercepted: Element <div aria-activedescendant="typeaheadFocus-0.6030767935421274" aria-autocomplete="list" aria-controls="typeaheadDropdownWrapped-1" aria-describedby="placeholder-5pi58" aria-label="Post text" aria-multiline="true" class="notranslate public-DraftEditor-content" contenteditable="true" data-testid="tweetTextarea_0" role="textbox" spellcheck="true" tabindex="0" no-focustrapview-refocus="true" style="outline: none; user-select: text; white-space: pre-wrap; overflow-wrap: break-word;">...</div> is not clickable at point (911, 14). Other element would receive the click: <a href="/home" role="tab" aria-selected="false" tabindex="-1" class="css-175oi2r r-1awozwy r-6koalj r-eqz5dr r-16y2uox r-1h3ijdo r-1777fci r-s8bhmr r-3pj75a r-o7ynqc r-6416eg r-1ny4l3l r-1loqt21">...</a>
  (Session info: chrome=139.0.7258.67); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#elementclickinterceptedexception
Stacktrace:
	GetHandleVerifier [0x0x7ff62e51e415+77285]
	GetHandleVerifier [0x0x7ff62e51e470+77376]
	(No symbol) [0x0x7ff62e2e9a6a]
	(No symbol) [0x0x7ff62e3483e9]
	(No symbol) [0x0x7ff62e345d8b]
	(No symbol) [0x0x7ff62e342dd1]
	(No symbol) [0x0x7ff62e341ce1]
	(No symbol) [0x0x7ff62e333458]
	(No symbol) [0x0x7ff62e36860a]
	(No symbol) [0x0x7ff62e332d06]
	(No symbol) [0x0x7ff62e368820]
	(No symbol) [0x0x7ff62e39087f]
	(No symbol) [0x0x7ff62e3683e3]
	(No symbol) [0x0x7ff62e331521]
	(No symbol) [0x0x7ff62e3322b3]
	GetHandleVerifier [0x0x7ff62e801efd+3107021]
	GetHandleVerifier [0x0x7ff62e7fc29d+3083373]
	GetHandleVerifier [0x0x7ff62e81bedd+3213485]
	GetHandleVerifier [0x0x7ff62e53884e+184862]
	GetHandleVerifier [0x0x7ff62e54055f+216879]
	GetHandleVerifier [0x0x7ff62e527084+113236]
	GetHandleVerifier [0x0x7ff62e527239+113673]
	GetHandleVerifier [0x0x7ff62e50e298+11368]
	BaseThreadInitThunk [0x0x7ffedff4e8d7+23]
	RtlUserThreadStart [0x0x7ffee0d7c34c+44]

2025-08-08 17:40:55 | ERROR    | src.modules.posting.executor:_input_text_content:1593 | 输入文本内容失败: Message: element click intercepted: Element <div aria-activedescendant="typeaheadFocus-0.560818815946612" aria-autocomplete="list" aria-controls="typeaheadDropdownWrapped-3" aria-describedby="placeholder-nns3" aria-label="Post text" aria-multiline="true" class="notranslate public-DraftEditor-content" contenteditable="true" data-testid="tweetTextarea_0" role="textbox" spellcheck="true" tabindex="0" no-focustrapview-refocus="true" style="outline: none; user-select: text; white-space: pre-wrap; overflow-wrap: break-word;">...</div> is not clickable at point (911, 90). Other element would receive the click: <div class="css-175oi2r r-1p0dtai r-1d2f490 r-1xcajam r-zchlnj r-ipm5af r-1ffj0ar" data-testid="mask"></div>
  (Session info: chrome=139.0.7258.67); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#elementclickinterceptedexception
Stacktrace:
	GetHandleVerifier [0x0x7ff62e51e415+77285]
	GetHandleVerifier [0x0x7ff62e51e470+77376]
	(No symbol) [0x0x7ff62e2e9a6a]
	(No symbol) [0x0x7ff62e3483e9]
	(No symbol) [0x0x7ff62e345d8b]
	(No symbol) [0x0x7ff62e342dd1]
	(No symbol) [0x0x7ff62e341ce1]
	(No symbol) [0x0x7ff62e333458]
	(No symbol) [0x0x7ff62e36860a]
	(No symbol) [0x0x7ff62e332d06]
	(No symbol) [0x0x7ff62e368820]
	(No symbol) [0x0x7ff62e39087f]
	(No symbol) [0x0x7ff62e3683e3]
	(No symbol) [0x0x7ff62e331521]
	(No symbol) [0x0x7ff62e3322b3]
	GetHandleVerifier [0x0x7ff62e801efd+3107021]
	GetHandleVerifier [0x0x7ff62e7fc29d+3083373]
	GetHandleVerifier [0x0x7ff62e81bedd+3213485]
	GetHandleVerifier [0x0x7ff62e53884e+184862]
	GetHandleVerifier [0x0x7ff62e54055f+216879]
	GetHandleVerifier [0x0x7ff62e527084+113236]
	GetHandleVerifier [0x0x7ff62e527239+113673]
	GetHandleVerifier [0x0x7ff62e50e298+11368]
	BaseThreadInitThunk [0x0x7ffedff4e8d7+23]
	RtlUserThreadStart [0x0x7ffee0d7c34c+44]

2025-08-08 17:40:55 | ERROR    | src.modules.posting.executor:execute_post:876 | ❌ 发帖失败: 内容输入失败: Message: element click intercepted: Element <div aria-activedescendant="typeaheadFocus-0.560818815946612" aria-autocomplete="list" aria-controls="typeaheadDropdownWrapped-3" aria-describedby="placeholder-nns3" aria-label="Post text" aria-multiline="true" class="notranslate public-DraftEditor-content" contenteditable="true" data-testid="tweetTextarea_0" role="textbox" spellcheck="true" tabindex="0" no-focustrapview-refocus="true" style="outline: none; user-select: text; white-space: pre-wrap; overflow-wrap: break-word;">...</div> is not clickable at point (911, 90). Other element would receive the click: <div class="css-175oi2r r-1p0dtai r-1d2f490 r-1xcajam r-zchlnj r-ipm5af r-1ffj0ar" data-testid="mask"></div>
  (Session info: chrome=139.0.7258.67); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#elementclickinterceptedexception
Stacktrace:
	GetHandleVerifier [0x0x7ff62e51e415+77285]
	GetHandleVerifier [0x0x7ff62e51e470+77376]
	(No symbol) [0x0x7ff62e2e9a6a]
	(No symbol) [0x0x7ff62e3483e9]
	(No symbol) [0x0x7ff62e345d8b]
	(No symbol) [0x0x7ff62e342dd1]
	(No symbol) [0x0x7ff62e341ce1]
	(No symbol) [0x0x7ff62e333458]
	(No symbol) [0x0x7ff62e36860a]
	(No symbol) [0x0x7ff62e332d06]
	(No symbol) [0x0x7ff62e368820]
	(No symbol) [0x0x7ff62e39087f]
	(No symbol) [0x0x7ff62e3683e3]
	(No symbol) [0x0x7ff62e331521]
	(No symbol) [0x0x7ff62e3322b3]
	GetHandleVerifier [0x0x7ff62e801efd+3107021]
	GetHandleVerifier [0x0x7ff62e7fc29d+3083373]
	GetHandleVerifier [0x0x7ff62e81bedd+3213485]
	GetHandleVerifier [0x0x7ff62e53884e+184862]
	GetHandleVerifier [0x0x7ff62e54055f+216879]
	GetHandleVerifier [0x0x7ff62e527084+113236]
	GetHandleVerifier [0x0x7ff62e527239+113673]
	GetHandleVerifier [0x0x7ff62e50e298+11368]
	BaseThreadInitThunk [0x0x7ffedff4e8d7+23]
	RtlUserThreadStart [0x0x7ffee0d7c34c+44]

2025-08-08 17:46:37 | ERROR    | src.modules.posting.executor:_input_text_content:1618 | 输入文本内容失败: Message: element click intercepted: Element <div aria-activedescendant="typeaheadFocus-0.23076155158558476" aria-autocomplete="list" aria-controls="typeaheadDropdownWrapped-8" aria-describedby="placeholder-amhnp" aria-label="Post text" aria-multiline="true" class="notranslate public-DraftEditor-content" contenteditable="true" data-testid="tweetTextarea_0" role="textbox" spellcheck="true" tabindex="0" no-focustrapview-refocus="true" style="outline: none; user-select: text; white-space: pre-wrap; overflow-wrap: break-word;">...</div> is not clickable at point (911, 90). Other element would receive the click: <div class="css-175oi2r r-1p0dtai r-1d2f490 r-1xcajam r-zchlnj r-ipm5af r-1ffj0ar" data-testid="mask"></div>
  (Session info: chrome=139.0.7258.67); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#elementclickinterceptedexception
Stacktrace:
	GetHandleVerifier [0x0x7ff62e51e415+77285]
	GetHandleVerifier [0x0x7ff62e51e470+77376]
	(No symbol) [0x0x7ff62e2e9a6a]
	(No symbol) [0x0x7ff62e3483e9]
	(No symbol) [0x0x7ff62e345d8b]
	(No symbol) [0x0x7ff62e342dd1]
	(No symbol) [0x0x7ff62e341ce1]
	(No symbol) [0x0x7ff62e333458]
	(No symbol) [0x0x7ff62e36860a]
	(No symbol) [0x0x7ff62e332d06]
	(No symbol) [0x0x7ff62e368820]
	(No symbol) [0x0x7ff62e39087f]
	(No symbol) [0x0x7ff62e3683e3]
	(No symbol) [0x0x7ff62e331521]
	(No symbol) [0x0x7ff62e3322b3]
	GetHandleVerifier [0x0x7ff62e801efd+3107021]
	GetHandleVerifier [0x0x7ff62e7fc29d+3083373]
	GetHandleVerifier [0x0x7ff62e81bedd+3213485]
	GetHandleVerifier [0x0x7ff62e53884e+184862]
	GetHandleVerifier [0x0x7ff62e54055f+216879]
	GetHandleVerifier [0x0x7ff62e527084+113236]
	GetHandleVerifier [0x0x7ff62e527239+113673]
	GetHandleVerifier [0x0x7ff62e50e298+11368]
	BaseThreadInitThunk [0x0x7ffedff4e8d7+23]
	RtlUserThreadStart [0x0x7ffee0d7c34c+44]


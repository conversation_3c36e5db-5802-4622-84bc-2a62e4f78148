#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
互动执行器模块 - Selenium版本
"""

import asyncio
import math
import random
import time
from datetime import datetime
from typing import Dict, Any, Optional, List
from urllib.parse import urlparse
from collections import defaultdict

from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException

from src.database.connection import get_db_session
from src.database.models import Account, InteractionTask
from src.core.browser_manager import get_browser_pool
from src.core.anti_detection import AntiDetectionEngine
from src.modules.posting.executor import LoginManager
from src.utils.logger import LoggerMixin
from src.config.constants import X_SELECTORS, INTERACTION_TYPES, TASK_STATUS
from src.config.settings import get_settings


class AccountRotationManager(LoggerMixin):
    """账号轮换管理器"""

    def __init__(self):
        self.usage_history = {}  # account_id -> 使用历史
        self.last_used_time = {}  # account_id -> 最后使用时间
        self.usage_count = {}     # account_id -> 使用次数

    def get_rotation_assignment(self, accounts, total_tasks):
        """
        基于轮换的智能分配

        Args:
            accounts: 可用账号列表
            total_tasks: 总任务数

        Returns:
            按使用频率排序的账号列表
        """
        # 🔄 按使用频率排序（使用少的优先）
        sorted_accounts = self._sort_accounts_by_usage(accounts)

        # 📊 记录本次分配前的状态
        self.logger.info(f"📊 账号使用统计:")
        for i, account in enumerate(sorted_accounts[:10]):  # 显示前10个
            usage_count = self.usage_count.get(account.id, 0)
            last_used = self.last_used_time.get(account.id, "从未使用")
            self.logger.info(f"   {account.username}: 使用{usage_count}次, 最后使用: {last_used}")

        return sorted_accounts

    def _sort_accounts_by_usage(self, accounts):
        """按使用频率排序账号（使用少的在前）"""
        current_time = time.time()

        def get_priority_score(account):
            usage_count = self.usage_count.get(account.id, 0)
            last_used = self.last_used_time.get(account.id, 0)
            time_since_last_use = current_time - last_used

            # 综合评分：使用次数越少越好，距离上次使用时间越长越好
            score = usage_count * 1000 - time_since_last_use
            return score

        return sorted(accounts, key=get_priority_score)

    def record_usage(self, account_id, action_type):
        """记录账号使用情况"""
        current_time = time.time()

        if account_id not in self.usage_count:
            self.usage_count[account_id] = 0

        self.usage_count[account_id] += 1
        self.last_used_time[account_id] = current_time

        # 记录详细使用历史
        if account_id not in self.usage_history:
            self.usage_history[account_id] = []

        self.usage_history[account_id].append({
            'action_type': action_type,
            'timestamp': current_time,
            'date': datetime.now().isoformat()
        })


class TaskTypeRotationManager(LoggerMixin):
    """任务类型轮换管理器"""

    def __init__(self):
        self.account_task_history = {}  # account_id -> 任务类型历史
        self.recent_assignments = {}    # account_id -> 最近N次的任务类型
        self.task_type_weights = {      # 任务类型权重
            'like': 1.0,
            'retweet': 1.2,    # 转发稍微重要一些
            'comment': 1.5     # 评论最重要
        }

    def get_diversified_assignment(self, accounts, interaction_config):
        """
        多样化任务类型分配

        策略：
        1. 优先给账号分配它最少执行的任务类型
        2. 避免连续分配相同的任务类型组合
        3. 在资源充足时最大化任务类型分散
        """

        like_count = interaction_config.get('like_count', 0)
        retweet_count = interaction_config.get('retweet_count', 0)
        comment_count = interaction_config.get('comment_count', 0)

        total_tasks = like_count + retweet_count + comment_count
        available_accounts = len(accounts)

        # 📊 分析资源情况
        resource_ratio = available_accounts / total_tasks if total_tasks > 0 else 1.0

        self.logger.info(f"📊 任务类型分配分析:")
        self.logger.info(f"   可用账号: {available_accounts}个")
        self.logger.info(f"   总任务数: {total_tasks}个")
        self.logger.info(f"   资源比例: {resource_ratio:.2f}")

        # 🔧 检查是否强制使用多样化分配
        force_diversified = interaction_config.get('force_diversified_assignment', False)
        if force_diversified:
            self.logger.info(f"🎯 强制使用多样化分配: 每个账号只执行一种任务类型")
            return self._diversified_assignment(accounts, interaction_config)

        if resource_ratio >= 1.0:
            # 资源充足：最大化任务类型多样化
            return self._diversified_assignment(accounts, interaction_config)
        elif resource_ratio >= 0.5:
            # 资源适中：平衡多样化和效率
            self.logger.warning(f"⚠️ 资源适中，将使用平衡分配（可能给同一账号分配多个任务）")
            self.logger.warning(f"💡 如需避免同账号多任务，请设置 force_diversified_assignment=True")
            return self._balanced_assignment(accounts, interaction_config)
        else:
            # 资源紧缺：允许适度重复，但仍尽量多样化
            self.logger.warning(f"⚠️ 资源紧缺，将使用高效分配（可能给同一账号分配多个任务）")
            self.logger.warning(f"💡 如需避免同账号多任务，请设置 force_diversified_assignment=True")
            return self._efficient_assignment(accounts, interaction_config)

    def _diversified_assignment(self, accounts, interaction_config):
        """
        多样化分配模式（资源充足）

        策略：每个账号只执行一种任务类型，最大化分散
        """
        like_count = interaction_config.get('like_count', 0)
        retweet_count = interaction_config.get('retweet_count', 0)
        comment_count = interaction_config.get('comment_count', 0)

        # 🎯 为每种任务类型选择最适合的账号
        like_accounts = self._select_accounts_for_task_type(accounts, 'like', like_count)
        retweet_accounts = self._select_accounts_for_task_type(
            [acc for acc in accounts if acc not in like_accounts], 'retweet', retweet_count
        )
        comment_accounts = self._select_accounts_for_task_type(
            [acc for acc in accounts if acc not in like_accounts and acc not in retweet_accounts],
            'comment', comment_count
        )

        tasks = []

        # 创建任务并记录分配
        for account in like_accounts:
            tasks.append({
                'account': account,
                'action_type': 'like',
                'assignment_strategy': 'diversified',
                'task_variety_score': self._calculate_variety_score(account.id, ['like'])
            })
            self._record_task_assignment(account.id, 'like')

        for account in retweet_accounts:
            tasks.append({
                'account': account,
                'action_type': 'retweet',
                'assignment_strategy': 'diversified',
                'task_variety_score': self._calculate_variety_score(account.id, ['retweet'])
            })
            self._record_task_assignment(account.id, 'retweet')

        for account in comment_accounts:
            tasks.append({
                'account': account,
                'action_type': 'comment',
                'assignment_strategy': 'diversified',
                'task_variety_score': self._calculate_variety_score(account.id, ['comment'])
            })
            self._record_task_assignment(account.id, 'comment')

        self.logger.info(f"🎯 多样化分配: 每个账号执行单一任务类型")
        self._log_assignment_diversity(tasks)

        return tasks

    def _balanced_assignment(self, accounts, interaction_config):
        """
        平衡分配模式（资源适中）

        策略：部分账号执行多任务，但避免重复的任务组合
        """
        like_count = interaction_config.get('like_count', 0)
        retweet_count = interaction_config.get('retweet_count', 0)
        comment_count = interaction_config.get('comment_count', 0)

        # 🎲 创建任务池
        task_pool = (['like'] * like_count +
                     ['retweet'] * retweet_count +
                     ['comment'] * comment_count)

        # 📊 按账号的任务类型历史排序
        sorted_accounts = self._sort_accounts_by_task_diversity(accounts)

        tasks = []
        account_task_assignments = {}  # 临时记录本次分配

        # 🔄 智能分配任务
        for task_type in task_pool:
            # 为这个任务类型选择最合适的账号
            best_account = self._find_best_account_for_task(
                sorted_accounts, task_type, account_task_assignments
            )

            if best_account:
                tasks.append({
                    'account': best_account,
                    'action_type': task_type,
                    'assignment_strategy': 'balanced'
                })

                # 记录本次分配
                if best_account.id not in account_task_assignments:
                    account_task_assignments[best_account.id] = []
                account_task_assignments[best_account.id].append(task_type)

                # 记录到历史
                self._record_task_assignment(best_account.id, task_type)

        self.logger.info(f"⚖️ 平衡分配: 智能避免重复任务组合")
        self._log_assignment_diversity(tasks)

        return tasks

    def _efficient_assignment(self, accounts, interaction_config):
        """
        高效分配模式（资源紧缺）

        策略：允许适度重复，但仍尽量保持多样化
        """
        like_count = interaction_config.get('like_count', 0)
        retweet_count = interaction_config.get('retweet_count', 0)
        comment_count = interaction_config.get('comment_count', 0)

        # 🎯 优先分配高价值任务（评论 > 转发 > 点赞）
        high_priority_tasks = (['comment'] * comment_count +
                              ['retweet'] * retweet_count)
        low_priority_tasks = ['like'] * like_count

        tasks = []
        used_accounts = set()

        # 📊 按多样化需求排序账号
        sorted_accounts = self._sort_accounts_by_task_diversity(accounts)

        # 🎯 先分配高优先级任务
        for task_type in high_priority_tasks:
            best_account = self._find_best_account_for_task(
                [acc for acc in sorted_accounts if acc.id not in used_accounts],
                task_type, {}
            )

            if best_account:
                tasks.append({
                    'account': best_account,
                    'action_type': task_type,
                    'assignment_strategy': 'efficient',
                    'priority': 'high'
                })
                used_accounts.add(best_account.id)
                self._record_task_assignment(best_account.id, task_type)

        # 🔄 分配低优先级任务（允许复用账号）
        account_index = 0
        for task_type in low_priority_tasks:
            account = sorted_accounts[account_index % len(sorted_accounts)]
            tasks.append({
                'account': account,
                'action_type': task_type,
                'assignment_strategy': 'efficient',
                'priority': 'low'
            })
            self._record_task_assignment(account.id, task_type)
            account_index += 1

        self.logger.warning(f"🔥 高效分配: 资源紧缺，允许适度重复")
        self._log_assignment_diversity(tasks)

        return tasks

    def _select_accounts_for_task_type(self, accounts, task_type, count):
        """为特定任务类型选择最合适的账号"""
        if count == 0:
            return []

        # 📊 按该任务类型的执行历史排序（执行少的优先）
        def get_task_type_score(account):
            history = self.account_task_history.get(account.id, {})
            task_count = history.get(task_type, 0)
            recent_tasks = self.recent_assignments.get(account.id, [])

            # 最近执行过相同任务类型的账号优先级降低
            recent_penalty = recent_tasks.count(task_type) * 10

            return task_count + recent_penalty

        sorted_accounts = sorted(accounts, key=get_task_type_score)
        return sorted_accounts[:count]

    def _sort_accounts_by_task_diversity(self, accounts):
        """按任务多样化需求排序账号"""
        def get_diversity_score(account):
            history = self.account_task_history.get(account.id, {})

            # 计算任务类型的方差（方差越大，多样性越好）
            task_counts = [history.get(task_type, 0) for task_type in ['like', 'retweet', 'comment']]
            if not task_counts:
                return 0

            mean_count = sum(task_counts) / len(task_counts)
            variance = sum((count - mean_count) ** 2 for count in task_counts) / len(task_counts)

            # 方差大的账号需要更多多样化（优先级高）
            return -variance

        return sorted(accounts, key=get_diversity_score)

    def _find_best_account_for_task(self, accounts, task_type, current_assignments):
        """为特定任务找到最佳账号"""
        if not accounts:
            return None

        def get_suitability_score(account):
            # 历史执行该任务类型的次数（越少越好）
            history = self.account_task_history.get(account.id, {})
            historical_count = history.get(task_type, 0)

            # 本次已分配的任务数（越少越好）
            current_count = len(current_assignments.get(account.id, []))

            # 最近是否执行过相同任务（避免连续重复）
            recent_tasks = self.recent_assignments.get(account.id, [])
            recent_penalty = 5 if task_type in recent_tasks[-2:] else 0

            return historical_count * 10 + current_count * 5 + recent_penalty

        return min(accounts, key=get_suitability_score)

    def _record_task_assignment(self, account_id, task_type):
        """记录任务分配历史"""
        # 更新总历史
        if account_id not in self.account_task_history:
            self.account_task_history[account_id] = {}

        if task_type not in self.account_task_history[account_id]:
            self.account_task_history[account_id][task_type] = 0

        self.account_task_history[account_id][task_type] += 1

        # 更新最近任务记录（保留最近10次）
        if account_id not in self.recent_assignments:
            self.recent_assignments[account_id] = []

        self.recent_assignments[account_id].append(task_type)
        if len(self.recent_assignments[account_id]) > 10:
            self.recent_assignments[account_id] = self.recent_assignments[account_id][-10:]

    def _calculate_variety_score(self, account_id, task_types):
        """计算任务多样性得分"""
        history = self.account_task_history.get(account_id, {})

        # 计算加上本次任务后的多样性
        temp_history = history.copy()
        for task_type in task_types:
            temp_history[task_type] = temp_history.get(task_type, 0) + 1

        # 计算标准差（多样性指标）
        counts = [temp_history.get(t, 0) for t in ['like', 'retweet', 'comment']]
        if not counts:
            return 0

        mean = sum(counts) / len(counts)
        variance = sum((c - mean) ** 2 for c in counts) / len(counts)
        return variance ** 0.5

    def _log_assignment_diversity(self, tasks):
        """记录分配多样性统计"""
        # 按账号统计任务类型
        account_tasks = {}
        for task in tasks:
            account_id = task['account'].id
            if account_id not in account_tasks:
                account_tasks[account_id] = []
            account_tasks[account_id].append(task['action_type'])

        # 统计任务组合类型
        combination_stats = {}
        for account_id, task_types in account_tasks.items():
            combination = '+'.join(sorted(set(task_types)))
            combination_stats[combination] = combination_stats.get(combination, 0) + 1

        self.logger.info(f"📊 任务组合统计:")
        for combination, count in combination_stats.items():
            self.logger.info(f"   {combination}: {count}个账号")

        # 计算多样性指标
        unique_combinations = len(combination_stats)
        total_accounts = len(account_tasks)
        diversity_ratio = unique_combinations / total_accounts if total_accounts > 0 else 0

        self.logger.info(f"🎯 多样性指标: {diversity_ratio:.2f} ({unique_combinations}/{total_accounts})")


class CommentGenerator(LoggerMixin):
    """评论生成器"""

    def __init__(self):
        # 🎯 大幅扩展评论模板库（100+个模板）
        self.comment_templates = [
            # 基础赞同类
            "很棒的分享！", "说得太对了！", "学到了，谢谢分享", "有道理！", "支持！",
            "赞同这个观点", "很有启发性", "感谢分享这个信息", "非常有用的内容", "完全同意！",
            "这个想法很不错", "值得思考", "很好的观点", "受益匪浅", "说到心坎里了",

            # 学习收获类
            "学到了很多", "涨知识了", "原来如此", "恍然大悟", "茅塞顿开",
            "受教了", "长见识了", "获益良多", "深受启发", "醍醐灌顶",
            "眼前一亮", "豁然开朗", "收获满满", "学习了", "mark一下",

            # 情感表达类
            "太赞了", "厉害了", "牛逼", "666", "绝了", "太棒了", "amazing",
            "incredible", "fantastic", "awesome", "brilliant", "excellent",
            "outstanding", "impressive", "remarkable", "wonderful",

            # 互动交流类
            "求更多分享", "期待后续", "继续关注", "已关注", "转发了",
            "收藏了", "分享给朋友", "推荐给大家", "值得推广", "必须转发",
            "已收藏", "马克", "码住了", "保存了", "截图了",

            # 专业认可类
            "专业", "权威", "靠谱", "实用", "干货", "精华", "核心",
            "要点", "关键", "重点", "精辟", "到位", "准确", "中肯", "客观",

            # 时间相关类
            "及时雨", "正需要", "来得正好", "恰到好处", "正合适",
            "刚好需要", "太及时了", "正想了解", "求之不得", "雪中送炭",

            # 简短回应类
            "赞", "顶", "好", "棒", "妙", "对", "是的", "没错", "确实",
            "真的", "同感", "+1", "me too", "exactly", "true", "right",

            # 感谢类
            "谢谢", "感谢", "多谢", "thanks", "thank you", "thx", "3Q",
            "感激", "感恩", "谢谢分享", "感谢科普", "谢谢提醒",

            # 疑问讨论类
            "有道理吗", "真的假的", "是这样吗", "确定吗", "可信吗",
            "有依据吗", "求证实", "求解释", "怎么理解", "什么意思",

            # 个人感受类
            "我也觉得", "深有同感", "感同身受", "心有戚戚", "深以为然",
            "不谋而合", "英雄所见略同", "所见略同", "想法一致", "观点相同"
        ]

        # 🎯 扩展修饰词库
        self.modifiers = {
            'prefix': [
                "确实", "真的", "的确", "果然", "不愧是", "必须说", "不得不说",
                "说实话", "老实说", "坦白讲", "客观来说", "公正地说", "实事求是地说"
            ],
            'suffix': [
                "呢", "啊", "哦", "嗯", "吧", "呀", "哈", "嘿", "咦", "哇",
                "了", "的", "呗", "咯", "喔", "额", "嗯嗯", "哈哈", "嘻嘻"
            ],
            'intensifier': [
                "非常", "特别", "超级", "极其", "相当", "十分", "很", "挺", "蛮",
                "真", "太", "好", "超", "巨", "贼", "老", "死", "狂"
            ]
        }

        # 🎯 安全emoji（BMP范围内）
        self.safe_emojis = [
            ":)", ":(", ":D", ":P", "^_^", "~_~", "@_@", "XD", ":-)", ":-(",
            ":-D", ":-P", "o_o", "O_O", ">_<", "T_T", "=_=", "-_-", "^o^", "^v^"
        ]
    
    def generate_comment(self, post_content: str = None) -> str:
        """
        生成评论内容（增强版：高度随机化）

        Args:
            post_content: 帖子内容（用于生成相关评论）

        Returns:
            评论内容
        """
        try:
            # 🎯 多种生成策略随机选择（优化权重：减少简单评论，增加丰富评论）
            generation_strategies = [
                self._generate_simple_comment,      # 20% 简单评论（从40%减少）
                self._generate_modified_comment,    # 35% 修饰评论（从30%增加）
                self._generate_combined_comment,    # 30% 组合评论（从20%增加）
                self._generate_creative_comment     # 15% 创意评论（从10%增加）
            ]

            # 根据权重选择策略（优化后更倾向于生成丰富内容）
            strategy_weights = [20, 35, 30, 15]
            strategy = random.choices(generation_strategies, weights=strategy_weights)[0]

            # 生成评论
            comment = strategy()

            # 🎯 随机后处理
            comment = self._apply_random_post_processing(comment)

            return comment

        except Exception as e:
            self.logger.warning(f"生成评论失败: {e}")
            return "很棒的分享！"

    def _generate_simple_comment(self) -> str:
        """生成简单评论"""
        return random.choice(self.comment_templates)

    def _generate_modified_comment(self) -> str:
        """生成修饰评论"""
        base_comment = random.choice(self.comment_templates)

        # 随机添加前缀修饰
        if random.random() < 0.4:
            prefix = random.choice(self.modifiers['prefix'])
            base_comment = f"{prefix}{base_comment}"

        # 随机添加强化词
        if random.random() < 0.3:
            intensifier = random.choice(self.modifiers['intensifier'])
            # 在适当位置插入强化词
            if "很" in base_comment:
                base_comment = base_comment.replace("很", intensifier, 1)
            elif "非常" in base_comment:
                base_comment = base_comment.replace("非常", intensifier, 1)
            else:
                base_comment = f"{intensifier}{base_comment}"

        # 随机添加后缀
        if random.random() < 0.5:
            suffix = random.choice(self.modifiers['suffix'])
            base_comment = f"{base_comment}{suffix}"

        return base_comment

    def _generate_combined_comment(self) -> str:
        """生成组合评论（增强版：生成更丰富的内容）"""
        # 🎯 优化：选择不同长度的评论进行组合
        short_comments = [c for c in self.comment_templates if len(c) <= 6]
        medium_comments = [c for c in self.comment_templates if 6 < len(c) <= 12]

        if len(short_comments) < 2:
            short_comments = self.comment_templates[:10]
        if len(medium_comments) < 1:
            medium_comments = self.comment_templates[10:20]

        # 🎯 增加组合策略：70%概率生成更长的组合
        if random.random() < 0.7:
            # 长组合：1个中等长度 + 1-2个短评论
            parts = [random.choice(medium_comments)]
            num_short = random.choice([1, 2])
            parts.extend(random.sample(short_comments, min(num_short, len(short_comments))))
        else:
            # 短组合：2-3个短评论
            num_parts = random.choice([2, 3])
            parts = random.sample(short_comments, min(num_parts, len(short_comments)))

        # 🎯 丰富的连接符
        connectors = ["，", "！", "。", "，真的", "，确实", "，非常", "，特别", "，很", "，超级"]
        connector = random.choice(connectors)

        return connector.join(parts)

    def _generate_creative_comment(self) -> str:
        """生成创意评论"""
        # 创意模式：随机组合不同元素
        elements = []

        # 添加感叹词
        if random.random() < 0.6:
            exclamations = ["哇", "哦", "嗯", "咦", "嘿", "呀", "啊"]
            elements.append(random.choice(exclamations))

        # 添加主体评论
        main_comment = random.choice(self.comment_templates)
        elements.append(main_comment)

        # 添加补充说明
        if random.random() < 0.4:
            supplements = ["真的", "确实", "没错", "对的", "是的", "同意"]
            elements.append(random.choice(supplements))

        # 随机连接
        separators = ["，", "！", "。", " "]
        separator = random.choice(separators)

        return separator.join(elements)

    def _apply_random_post_processing(self, comment: str) -> str:
        """应用随机后处理"""
        # 随机添加安全emoji
        if random.random() < 0.3:  # 30%概率添加emoji
            emoji = random.choice(self.safe_emojis)
            if random.random() < 0.5:
                comment = f"{emoji} {comment}"  # 前置
            else:
                comment = f"{comment} {emoji}"  # 后置

        # 随机重复某些字符（模拟激动）
        if random.random() < 0.1:  # 10%概率
            if "！" in comment:
                comment = comment.replace("！", "！！", 1)
            elif "。" in comment:
                comment = comment.replace("。", "！", 1)

        # 随机添加空格（模拟输入习惯）
        if random.random() < 0.2:  # 20%概率
            if len(comment) > 3:
                pos = random.randint(1, len(comment) - 1)
                comment = comment[:pos] + " " + comment[pos:]

        return comment


class InteractionExecutor(LoggerMixin):
    """互动执行器"""
    
    def __init__(self):
        self.settings = get_settings()
        self.browser_pool = get_browser_pool()
        self.anti_detection = AntiDetectionEngine()
        self.login_manager = LoginManager()
        self.comment_generator = CommentGenerator()

        # 🧠 智能分配管理器
        self.account_rotation_manager = AccountRotationManager()
        self.task_type_rotation_manager = TaskTypeRotationManager()
    
    async def execute_interaction(self, account: Account, post_url: str,
                                action_type: str, comment_content: str = None) -> bool:
        """
        执行互动操作

        Args:
            account: 账号对象
            post_url: 帖子URL
            action_type: 互动类型
            comment_content: 评论内容（评论时使用）

        Returns:
            是否成功
        """
        driver_wrapper = None
        try:
            self.logger.info(f"开始互动: {account.username}, {action_type}, {post_url}")
            self.log_account_start(account.username, "interaction")

            # 获取WebDriver
            driver_wrapper = await self.browser_pool.get_driver(account)
            if not driver_wrapper:
                raise Exception("获取WebDriver失败")

            # 确保账号已登录
            if not await self.login_manager.login_account(account):
                raise Exception("账号登录失败")

            # 访问帖子
            await driver_wrapper.get(post_url)
            # 等待页面加载完成
            await driver_wrapper.execute_async(
                lambda: WebDriverWait(driver_wrapper.driver, 10).until(
                    lambda d: d.execute_script('return document.readyState') == 'complete'
                )
            )
            await self.anti_detection.simulate_human_behavior(driver_wrapper)

            # 执行具体互动操作
            success = False
            if action_type == INTERACTION_TYPES['LIKE']:
                success = await self._execute_like(driver_wrapper)
            elif action_type == INTERACTION_TYPES['RETWEET']:
                success = await self._execute_retweet(driver_wrapper)
            elif action_type == INTERACTION_TYPES['COMMENT']:
                if not comment_content:
                    comment_content = self.comment_generator.generate_comment()
                success = await self._execute_comment(driver_wrapper, comment_content)
            else:
                raise Exception(f"不支持的互动类型: {action_type}")

            if success:
                self.logger.info(f"互动成功: {account.username}, {action_type}")
                self.log_interaction_action(account.username, post_url, action_type, True, "")
            else:
                self.logger.warning(f"互动失败: {account.username}, {action_type}")
                self.log_interaction_action(account.username, post_url, action_type, False, "操作失败")

            return success

        except Exception as e:
            self.logger.error(f"互动异常: {account.username}, {action_type}, {e}")
            self.log_interaction_action(account.username, post_url, action_type, False, str(e))
            return False
    
    async def _execute_like(self, driver_wrapper) -> bool:
        """执行点赞"""
        try:
            # 查找点赞按钮
            wait = WebDriverWait(driver_wrapper.driver, 10)
            like_button = await driver_wrapper.execute_async(
                lambda: wait.until(EC.visibility_of_element_located((By.CSS_SELECTOR, X_SELECTORS['LIKE_BUTTON'])))
            )

            # 检查是否已经点赞
            button_aria_label = await driver_wrapper.execute_async(
                lambda: like_button.get_attribute('aria-label')
            )
            if button_aria_label and 'liked' in button_aria_label.lower():
                self.logger.debug("已经点赞过了")
                return True

            # 模拟人类行为
            await self.anti_detection.simulate_human_behavior(driver_wrapper)

            # 点击点赞
            await self.anti_detection.safe_click(driver_wrapper, X_SELECTORS['LIKE_BUTTON'])

            # 等待操作完成
            await asyncio.sleep(random.uniform(1, 2))
            
            return True
            
        except Exception as e:
            self.logger.error(f"执行点赞失败: {e}")
            return False
    
    async def _execute_retweet(self, driver_wrapper) -> bool:
        """执行转发"""
        try:
            # 查找转发按钮
            wait = WebDriverWait(driver_wrapper.driver, 10)
            retweet_button = await driver_wrapper.execute_async(
                lambda: wait.until(EC.visibility_of_element_located((By.CSS_SELECTOR, X_SELECTORS['RETWEET_BUTTON'])))
            )

            # 模拟人类行为
            await self.anti_detection.simulate_human_behavior(driver_wrapper)

            # 点击转发按钮
            await self.anti_detection.safe_click(driver_wrapper, X_SELECTORS['RETWEET_BUTTON'])

            # 等待转发菜单出现
            await asyncio.sleep(random.uniform(0.5, 1.0))

            # 点击确认转发
            wait = WebDriverWait(driver_wrapper.driver, 5)
            confirm_button = await driver_wrapper.execute_async(
                lambda: wait.until(EC.visibility_of_element_located((By.CSS_SELECTOR, X_SELECTORS['RETWEET_CONFIRM'])))
            )
            await driver_wrapper.execute_async(confirm_button.click)

            # 等待操作完成
            await asyncio.sleep(random.uniform(1, 2))

            return True

        except Exception as e:
            self.logger.error(f"执行转发失败: {e}")
            return False
    
    async def _execute_comment(self, driver_wrapper, comment_content: str) -> bool:
        """执行评论"""
        try:
            # 查找回复按钮
            wait = WebDriverWait(driver_wrapper.driver, 10)
            reply_button = await driver_wrapper.execute_async(
                lambda: wait.until(EC.visibility_of_element_located((By.CSS_SELECTOR, X_SELECTORS['REPLY_BUTTON'])))
            )

            # 模拟人类行为
            await self.anti_detection.simulate_human_behavior(driver_wrapper)

            # 点击回复按钮
            await self.anti_detection.safe_click(driver_wrapper, X_SELECTORS['REPLY_BUTTON'])

            # 等待回复框出现
            await asyncio.sleep(random.uniform(1, 2))

            # 输入评论内容
            wait = WebDriverWait(driver_wrapper.driver, 5)
            comment_textarea = await driver_wrapper.execute_async(
                lambda: wait.until(EC.visibility_of_element_located((By.CSS_SELECTOR, X_SELECTORS['TWEET_COMPOSE'])))
            )

            # 模拟人类打字
            await self.anti_detection.safe_type(driver_wrapper, X_SELECTORS['TWEET_COMPOSE'], comment_content)

            # 等待输入完成
            await asyncio.sleep(random.uniform(1, 2))

            # 点击发送按钮
            wait = WebDriverWait(driver_wrapper.driver, 5)
            send_button = await driver_wrapper.execute_async(
                lambda: wait.until(EC.visibility_of_element_located((By.CSS_SELECTOR, '[data-testid="tweetButton"]')))
            )
            await driver_wrapper.execute_async(send_button.click)

            # 等待发送完成
            await asyncio.sleep(random.uniform(2, 3))

            return True

        except Exception as e:
            self.logger.error(f"执行评论失败: {e}")
            return False
    
    async def batch_interact(self, accounts: List[Account], post_url: str,
                           interaction_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        批量互动
        
        Args:
            accounts: 账号列表
            post_url: 帖子URL
            interaction_config: 互动配置
            
        Returns:
            执行结果统计
        """
        try:
            self.logger.info(f"⚡ 开始智能互动分配: {post_url}")

            # 📊 分析互动需求
            like_count = interaction_config.get('like_count', 0)
            retweet_count = interaction_config.get('retweet_count', 0)
            comment_count = interaction_config.get('comment_count', 0)
            total_interactions = like_count + retweet_count + comment_count
            available_accounts = len(accounts)

            if total_interactions == 0:
                self.logger.warning("没有设置任何互动任务")
                return {'success_count': 0, 'failed_count': 0, 'total_count': 0}

            # 🧠 智能自适应分配策略
            resource_ratio = available_accounts / total_interactions
            self.logger.info(f"📊 资源分析: {available_accounts}个账号 / {total_interactions}个互动 = {resource_ratio:.2f}")

            # 🔄 第一步：账号轮换分配
            rotation_accounts = self.account_rotation_manager.get_rotation_assignment(accounts, total_interactions)

            # 🎯 第二步：智能混合分配（70%轮换 + 30%随机）
            rotation_count = int(total_interactions * 0.7)
            random_count = total_interactions - rotation_count

            selected_accounts = rotation_accounts[:rotation_count]
            if random_count > 0:
                remaining_accounts = [acc for acc in accounts if acc not in selected_accounts]
                if remaining_accounts:
                    random.shuffle(remaining_accounts)
                    selected_accounts.extend(remaining_accounts[:random_count])

            # 🎲 随机打乱最终选择的账号
            random.shuffle(selected_accounts)

            # 🎯 第三步：任务类型多样化分配
            task_assignments = self.task_type_rotation_manager.get_diversified_assignment(
                selected_accounts[:total_interactions], interaction_config
            )

            # 📋 第四步：创建执行任务
            execution_tasks = []
            for assignment in task_assignments:
                account = assignment['account']
                action_type = assignment['action_type']

                # 生成评论内容（如果需要）
                comment_content = None
                if action_type == 'comment':
                    # 🎯 使用专门的去重评论生成方法（25-30个字符）
                    comment_content = self._generate_unique_comment_with_random_chars()

                # 创建数据库任务记录
                db_task = self._create_interaction_task(account, post_url, action_type, comment_content)
                if db_task:
                    execution_tasks.append({
                        'account': account,
                        'action_type': action_type,
                        'comment_content': comment_content,
                        'assignment_info': assignment
                    })

                    # 记录账号使用
                    self.account_rotation_manager.record_usage(account.id, action_type)

            # 📊 分析任务分配情况
            account_task_count = {}
            for task in execution_tasks:
                account_id = task['account'].id
                account_task_count[account_id] = account_task_count.get(account_id, 0) + 1

            unique_accounts = len(set(t['account'].id for t in execution_tasks))
            self.logger.info(f"🎯 智能分配完成: {len(execution_tasks)}个任务分配给{unique_accounts}个不同账号")

            # 🔍 详细分配情况
            multi_task_accounts = {acc_id: count for acc_id, count in account_task_count.items() if count > 1}
            if multi_task_accounts:
                self.logger.info(f"📋 多任务账号分配详情:")
                for task in execution_tasks:
                    if task['account'].id in multi_task_accounts:
                        account_name = task['account'].username
                        action_type = task['action_type']
                        self.logger.info(f"   {account_name}: {action_type}")
            else:
                self.logger.info(f"✅ 所有账号都只分配了单个任务")

            # ⚡ 第五步：智能并发执行（修复：动态超时时间）
            start_time = time.time()

            # 🔄 标记关联互动账号为持久化（关键改进！）
            involved_account_ids = set()
            for task in execution_tasks:
                account_id = task['account'].id
                involved_account_ids.add(account_id)
                self.browser_pool.mark_account_persistent(account_id)

            self.logger.info(f"🔄 已标记 {len(involved_account_ids)} 个关联互动账号为持久化")

            # 🚀 智能并发控制（提前定义）
            max_concurrent = min(
                interaction_config.get('max_concurrent', 15),  # 用户设置的并发数
                len(execution_tasks),  # 任务数量
                15  # 系统最大并发限制
            )

            # 🔧 动态计算超时时间（修复：更合理的时间估算）
            base_timeout = interaction_config.get('batch_timeout', 600)  # 默认10分钟（从5分钟增加）
            task_count = len(execution_tasks)
            estimated_time_per_task = 90  # 每个任务预估90秒（从45秒增加）

            # 考虑并发执行，计算实际需要时间
            concurrent_groups = math.ceil(task_count / max_concurrent)
            estimated_total_time = concurrent_groups * estimated_time_per_task

            # 设置超时时间（预估时间 + 100%缓冲，确保充足时间）
            MAX_EXECUTION_TIME = max(base_timeout, int(estimated_total_time * 2.0))

            self.logger.info(f"⏰ 动态超时计算: {task_count}个任务，{concurrent_groups}个并发组，预估{estimated_total_time}秒，超时{MAX_EXECUTION_TIME}秒")

            # 🔧 调试模式检查
            debug_mode = interaction_config.get('debug_mode', False)
            self.debug_mode = debug_mode  # 设置实例变量供其他方法使用
            if debug_mode:
                self.logger.info("🔧 调试模式已启用，将输出详细日志和截图")
                # 在调试模式下，将日志级别临时设置为DEBUG
                import logging
                original_level = self.logger.level
                self.logger.setLevel(logging.DEBUG)

            # 🎭 反检测功能配置
            self.enable_natural_typing = interaction_config.get('enable_natural_typing', True)
            if self.enable_natural_typing:
                self.logger.info("🎭 自然输入反检测功能已启用")
            else:
                self.logger.info("⚡ 使用快速输入模式（已禁用反检测）")

            # � 智能登录检查配置
            self.enable_smart_login_check = interaction_config.get('enable_smart_login_check', True)
            if self.enable_smart_login_check:
                self.logger.info("🔐 智能登录状态检查已启用")
            else:
                self.logger.info("⚡ 已禁用登录检查（快速模式）")

            # 🔄 会话稳定化配置
            self.enable_session_stabilization = interaction_config.get('enable_session_stabilization', True)
            self.stabilization_level = interaction_config.get('stabilization_level', 'normal')  # light, normal, heavy
            if self.enable_session_stabilization:
                self.logger.info(f"🔄 会话稳定化已启用 (级别: {self.stabilization_level})")
            else:
                self.logger.info("⚡ 已禁用会话稳定化（快速模式）")

            # �🚀 智能并发控制（已在前面定义）

            self.logger.info(f"🚀 智能并发执行: {len(execution_tasks)}个任务，最大并发{max_concurrent}个，目标{MAX_EXECUTION_TIME}秒内完成")

            # 执行任务
            results = await self._execute_ultra_fast_interactions(execution_tasks, post_url, max_concurrent, MAX_EXECUTION_TIME)

            # 🔧 恢复日志级别
            if debug_mode:
                self.logger.setLevel(original_level)

            # 📊 统计结果
            total_time = time.time() - start_time
            success_count = sum(1 for r in results if r.get('success', False))
            failed_count = len(results) - success_count

            # 📈 计算性能指标
            tasks_per_second = len(results) / total_time if total_time > 0 else 0
            success_rate = (success_count / len(results) * 100) if results else 0

            result_summary = {
                'success_count': success_count,
                'failed_count': failed_count,
                'total_count': len(results),
                'execution_time': total_time,
                'tasks_per_second': tasks_per_second,
                'success_rate': success_rate,
                'details': results
            }

            self.logger.info(f"⚡ 智能互动完成!")
            self.logger.info(f"   总耗时: {total_time:.1f}秒")
            self.logger.info(f"   成功率: {success_count}/{len(results)} ({success_rate:.1f}%)")
            self.logger.info(f"   处理速度: {tasks_per_second:.1f}任务/秒")

            return result_summary

        except Exception as e:
            self.logger.error(f"批量互动失败: {e}")
            return {
                'success_count': 0,
                'failed_count': 0,
                'total_count': 0,
                'error': str(e)
            }

    async def _execute_concurrent_interactions(self, tasks: List[tuple], post_url: str, max_concurrent: int) -> List[Dict[str, Any]]:
        """
        并发执行互动任务

        Args:
            tasks: 任务列表 [(account, action_type, comment_content), ...]
            post_url: 帖子URL
            max_concurrent: 最大并发数

        Returns:
            执行结果列表
        """
        try:
            # 创建信号量控制并发数
            semaphore = asyncio.Semaphore(max_concurrent)

            async def execute_single_interaction_with_semaphore(task_info):
                """带信号量控制的单个互动执行"""
                account, action_type, comment_content = task_info

                async with semaphore:
                    try:
                        # 添加随机延迟避免同时启动
                        initial_delay = random.uniform(0.5, 2.0)
                        await asyncio.sleep(initial_delay)

                        self.logger.info(f"🎯 开始互动: {account.username} - {action_type}")

                        success = await self.execute_interaction(
                            account, post_url, action_type, comment_content
                        )

                        result = {
                            'account_id': account.id,
                            'account_username': account.username,
                            'action_type': action_type,
                            'success': success
                        }

                        if success:
                            self.logger.info(f"✅ 互动成功: {account.username} - {action_type}")
                            # 成功后短暂延迟
                            await asyncio.sleep(random.uniform(1, 3))
                        else:
                            self.logger.warning(f"❌ 互动失败: {account.username} - {action_type}")

                        return result

                    except Exception as e:
                        self.logger.error(f"互动任务执行失败: {account.username} - {action_type}, {e}")
                        return {
                            'account_id': account.id,
                            'account_username': account.username,
                            'action_type': action_type,
                            'success': False,
                            'error': str(e)
                        }

            # 创建并发任务
            concurrent_tasks = [
                execute_single_interaction_with_semaphore(task)
                for task in tasks
            ]

            # 执行所有并发任务
            self.logger.info(f"🚀 启动 {len(concurrent_tasks)} 个并发互动任务...")
            results = await asyncio.gather(*concurrent_tasks, return_exceptions=True)

            # 处理异常结果
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    account = tasks[i][0]
                    action_type = tasks[i][1]
                    processed_results.append({
                        'account_id': account.id,
                        'account_username': account.username,
                        'action_type': action_type,
                        'success': False,
                        'error': str(result)
                    })
                    self.logger.error(f"并发任务异常: {account.username} - {action_type}, {result}")
                else:
                    processed_results.append(result)

            return processed_results

        except Exception as e:
            self.logger.error(f"并发互动执行失败: {e}")
            # 返回失败结果
            return [
                {
                    'account_id': task[0].id,
                    'account_username': task[0].username,
                    'action_type': task[1],
                    'success': False,
                    'error': str(e)
                }
                for task in tasks
            ]

    async def _execute_sequential_interactions(self, tasks: List[tuple], post_url: str) -> List[Dict[str, Any]]:
        """
        串行执行互动任务

        Args:
            tasks: 任务列表 [(account, action_type, comment_content), ...]
            post_url: 帖子URL

        Returns:
            执行结果列表
        """
        results = []

        for i, (account, action_type, comment_content) in enumerate(tasks):
            try:
                self.logger.info(f"📝 串行互动 ({i+1}/{len(tasks)}): {account.username} - {action_type}")

                success = await self.execute_interaction(
                    account, post_url, action_type, comment_content
                )

                result = {
                    'account_id': account.id,
                    'account_username': account.username,
                    'action_type': action_type,
                    'success': success
                }

                results.append(result)

                if success:
                    self.logger.info(f"✅ 互动成功: {account.username} - {action_type}")
                    # 任务间延迟
                    delay = random.uniform(2, 5)
                    await asyncio.sleep(delay)
                else:
                    self.logger.warning(f"❌ 互动失败: {account.username} - {action_type}")

            except Exception as e:
                self.logger.error(f"串行互动任务执行失败: {account.username} - {action_type}, {e}")
                results.append({
                    'account_id': account.id,
                    'account_username': account.username,
                    'action_type': action_type,
                    'success': False,
                    'error': str(e)
                })

        return results
    
    def _create_interaction_task(self, account: Account, post_url: str,
                               action_type: str, comment_content: str = None) -> Optional[InteractionTask]:
        """
        创建互动任务记录
        
        Args:
            account: 账号对象
            post_url: 帖子URL
            action_type: 互动类型
            comment_content: 评论内容
            
        Returns:
            互动任务对象
        """
        session = get_db_session()
        try:
            task = InteractionTask(
                post_url=post_url,
                account_id=account.id,
                action_type=action_type,
                comment_content=comment_content,
                status=TASK_STATUS['PENDING']
            )
            
            session.add(task)
            session.commit()
            
            return task
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"创建互动任务失败: {e}")
            return None
        finally:
            session.close()

    async def _execute_ultra_fast_interactions(self, tasks: List[Dict], post_url: str, max_concurrent: int, max_time: float) -> List[Dict]:
        """
        极速并发执行互动任务（修复版：支持同账号任务顺序执行）

        Args:
            tasks: 任务列表，每个任务包含account, action_type, comment_content等
            post_url: 帖子URL
            max_concurrent: 最大并发数
            max_time: 最大执行时间（秒）

        Returns:
            执行结果列表
        """
        if not tasks:
            return []

        # 🔧 按账号分组任务，确保同账号任务顺序执行
        account_task_groups = {}
        for task in tasks:
            account_id = task['account'].id
            if account_id not in account_task_groups:
                account_task_groups[account_id] = []
            account_task_groups[account_id].append(task)

        self.logger.info(f"📊 任务分组: {len(account_task_groups)}个账号，{len(tasks)}个任务")

        # 🔍 检查是否有账号被分配多个任务
        multi_task_accounts = {acc_id: tasks for acc_id, tasks in account_task_groups.items() if len(tasks) > 1}
        if multi_task_accounts:
            self.logger.warning(f"⚠️ 发现{len(multi_task_accounts)}个账号被分配多个任务:")
            for acc_id, acc_tasks in multi_task_accounts.items():
                account_name = acc_tasks[0]['account'].username
                task_types = [t['action_type'] for t in acc_tasks]
                self.logger.warning(f"   {account_name}: {task_types}")

        # 🔒 为每个账号创建独立的信号量，确保同账号任务顺序执行
        account_semaphores = {acc_id: asyncio.Semaphore(1) for acc_id in account_task_groups.keys()}

        # 🌐 全局并发控制信号量
        global_semaphore = asyncio.Semaphore(max_concurrent)

        async def execute_account_tasks_sequentially(account_id, account_tasks):
            """为单个账号顺序执行所有任务"""
            results = []
            account_name = account_tasks[0]['account'].username

            async with account_semaphores[account_id]:  # 确保同账号任务不并发
                self.logger.info(f"🎯 开始为账号 {account_name} 顺序执行 {len(account_tasks)} 个任务")

                for i, task_info in enumerate(account_tasks):
                    try:
                        action_type = task_info['action_type']
                        comment_content = task_info.get('comment_content')

                        # 🎯 单个任务超时控制（修复：大幅增加超时时间）
                        # 考虑WebDriver创建(30秒) + 稳定化等待(8-10秒) + 页面加载(5-10秒) + 互动操作(10-15秒)
                        base_timeout = getattr(self, 'task_timeout', 90)  # 默认90秒（从45秒增加到90秒）
                        task_timeout = min(base_timeout, max_time - 15)  # 保留15秒缓冲
                        task_timeout = max(task_timeout, 60)  # 最少60秒（从30秒增加到60秒）

                        self.logger.debug(f"⏰ 任务超时设置: {task_timeout}秒 (账号: {account_name})")

                        async with global_semaphore:  # 全局并发控制
                            self.logger.info(f"🎯 执行任务 {i+1}/{len(account_tasks)}: {account_name} - {action_type}")

                            # ⚡ 执行互动（带超时）
                            success = await asyncio.wait_for(
                                self._execute_single_interaction_fast(task_info['account'], post_url, action_type, comment_content),
                                timeout=task_timeout
                            )

                            result = {
                                'account_id': task_info['account'].id,
                                'account_username': account_name,
                                'action_type': action_type,
                                'success': success,
                                'assignment_info': task_info.get('assignment_info', {}),
                                'task_sequence': i + 1,
                                'total_tasks': len(account_tasks)
                            }

                            if success:
                                self.logger.info(f"✅ 任务成功 {i+1}/{len(account_tasks)}: {account_name} - {action_type}")
                                # 🕐 任务间延迟（重要！避免操作过快）
                                if i < len(account_tasks) - 1:  # 不是最后一个任务
                                    delay = random.uniform(2, 4)  # 2-4秒延迟
                                    self.logger.info(f"⏳ 任务间延迟 {delay:.1f}秒")
                                    await asyncio.sleep(delay)
                            else:
                                self.logger.error(f"❌ 任务失败 {i+1}/{len(account_tasks)}: {account_name} - {action_type}")

                            results.append(result)

                    except asyncio.TimeoutError:
                        self.logger.warning(f"⏰ 任务超时: {account_name} - {task_info['action_type']}")
                        results.append({
                            'account_id': task_info['account'].id,
                            'account_username': account_name,
                            'action_type': task_info['action_type'],
                            'success': False,
                            'error': 'timeout',
                            'task_sequence': i + 1,
                            'total_tasks': len(account_tasks)
                        })
                    except Exception as e:
                        self.logger.error(f"❌ 任务异常: {account_name} - {task_info['action_type']} - {e}")
                        results.append({
                            'account_id': task_info['account'].id,
                            'account_username': account_name,
                            'action_type': task_info['action_type'],
                            'success': False,
                            'error': str(e),
                            'task_sequence': i + 1,
                            'total_tasks': len(account_tasks)
                        })

                self.logger.info(f"✅ 账号 {account_name} 所有任务执行完成: {len(results)}个结果")

            return results

        # 🚀 为每个账号创建并发任务（账号间并发，账号内顺序）
        account_concurrent_tasks = [
            execute_account_tasks_sequentially(account_id, account_tasks)
            for account_id, account_tasks in account_task_groups.items()
        ]

        try:
            # ⏰ 整体超时控制
            account_results = await asyncio.wait_for(
                asyncio.gather(*account_concurrent_tasks, return_exceptions=True),
                timeout=max_time
            )

            # 🔧 合并所有账号的结果
            all_results = []
            for account_result in account_results:
                if isinstance(account_result, list):
                    all_results.extend(account_result)
                elif isinstance(account_result, Exception):
                    self.logger.error(f"账号任务组执行异常: {account_result}")
                    all_results.append({
                        'success': False,
                        'error': str(account_result)
                    })

            return all_results

        except asyncio.TimeoutError:
            self.logger.warning(f"⏰ 整体执行超时: {max_time}秒")
            return [{'success': False, 'error': 'batch_timeout'} for _ in tasks]

    async def _execute_single_interaction_fast(self, account: Account, post_url: str, action_type: str, comment_content: str = None, retry_count: int = 0) -> bool:
        """
        单个互动的极速执行版本（优化版）- 支持智能重试

        优化点：
        1. 先检查登录状态，确保账号已登录
        2. 使用缓存的WebDriver
        3. 最小化等待时间
        4. 快速失败机制
        5. 🔄 智能重试机制：失败时cookie重新登录后再试一次
        """
        try:
            self.logger.info(f"🎯 开始执行互动: {account.username} - {action_type} - {post_url}")

            # 1. 快速获取WebDriver（使用缓存）
            driver_wrapper = await self.browser_pool.get_driver(account)
            if not driver_wrapper:
                self.logger.error(f"❌ 获取WebDriver失败: {account.username}")
                return False

            self.logger.debug(f"✅ WebDriver获取成功: {account.username}")

            # 2. 🔐 优先进行登录状态检查和登录（关键改进！）
            if getattr(self, 'enable_smart_login_check', True):
                try:
                    self.logger.debug(f"🔐 开始登录状态检查: {account.username}")

                    # 先访问X主页进行状态检查
                    await driver_wrapper.get("https://x.com")
                    await asyncio.sleep(1)  # 短暂等待页面加载

                    login_required = await self._smart_login_check(driver_wrapper, account)
                    if login_required:
                        self.logger.warning(f"⚠️ 检测到需要登录，开始登录流程: {account.username}")
                        # 🔧 修复：使用现有WebDriver进行登录，避免重复创建
                        if not await self._login_with_existing_driver(driver_wrapper, account):
                            self.logger.error(f"❌ 登录失败: {account.username}")
                            return False

                        self.logger.info(f"✅ 登录成功: {account.username}")
                        # 🔄 登录成功后进行会话稳定化等待（关键改进！）
                        if getattr(self, 'enable_session_stabilization', True):
                            await self._stabilize_login_session(driver_wrapper, account)
                        else:
                            await asyncio.sleep(1)  # 最小等待
                    else:
                        self.logger.debug(f"✅ 账号已登录: {account.username}")
                        # 即使已登录，也进行轻量级稳定化
                        if getattr(self, 'enable_session_stabilization', True):
                            await self._light_stabilization(driver_wrapper, account)
                        else:
                            await asyncio.sleep(0.5)  # 最小等待

                except Exception as e:
                    self.logger.warning(f"⚠️ 登录状态检查异常: {account.username} - {e}")
                    # 如果检查失败，尝试执行登录以确保安全
                    try:
                        await self.login_manager.login_account(account)
                        self.logger.info(f"✅ 备用登录成功: {account.username}")
                    except Exception as login_e:
                        self.logger.error(f"❌ 备用登录失败: {account.username} - {login_e}")
                        return False
            else:
                self.logger.debug(f"已禁用智能登录检查: {account.username}")

            # 3. 访问目标页面（现在账号已确保登录且稳定）
            try:
                self.logger.debug(f"🌐 开始访问目标页面: {post_url}")
                await asyncio.wait_for(driver_wrapper.get(post_url), timeout=15)
                self.logger.debug(f"✅ 目标页面访问成功: {account.username}")

                # 🔄 页面访问后的稳定化等待（关键改进！）
                if getattr(self, 'enable_session_stabilization', True):
                    await self._stabilize_page_after_navigation(driver_wrapper, account, post_url)
                else:
                    await asyncio.sleep(1)  # 最小等待

            except asyncio.TimeoutError:
                self.logger.error(f"❌ 目标页面加载超时: {account.username} - {post_url}")
                return False
            except Exception as e:
                self.logger.error(f"❌ 目标页面访问失败: {account.username} - {e}")
                return False

            # 4. 智能等待页面和互动元素加载完成
            try:
                self.logger.debug(f"⏳ 等待页面加载完成: {account.username}")

                # 等待页面基本加载完成
                await asyncio.wait_for(
                    driver_wrapper.execute_async(
                        lambda: WebDriverWait(driver_wrapper.driver, 3).until(
                            lambda d: d.execute_script('return document.readyState') == 'complete'
                        )
                    ),
                    timeout=5
                )

                # 额外等待互动按钮加载（重要！）
                self.logger.debug(f"⏳ 等待互动按钮加载: {account.username}")
                await asyncio.sleep(2)  # 给互动按钮额外的加载时间

                # 尝试等待至少一个互动按钮出现
                interaction_selectors = [
                    '[data-testid="like"]',
                    '[data-testid="retweet"]',
                    '[data-testid="reply"]',
                    'div[role="button"][aria-label*="Like"]',
                    'div[role="button"][aria-label*="Retweet"]',
                    'div[role="button"][aria-label*="Reply"]'
                ]

                for selector in interaction_selectors:
                    try:
                        element = await driver_wrapper.find_element(By.CSS_SELECTOR, selector)
                        if element:
                            self.logger.debug(f"✅ 找到互动按钮: {account.username} - {selector}")
                            break
                    except:
                        continue

                self.logger.debug(f"✅ 页面和互动元素加载完成: {account.username}")

            except Exception as e:
                # 页面未完全加载也继续执行
                self.logger.warning(f"⚠️ 页面加载未完成，继续执行: {account.username} - {e}")

            # 5. 最终状态验证（可选）
            try:
                current_url = await driver_wrapper.execute_async(
                    lambda: driver_wrapper.driver.current_url
                )

                # 如果在登录页面，说明前面的登录检查有问题
                if any(login_path in current_url.lower() for login_path in ['/login', '/signin', '/i/flow/login']):
                    self.logger.error(f"❌ 仍在登录页面，互动无法执行: {account.username}")
                    return False

                self.logger.debug(f"✅ 最终状态验证通过: {account.username}")

            except Exception as e:
                self.logger.warning(f"⚠️ 最终状态验证失败，继续执行: {account.username} - {e}")

            # 5. 执行具体互动
            self.logger.info(f"🎯 开始执行 {action_type} 操作: {account.username}")

            if action_type == 'like':
                result = await self._execute_like_fast(driver_wrapper, account.username)
            elif action_type == 'retweet':
                result = await self._execute_retweet_fast(driver_wrapper, account.username)
            elif action_type == 'comment':
                result = await self._execute_comment_fast(driver_wrapper, comment_content, account.username)
            else:
                self.logger.error(f"❌ 未知的互动类型: {action_type}")
                return False

            if result:
                self.logger.info(f"✅ 互动成功: {account.username} - {action_type}")
                return True
            else:
                # 🔄 智能重试机制：失败时进行cookie重新登录后再试一次
                if retry_count == 0:  # 第一次失败，尝试重试
                    self.logger.warning(f"⚠️ 互动失败，尝试cookie重新登录后重试: {account.username} - {action_type}")

                    try:
                        # 1. 获取WebDriver
                        driver_wrapper = await self.browser_pool.get_driver(account)
                        if driver_wrapper:
                            # 2. 强制cookie重新登录
                            self.logger.info(f"🔄 开始cookie重新登录: {account.username}")
                            login_success = await self._force_cookie_relogin(driver_wrapper, account)

                            if login_success:
                                self.logger.info(f"✅ Cookie重新登录成功，开始重试: {account.username}")
                                # 3. 递归调用自己，重试一次
                                return await self._execute_single_interaction_fast(
                                    account, post_url, action_type, comment_content, retry_count + 1
                                )
                            else:
                                self.logger.warning(f"⚠️ Cookie重新登录失败，跳过重试: {account.username}")

                    except Exception as retry_e:
                        self.logger.warning(f"⚠️ 重试过程异常: {account.username} - {retry_e}")

                # 第二次失败或重试失败，最终放弃
                self.logger.error(f"❌ 互动最终失败: {account.username} - {action_type}")
                return False

        except Exception as e:
            # 🔄 异常情况下的智能重试
            error_msg = str(e)
            is_retryable_error = any(keyword in error_msg.lower() for keyword in [
                'click intercepted', 'element not clickable', 'stale element',
                'no such element', 'timeout', 'connection'
            ])

            if retry_count == 0 and is_retryable_error:
                self.logger.warning(f"⚠️ 遇到可重试异常，尝试cookie重新登录后重试: {account.username} - {error_msg}")

                try:
                    # 强制cookie重新登录后重试
                    driver_wrapper = await self.browser_pool.get_driver(account)
                    if driver_wrapper:
                        login_success = await self._force_cookie_relogin(driver_wrapper, account)
                        if login_success:
                            return await self._execute_single_interaction_fast(
                                account, post_url, action_type, comment_content, retry_count + 1
                            )
                except Exception as retry_e:
                    self.logger.warning(f"⚠️ 异常重试失败: {account.username} - {retry_e}")

            self.logger.error(f"❌ 互动执行异常: {account.username} - {action_type} - {e}")
            import traceback
            self.logger.debug(f"详细错误信息: {traceback.format_exc()}")
            return False

    def _sanitize_comment_content(self, content: str) -> str:
        """
        清理评论内容，移除可能导致ChromeDriver问题的字符

        Args:
            content: 原始评论内容

        Returns:
            清理后的安全内容
        """
        if not content:
            return "很棒的分享！"

        try:
            # 1. 移除超出BMP范围的Unicode字符（emoji等）
            # BMP范围是 U+0000 到 U+FFFF
            safe_content = ""
            for char in content:
                if ord(char) <= 0xFFFF:
                    safe_content += char
                else:
                    # 用简单的表情符号替换复杂emoji
                    if char in "👍👏💯🔥❤️😊🙌✨💪👌🎯💡🚀⭐🌟":
                        safe_content += ":)"
                    # 跳过其他超出BMP的字符

            # 2. 如果清理后内容为空或过短，使用默认内容
            safe_content = safe_content.strip()
            if len(safe_content) < 2:
                safe_content = "很棒的分享！"

            # 3. 限制长度
            if len(safe_content) > 100:
                safe_content = safe_content[:97] + "..."

            return safe_content

        except Exception as e:
            self.logger.warning(f"清理评论内容失败: {e}")
            return "很棒的分享！"

    def _simulate_natural_typing(self, content: str, enable_random_chars: bool = True) -> str:
        """
        模拟自然输入，通过随机插入字符来防止检测

        Args:
            content: 原始内容
            enable_random_chars: 是否启用随机字符插入

        Returns:
            处理后的内容（用于模拟输入过程）
        """
        if not enable_random_chars or not content:
            return content

        try:
            # 定义可插入的随机字符池（增强版）
            random_chars = {
                'chinese': ['的', '了', '是', '在', '有', '我', '他', '这', '个', '们', '你', '她', '它', '和', '与', '或', '但', '而', '所', '以'],
                'english': [
                    # 常用字母组合和单词片段
                    'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm',
                    'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z',
                    'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M',
                    'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
                    # 常用英文单词片段
                    'th', 'he', 'in', 'er', 'an', 're', 'ed', 'nd', 'on', 'en', 'at', 'ou', 'it', 'is', 'or', 'ti', 'hi', 'st', 'io', 'le', 'as', 'ar', 'ng', 'al', 'te'
                ],
                'numbers': ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '20', '21', '99', '100'],
                'symbols': ['.', ',', '!', '?', ':', ';', '-', '_', '&', '+', '=', '#', '@', '*', '~', '|'],
                'safe_emoji': [':)', ':(', ':D', ':P', '^_^', '~_~', '@_@', 'XD', ':-)', ':-(', ':-D', ':-P', 'o_o', 'O_O', '>_<']
            }

            # 计算插入概率和数量（确保足够的字符插入）
            content_length = len(content)

            # 🎯 确保至少插入10个字符
            if content_length < 10:
                # 短内容：插入更多字符来达到要求
                min_inserts = max(8, 15 - content_length)  # 确保总长度至少15
                max_inserts = min_inserts + 5
            elif content_length < 20:
                # 中等内容：插入10-15个字符
                min_inserts = 10
                max_inserts = 15
            else:
                # 长内容：插入8-12个字符
                min_inserts = 8
                max_inserts = 12

            insert_count = random.randint(min_inserts, max_inserts)

            # 创建插入计划（确保插入足够数量的字符）
            insert_positions = []

            # 🎯 为了确保插入足够数量，我们允许在同一位置插入多个字符
            # 生成插入位置（允许重复位置）
            available_positions = list(range(content_length + 1))  # 0到content_length的所有位置

            for _ in range(insert_count):
                # 随机选择插入位置（允许重复）
                pos = random.choice(available_positions)
                insert_positions.append(pos)

            # 按位置分组，从后往前处理
            position_groups = {}
            for pos in insert_positions:
                if pos not in position_groups:
                    position_groups[pos] = 0
                position_groups[pos] += 1

            # 按位置从大到小排序
            sorted_positions = sorted(position_groups.keys(), reverse=True)

            # 执行插入（从后往前插入，避免位置偏移）
            result_content = content
            inserted_chars = []

            for pos in sorted_positions:
                # 在同一位置插入多个字符
                chars_to_insert = position_groups[pos]
                insert_string = ""

                for _ in range(chars_to_insert):
                    # 🎯 加权选择字符类型（增加英文和数字的比例）
                    char_type_weights = [
                        ('english', 40),     # 40% 英文字符
                        ('numbers', 25),     # 25% 数字
                        ('chinese', 15),     # 15% 中文字符
                        ('symbols', 12),     # 12% 符号
                        ('safe_emoji', 8)    # 8% 安全emoji
                    ]

                    # 根据权重选择字符类型
                    total_weight = sum(weight for _, weight in char_type_weights)
                    rand_num = random.randint(1, total_weight)
                    cumulative_weight = 0

                    for char_type, weight in char_type_weights:
                        cumulative_weight += weight
                        if rand_num <= cumulative_weight:
                            break

                    char_pool = random_chars[char_type]
                    random_char = random.choice(char_pool)
                    insert_string += random_char
                    inserted_chars.append(f"{random_char}({char_type})@{pos}")

                # 在指定位置插入所有字符
                result_content = result_content[:pos] + insert_string + result_content[pos:]

            # 记录插入信息（用于调试）
            if inserted_chars:
                self.logger.debug(f"随机字符插入: {', '.join(inserted_chars)}")

            return result_content

        except Exception as e:
            self.logger.warning(f"自然输入模拟失败: {e}")
            return content

    async def _natural_typing_input(self, driver_wrapper, input_element, content: str,
                                   enable_anti_detection: bool = True) -> bool:
        """
        自然输入模拟，包括随机字符插入、错误修正、输入节奏等

        Args:
            driver_wrapper: WebDriver包装器
            input_element: 输入框元素
            content: 要输入的内容
            enable_anti_detection: 是否启用反检测功能

        Returns:
            是否输入成功
        """
        try:
            # 清空输入框
            await driver_wrapper.execute_async(input_element.clear)
            await asyncio.sleep(random.uniform(0.2, 0.5))

            if not enable_anti_detection:
                # 快速输入模式（评论场景推荐）
                self.logger.debug(f"使用快速输入模式: {content}")
                await driver_wrapper.execute_async(input_element.send_keys, content)
                return True

            # 🎭 简化的自然输入模式（针对评论优化）
            self.logger.debug(f"开始简化自然输入: {content}")

            # 1. 简单的分段输入（减少复杂度）
            if len(content) > 10:
                # 长内容分2-3段输入
                mid_point = len(content) // 2
                segments = [content[:mid_point], content[mid_point:]]

                for i, segment in enumerate(segments):
                    await driver_wrapper.execute_async(input_element.send_keys, segment)
                    if i < len(segments) - 1:
                        # 简短的段间延迟
                        await asyncio.sleep(random.uniform(0.1, 0.3))
            else:
                # 短内容直接输入
                await driver_wrapper.execute_async(input_element.send_keys, content)

            # 2. 简短的完成延迟
            await asyncio.sleep(random.uniform(0.1, 0.3))

            return True

        except Exception as e:
            self.logger.error(f"自然输入失败: {e}")
            # 降级到简单输入
            try:
                await driver_wrapper.execute_async(input_element.clear)
                await driver_wrapper.execute_async(input_element.send_keys, content)
                return True
            except:
                return False

    async def _smart_login_check(self, driver_wrapper, account: Account) -> bool:
        """
        智能登录状态检查

        Args:
            driver_wrapper: WebDriver包装器
            account: 账号对象

        Returns:
            是否需要登录 (True=需要登录, False=已登录)
        """
        try:
            self.logger.debug(f"开始智能登录检查: {account.username}")

            # 1. 快速URL检查
            try:
                current_url = await driver_wrapper.execute_async(
                    lambda: driver_wrapper.driver.current_url
                )

                # 如果在登录页面，明确需要登录
                if any(login_path in current_url.lower() for login_path in ['/login', '/signin', '/i/flow/login']):
                    self.logger.info(f"检测到登录页面，需要登录: {account.username}")
                    return True

                # 如果在主页或帖子页面，可能已登录
                if any(logged_path in current_url.lower() for logged_path in ['/home', '/status/', 'x.com']):
                    self.logger.debug(f"在正常页面，进行元素检查: {account.username}")
                    # 继续进行元素检查
                else:
                    self.logger.warning(f"在未知页面，需要验证: {current_url}")
                    return True  # 保守策略：未知页面需要登录

            except Exception as e:
                self.logger.warning(f"URL检查失败: {e}")
                return True  # 检查失败，保守策略

            # 2. 快速元素检查（检查是否有登录后才有的元素）
            try:
                # 检查登录后的关键元素
                logged_in_indicators = [
                    '[data-testid="primaryColumn"]',      # 主要内容区域
                    '[data-testid="SideNav_AccountSwitcher_Button"]',  # 账号切换按钮
                    '[data-testid="tweetButtonInline"]',  # 发推按钮
                    '[data-testid="AppTabBar_Home_Link"]' # 主页链接
                ]

                found_indicators = 0
                for selector in logged_in_indicators:
                    try:
                        element = await driver_wrapper.execute_async(
                            lambda s=selector: driver_wrapper.driver.find_element(By.CSS_SELECTOR, s)
                        )
                        if element and element.is_displayed():
                            found_indicators += 1
                            self.logger.debug(f"找到登录指标: {selector}")
                            break  # 找到一个就够了，快速检查
                    except Exception:
                        continue

                # 如果找到登录指标，认为已登录
                if found_indicators > 0:
                    self.logger.debug(f"找到{found_indicators}个登录指标，判定为已登录: {account.username}")
                    return False  # 不需要登录

            except Exception as e:
                self.logger.warning(f"元素检查失败: {e}")

            # 3. 检查是否有明显的登录表单
            try:
                login_form_selectors = [
                    'input[name="text"]',           # 用户名输入框
                    'input[name="password"]',       # 密码输入框
                    '[data-testid="LoginForm_Login_Button"]',  # 登录按钮
                    'form[action*="login"]'         # 登录表单
                ]

                for selector in login_form_selectors:
                    try:
                        element = await driver_wrapper.execute_async(
                            lambda s=selector: driver_wrapper.driver.find_element(By.CSS_SELECTOR, s)
                        )
                        if element and element.is_displayed():
                            self.logger.info(f"检测到登录表单，需要登录: {account.username}")
                            return True  # 需要登录
                    except Exception:
                        continue

            except Exception as e:
                self.logger.warning(f"登录表单检查失败: {e}")

            # 4. 默认策略：如果无法确定，检查账号的数据库状态
            try:
                from src.core.account_manager import AccountManager
                account_manager = AccountManager()
                db_account = account_manager.get_account_by_id(account.id)

                if db_account and db_account.status:
                    db_status = db_account.status
                    if db_status == 'logged_in':
                        self.logger.debug(f"数据库状态显示已登录，跳过登录: {account.username}")
                        return False  # 不需要登录
                    else:
                        self.logger.info(f"数据库状态显示未登录({db_status})，需要登录: {account.username}")
                        return True   # 需要登录
                else:
                    self.logger.warning(f"无法从数据库获取账号状态: {account.username}")
                    return True  # 无法确定状态时，选择登录

            except Exception as e:
                self.logger.warning(f"数据库状态检查失败: {e}")

            # 5. 最终保守策略：如果所有检查都失败，不强制登录（避免重复登录）
            self.logger.warning(f"无法确定登录状态，假设已登录: {account.username}")
            return False  # 不需要登录

        except Exception as e:
            self.logger.error(f"智能登录检查异常: {account.username} - {e}")
            return False  # 异常时不强制登录

    def stop_interaction_session(self, account_ids: List[int] = None):
        """
        停止互动会话，取消账号持久化标记

        Args:
            account_ids: 要取消持久化的账号ID列表，如果为None则取消所有
        """
        try:
            if account_ids is None:
                # 取消所有持久化账号
                persistent_accounts = list(self.browser_pool.persistent_accounts)
                for account_id in persistent_accounts:
                    self.browser_pool.unmark_account_persistent(account_id)
                self.logger.info(f"🔄 已取消所有 {len(persistent_accounts)} 个账号的持久化标记")
            else:
                # 取消指定账号的持久化
                for account_id in account_ids:
                    self.browser_pool.unmark_account_persistent(account_id)
                self.logger.info(f"🔄 已取消 {len(account_ids)} 个账号的持久化标记")

        except Exception as e:
            self.logger.warning(f"⚠️ 取消账号持久化标记失败: {e}")

    async def _force_cookie_relogin(self, driver_wrapper, account: Account) -> bool:
        """
        强制进行cookie重新登录

        Args:
            driver_wrapper: WebDriver包装器
            account: 账号信息

        Returns:
            bool: 重新登录是否成功
        """
        try:
            self.logger.info(f"🔄 开始强制cookie重新登录: {account.username}")

            # 1. 清除当前页面状态
            try:
                await driver_wrapper.execute_async(lambda: driver_wrapper.driver.delete_all_cookies())
                self.logger.debug(f"✅ 已清除所有cookies: {account.username}")
            except Exception as e:
                self.logger.debug(f"⚠️ 清除cookies失败: {e}")

            # 2. 导航到登录页面
            try:
                await driver_wrapper.get("https://x.com/login")
                await asyncio.sleep(2)  # 等待页面加载
                self.logger.debug(f"✅ 已导航到登录页面: {account.username}")
            except Exception as e:
                self.logger.warning(f"⚠️ 导航到登录页面失败: {e}")
                return False

            # 3. 使用cookie登录
            try:
                from src.core.cookie_session_manager import CookieSessionManager
                cookie_manager = CookieSessionManager()

                login_success = await cookie_manager.try_cookie_login(driver_wrapper, account)

                if login_success:
                    self.logger.info(f"✅ Cookie重新登录成功: {account.username}")

                    # 4. 等待登录稳定
                    await asyncio.sleep(2)

                    # 5. 验证登录状态
                    try:
                        current_url = driver_wrapper.driver.current_url
                        if "login" not in current_url.lower():
                            self.logger.info(f"✅ 登录状态验证成功: {account.username}")
                            return True
                        else:
                            self.logger.warning(f"⚠️ 仍在登录页面: {account.username}")
                            return False
                    except Exception as verify_e:
                        self.logger.warning(f"⚠️ 登录状态验证失败: {verify_e}")
                        return True  # 假设成功，继续尝试
                else:
                    self.logger.warning(f"⚠️ Cookie重新登录失败: {account.username}")
                    return False

            except Exception as e:
                self.logger.warning(f"⚠️ Cookie登录过程异常: {account.username} - {e}")
                return False

        except Exception as e:
            self.logger.error(f"❌ 强制cookie重新登录异常: {account.username} - {e}")
            return False

    async def _stabilize_login_session(self, driver_wrapper, account: Account):
        """
        登录后的会话稳定化等待

        Args:
            driver_wrapper: WebDriver包装器
            account: 账号对象
        """
        try:
            self.logger.info(f"🔄 开始会话稳定化: {account.username}")

            # 1. 基础等待时间（让登录状态完全生效）
            stabilization_level = getattr(self, 'stabilization_level', 'normal')

            if stabilization_level == 'light':
                base_wait = random.uniform(1, 2)
            elif stabilization_level == 'heavy':
                base_wait = random.uniform(5, 8)
            else:  # normal
                base_wait = random.uniform(3, 5)

            self.logger.debug(f"⏳ 基础稳定化等待 {base_wait:.1f}秒 (级别: {stabilization_level}): {account.username}")
            await asyncio.sleep(base_wait)

            # 2. 验证登录状态是否稳定
            for attempt in range(3):
                try:
                    self.logger.debug(f"🔍 验证登录状态稳定性 (尝试 {attempt + 1}/3): {account.username}")

                    # 访问主页验证状态
                    await driver_wrapper.get("https://x.com/home")
                    await asyncio.sleep(2)

                    # 检查关键登录元素
                    login_indicators = [
                        '[data-testid="SideNav_AccountSwitcher_Button"]',  # 账号切换按钮
                        '[data-testid="SideNav_NewTweet_Button"]',         # 发推按钮
                        '[data-testid="primaryColumn"]'                    # 主要内容区域
                    ]

                    stable_indicators = 0
                    for selector in login_indicators:
                        try:
                            element = await driver_wrapper.find_element(By.CSS_SELECTOR, selector)
                            if element and element.is_displayed():
                                stable_indicators += 1
                                break  # 找到一个就够了
                        except Exception:
                            continue

                    if stable_indicators > 0:
                        self.logger.debug(f"✅ 登录状态稳定: {account.username}")
                        break
                    else:
                        self.logger.warning(f"⚠️ 登录状态不稳定，继续等待: {account.username}")
                        await asyncio.sleep(2)

                except Exception as e:
                    self.logger.warning(f"⚠️ 状态验证异常: {account.username} - {e}")
                    if attempt < 2:
                        await asyncio.sleep(2)
                        continue

            # 3. 额外的会话预热（模拟正常用户行为）
            try:
                self.logger.debug(f"🔥 进行会话预热: {account.username}")

                # 模拟轻量级用户行为
                await driver_wrapper.execute_async(
                    lambda: driver_wrapper.driver.execute_script('window.scrollTo(0, 100);')
                )
                await asyncio.sleep(1)

                await driver_wrapper.execute_async(
                    lambda: driver_wrapper.driver.execute_script('window.scrollTo(0, 0);')
                )
                await asyncio.sleep(1)

                self.logger.debug(f"✅ 会话预热完成: {account.username}")

            except Exception as e:
                self.logger.debug(f"⚠️ 会话预热失败，继续执行: {account.username} - {e}")

            # 4. 最终稳定化等待
            final_wait = random.uniform(2, 4)  # 2-4秒随机等待
            self.logger.debug(f"⏳ 最终稳定化等待 {final_wait:.1f}秒: {account.username}")
            await asyncio.sleep(final_wait)

            self.logger.info(f"✅ 会话稳定化完成: {account.username}")

        except Exception as e:
            self.logger.warning(f"⚠️ 会话稳定化异常: {account.username} - {e}")
            # 即使稳定化失败，也给一个基础等待时间
            await asyncio.sleep(3)

    async def _login_with_existing_driver(self, driver_wrapper, account: Account) -> bool:
        """
        使用现有WebDriver进行登录（避免重复创建WebDriver）

        Args:
            driver_wrapper: 现有的WebDriver包装器
            account: 账号对象

        Returns:
            是否登录成功
        """
        try:
            self.logger.info(f"🔐 使用现有WebDriver进行登录: {account.username}")

            # 使用多层登录管理器，传入现有的WebDriver
            from src.core.multi_layer_login_manager import MultiLayerLoginManager
            multi_login_manager = MultiLayerLoginManager()

            result = await multi_login_manager.login_account(driver_wrapper, account)

            if result['success']:
                self.logger.info(f"✅ 登录成功 (方法: {result['method']}): {account.username}")

                # 保存登录后的Cookie
                try:
                    from src.core.cookie_session_manager import CookieSessionManager
                    cookie_manager = CookieSessionManager()
                    await cookie_manager.save_login_cookies(driver_wrapper, account)
                    self.logger.debug(f"✅ Cookie保存成功: {account.username}")
                except Exception as e:
                    self.logger.warning(f"⚠️ Cookie保存失败: {account.username} - {e}")

                return True
            else:
                self.logger.error(f"❌ 登录失败: {account.username} - {result['message']}")
                return False

        except Exception as e:
            self.logger.error(f"❌ 使用现有WebDriver登录异常: {account.username} - {e}")
            return False

    async def _light_stabilization(self, driver_wrapper, account: Account):
        """
        轻量级稳定化（用于已登录账号）

        Args:
            driver_wrapper: WebDriver包装器
            account: 账号对象
        """
        try:
            self.logger.debug(f"🔄 轻量级稳定化: {account.username}")

            # 轻量级等待和验证
            await asyncio.sleep(random.uniform(1, 2))

            # 简单的页面交互验证
            try:
                current_url = await driver_wrapper.execute_async(
                    lambda: driver_wrapper.driver.current_url
                )

                if 'x.com' in current_url:
                    self.logger.debug(f"✅ 轻量级稳定化完成: {account.username}")
                else:
                    self.logger.warning(f"⚠️ 页面状态异常: {current_url}")
                    await asyncio.sleep(2)  # 额外等待

            except Exception as e:
                self.logger.debug(f"⚠️ 轻量级验证失败: {account.username} - {e}")
                await asyncio.sleep(2)

        except Exception as e:
            self.logger.warning(f"⚠️ 轻量级稳定化异常: {account.username} - {e}")
            await asyncio.sleep(1)

    async def _stabilize_page_after_navigation(self, driver_wrapper, account: Account, post_url: str):
        """
        页面导航后的稳定化等待（优化版：减少等待时间）

        Args:
            driver_wrapper: WebDriver包装器
            account: 账号对象
            post_url: 目标页面URL
        """
        try:
            self.logger.debug(f"🔄 页面导航后稳定化: {account.username}")

            # 1. 基础页面加载等待（减少等待时间）
            self.logger.debug(f"⏳ 等待页面基础加载: {account.username}")
            await asyncio.sleep(random.uniform(1, 2))  # 从2-3秒减少到1-2秒

            # 2. 等待页面完全加载（减少重试次数）
            for attempt in range(2):  # 从3次减少到2次
                try:
                    page_state = await driver_wrapper.execute_async(
                        lambda: driver_wrapper.driver.execute_script('return document.readyState')
                    )

                    if page_state == 'complete':
                        self.logger.debug(f"✅ 页面加载完成: {account.username}")
                        break
                    else:
                        self.logger.debug(f"⏳ 页面仍在加载: {page_state}")
                        await asyncio.sleep(0.5)  # 从1秒减少到0.5秒

                except Exception as e:
                    self.logger.debug(f"⚠️ 页面状态检查失败: {e}")
                    if attempt < 1:
                        await asyncio.sleep(0.5)  # 从1秒减少到0.5秒
                        continue

            # 3. 等待关键互动元素加载（优化版：减少等待时间）
            self.logger.debug(f"⏳ 等待互动元素加载: {account.username}")

            # 关键互动元素选择器（优先级排序）
            interaction_selectors = [
                '[data-testid="reply"]',  # 评论按钮优先
                '[data-testid="like"]',
                '[data-testid="retweet"]',
                'div[role="button"][aria-label*="Reply"]',
                'div[role="button"][aria-label*="Like"]',
                'div[role="button"][aria-label*="Retweet"]'
            ]

            # 尝试等待至少一个互动元素出现（减少等待次数）
            element_found = False
            for wait_attempt in range(3):  # 从5次减少到3次
                for selector in interaction_selectors:
                    try:
                        element = await driver_wrapper.find_element(By.CSS_SELECTOR, selector)
                        if element and element.is_displayed():
                            self.logger.debug(f"✅ 找到互动元素: {selector}")
                            element_found = True
                            break
                    except Exception:
                        continue

                if element_found:
                    break

                self.logger.debug(f"⏳ 互动元素未就绪，继续等待 (尝试 {wait_attempt + 1}/3)")
                await asyncio.sleep(0.5)  # 从1秒减少到0.5秒

            if not element_found:
                self.logger.warning(f"⚠️ 未找到互动元素，但继续执行: {account.username}")

            # 4. 额外的稳定化等待（减少等待时间）
            stabilization_wait = random.uniform(1, 2)  # 从2-4秒减少到1-2秒
            self.logger.debug(f"⏳ 最终稳定化等待 {stabilization_wait:.1f}秒: {account.username}")
            await asyncio.sleep(stabilization_wait)

            # 5. 验证页面状态
            try:
                current_url = await driver_wrapper.execute_async(
                    lambda: driver_wrapper.driver.current_url
                )

                if post_url in current_url or any(keyword in current_url for keyword in ['/status/', 'x.com']):
                    self.logger.debug(f"✅ 页面状态验证通过: {account.username}")
                else:
                    self.logger.warning(f"⚠️ 页面可能跳转: {current_url}")

            except Exception as e:
                self.logger.warning(f"⚠️ 页面状态验证失败: {account.username} - {e}")

            self.logger.info(f"✅ 页面稳定化完成: {account.username}")

        except Exception as e:
            self.logger.warning(f"⚠️ 页面稳定化异常: {account.username} - {e}")
            # 即使稳定化失败，也给一个基础等待时间
            await asyncio.sleep(3)

    async def _simulate_typing_errors(self, driver_wrapper, input_element):
        """
        模拟输入错误和修正过程

        Args:
            driver_wrapper: WebDriver包装器
            input_element: 输入框元素
        """
        try:
            # 模拟常见的输入错误
            error_types = [
                'extra_char',    # 多输入字符
                'wrong_char',    # 输入错误字符
                'missing_char'   # 漏输入字符
            ]

            error_type = random.choice(error_types)

            if error_type == 'extra_char':
                # 输入多余字符然后删除
                extra_char = random.choice('abcdefghijklmnopqrstuvwxyz')
                await driver_wrapper.execute_async(input_element.send_keys, extra_char)
                await asyncio.sleep(random.uniform(0.2, 0.6))
                # 模拟发现错误，删除
                await driver_wrapper.execute_async(input_element.send_keys, '\b')  # 退格

            elif error_type == 'wrong_char':
                # 输入错误字符然后修正
                wrong_char = random.choice('qwertyuiop')
                await driver_wrapper.execute_async(input_element.send_keys, wrong_char)
                await asyncio.sleep(random.uniform(0.3, 0.8))
                # 删除错误字符
                await driver_wrapper.execute_async(input_element.send_keys, '\b')
                await asyncio.sleep(random.uniform(0.1, 0.3))

            # 模拟修正后的短暂停顿
            await asyncio.sleep(random.uniform(0.2, 0.5))

        except Exception as e:
            self.logger.debug(f"输入错误模拟失败: {e}")
            # 忽略错误，继续正常流程

    async def _execute_like_fast(self, driver_wrapper, username: str) -> bool:
        """快速点赞执行"""
        try:
            self.logger.info(f"🎯 开始查找点赞按钮: {username}")

            # 🎯 更新的点赞按钮选择器（2024年最新）
            like_selectors = [
                # 主要选择器
                '[data-testid="like"]',
                '[data-testid="unlike"]',  # 如果已经点赞过

                # 备用选择器
                'div[role="button"][aria-label*="Like"]',
                'div[role="button"][aria-label*="like"]',
                'button[aria-label*="Like"]',
                'button[aria-label*="like"]',

                # 通用选择器
                'div[role="button"]:has(svg[data-testid="heart"])',
                'button:has(svg[data-testid="heart"])',

                # 更宽泛的选择器
                'div[role="button"]:contains("Like")',
                'button:contains("Like")',

                # 中文界面
                '[aria-label*="点赞"]',
                '[aria-label*="喜欢"]'
            ]

            for i, selector in enumerate(like_selectors):
                try:
                    self.logger.info(f"🔍 尝试选择器 {i+1}: {selector}")
                    like_button = await driver_wrapper.find_element(By.CSS_SELECTOR, selector)
                    if like_button and like_button.is_enabled():
                        self.logger.info(f"✅ 找到点赞按钮: {username}")
                        await driver_wrapper.execute_async(like_button.click)
                        await asyncio.sleep(random.uniform(0.5, 1.0))  # 短暂等待
                        self.logger.info(f"✅ 点赞操作完成: {username}")
                        return True
                    else:
                        self.logger.info(f"⚠️ 按钮不可用: {username}")
                except Exception as e:
                    self.logger.info(f"⚠️ 选择器 {i+1} 失败: {e}")
                    continue

            # 🔧 调试：如果所有选择器都失败，截图保存
            try:
                screenshot_path = f"debug_like_failed_{username}_{int(time.time())}.png"
                driver_wrapper.driver.save_screenshot(screenshot_path)
                self.logger.error(f"❌ 未找到可用的点赞按钮，已保存截图: {screenshot_path}")

                # 输出页面源码的关键部分（用于调试）
                page_source = driver_wrapper.driver.page_source
                if 'like' in page_source.lower() or 'heart' in page_source.lower():
                    self.logger.info(f"🔍 页面包含点赞相关元素，但选择器可能不准确")
                else:
                    self.logger.info(f"🔍 页面似乎不包含点赞元素，可能页面结构异常")

            except Exception as e:
                self.logger.info(f"截图失败: {e}")

            return False

        except Exception as e:
            self.logger.error(f"❌ 快速点赞失败: {username} - {e}")
            return False

    async def _execute_retweet_fast(self, driver_wrapper, username: str) -> bool:
        """快速转发执行"""
        try:
            self.logger.info(f"🎯 开始查找转发按钮: {username}")

            # 🎯 更新的转发按钮选择器（2024年最新）
            retweet_selectors = [
                # 主要选择器
                '[data-testid="retweet"]',
                '[data-testid="unretweet"]',  # 如果已经转发过

                # 备用选择器
                'div[role="button"][aria-label*="Retweet"]',
                'div[role="button"][aria-label*="retweet"]',
                'button[aria-label*="Retweet"]',
                'button[aria-label*="retweet"]',

                # 通用选择器
                'div[role="button"]:has(svg[data-testid="retweet"])',
                'button:has(svg[data-testid="retweet"])',

                # 更宽泛的选择器
                'div[role="button"]:contains("Retweet")',
                'button:contains("Retweet")',

                # 中文界面
                '[aria-label*="转发"]',
                '[aria-label*="转推"]'
            ]

            for i, selector in enumerate(retweet_selectors):
                try:
                    self.logger.info(f"🔍 尝试转发选择器 {i+1}: {selector}")
                    retweet_button = await driver_wrapper.find_element(By.CSS_SELECTOR, selector)
                    if retweet_button and retweet_button.is_enabled():
                        self.logger.info(f"✅ 找到转发按钮: {username}")
                        await driver_wrapper.execute_async(retweet_button.click)
                        await asyncio.sleep(random.uniform(0.5, 1.0))

                        # 🎯 快速确认转发
                        self.logger.info(f"🔍 查找转发确认按钮: {username}")
                        # 等待弹窗完全加载
                        await asyncio.sleep(0.5)

                        confirm_selectors = [
                            # 主要的Repost按钮选择器（基于截图中的新结构）
                            '[data-testid="retweetConfirm"]',
                            'div[role="menuitem"]:has-text("Repost")',
                            'div[role="menuitem"] span:contains("Repost")',
                            'div[role="menuitem"]:contains("Repost")',

                            # 基于aria-label的选择器
                            'div[role="menuitem"][aria-label*="Repost"]',
                            'div[role="menuitem"][aria-label*="repost"]',

                            # 传统的Retweet选择器（备用）
                            '[role="menuitem"][data-testid="retweetConfirm"]',
                            'div[role="menuitem"]:contains("Retweet")',
                            'span:contains("Retweet")',

                            # 中文选择器
                            'div[role="menuitem"]:contains("转发")',
                            'span:contains("转发")',

                            # 更宽泛的选择器 - 第一个菜单项通常是转发
                            'div[role="menuitem"]:first-child',
                            'div[role="menuitem"]'
                        ]

                        confirm_button = None
                        for j, confirm_selector in enumerate(confirm_selectors):
                            try:
                                self.logger.info(f"🔍 尝试确认选择器 {j+1}: {confirm_selector}")
                                confirm_button = await driver_wrapper.find_element(By.CSS_SELECTOR, confirm_selector)
                                if confirm_button:
                                    self.logger.info(f"✅ 找到确认按钮: {username} - {confirm_selector}")
                                    break
                                else:
                                    self.logger.info(f"⚠️ 确认按钮不可用: {username}")
                            except Exception as e:
                                self.logger.info(f"⚠️ 确认选择器 {j+1} 失败: {e}")
                                continue

                        if confirm_button:
                            await driver_wrapper.execute_async(confirm_button.click)
                            await asyncio.sleep(random.uniform(0.5, 1.0))
                            self.logger.info(f"✅ 转发操作完成: {username}")
                            return True
                        else:
                            self.logger.warning(f"⚠️ 未找到确认按钮，但转发可能成功: {username}")
                            return True  # 即使没找到确认按钮也认为成功

                        self.logger.warning(f"⚠️ 未找到确认按钮，但转发可能成功: {username}")
                        return True  # 即使没找到确认按钮也认为成功
                    else:
                        self.logger.info(f"⚠️ 转发按钮不可用: {username}")
                except Exception as e:
                    self.logger.info(f"⚠️ 转发选择器 {i+1} 失败: {e}")
                    continue

            self.logger.error(f"❌ 未找到可用的转发按钮: {username}")
            return False

        except Exception as e:
            self.logger.error(f"❌ 快速转发失败: {username} - {e}")
            return False

    async def _execute_comment_fast(self, driver_wrapper, comment_content: str, username: str) -> bool:
        """快速评论执行"""
        try:
            if not comment_content:
                # 🎯 使用专门的去重评论生成方法（25-30个字符）
                comment_content = self._generate_unique_comment_with_random_chars()

            self.logger.info(f"🎯 开始查找评论按钮: {username}")

            # 🎯 更新的评论按钮选择器（2024年最新）
            comment_selectors = [
                # 主要选择器
                '[data-testid="reply"]',

                # 备用选择器
                'div[role="button"][aria-label*="Reply"]',
                'div[role="button"][aria-label*="reply"]',
                'button[aria-label*="Reply"]',
                'button[aria-label*="reply"]',

                # 通用选择器
                'div[role="button"]:has(svg[data-testid="reply"])',
                'button:has(svg[data-testid="reply"])',

                # 更宽泛的选择器
                'div[role="button"]:contains("Reply")',
                'button:contains("Reply")',

                # 中文界面
                '[aria-label*="回复"]',
                '[aria-label*="评论"]'
            ]

            # 🔄 评论按钮点击重试机制
            comment_button_clicked = False
            for i, selector in enumerate(comment_selectors):
                try:
                    self.logger.info(f"🔍 尝试评论选择器 {i+1}: {selector}")
                    comment_button = await driver_wrapper.find_element(By.CSS_SELECTOR, selector)
                    if comment_button and comment_button.is_enabled():
                        self.logger.info(f"✅ 找到评论按钮: {username}")

                        # 🔄 点击重试机制
                        click_success = False
                        for click_attempt in range(2):
                            try:
                                await driver_wrapper.execute_async(comment_button.click)
                                await asyncio.sleep(random.uniform(0.5, 1.0))
                                click_success = True
                                break
                            except Exception as click_e:
                                self.logger.warning(f"⚠️ 点击尝试 {click_attempt + 1} 失败: {click_e}")
                                if click_attempt < 1:
                                    await asyncio.sleep(0.5)

                        if click_success:
                            comment_button_clicked = True
                            self.logger.info(f"✅ 评论按钮点击成功: {username}")
                            break
                        else:
                            self.logger.warning(f"⚠️ 评论按钮点击失败: {username}")
                            continue
                    else:
                        self.logger.debug(f"⚠️ 评论按钮不可用: {username}")
                except Exception as e:
                    self.logger.debug(f"⚠️ 评论选择器 {i+1} 失败: {e}")
                    continue

            # 🔍 如果评论按钮点击成功，查找输入框
            if comment_button_clicked:
                self.logger.info(f"🔍 查找评论输入框: {username}")
                await asyncio.sleep(1)  # 等待输入框加载

                input_selectors = [
                    # 🎯 最高优先级：弹窗内的输入框（2024年最新结构）
                    'div[role="dialog"] div[role="textbox"][contenteditable="true"]',
                    'div[aria-modal="true"] div[role="textbox"][contenteditable="true"]',
                    'div[role="dialog"] [data-testid="tweetTextarea_0"]',

                    # 🎯 高优先级：主要输入框选择器
                    '[data-testid="tweetTextarea_0"]',
                    '[data-testid="tweetTextarea_1"]',

                    # 🎯 中优先级：页面内评论输入框
                    'article div[role="textbox"][contenteditable="true"]',
                    'div[data-testid="cellInnerDiv"] div[role="textbox"][contenteditable="true"]',

                    # 🎯 通用选择器（排除顶部compose区域）
                    'div[role="textbox"][contenteditable="true"]:not([data-testid*="compose"])',
                    'div[contenteditable="true"][role="textbox"]:not([data-testid*="compose"])',

                    # 🎯 弹窗内的备用选择器
                    'div[role="dialog"] div[contenteditable="true"]',
                    'div[aria-modal="true"] div[contenteditable="true"]',
                    'div[data-testid="modal"] div[role="textbox"][contenteditable="true"]',

                    # 🎯 占位符选择器
                    'div[aria-label*="Tweet your reply" i] div[role="textbox"][contenteditable="true"]',
                    'div[aria-label*="Post your reply" i] div[role="textbox"][contenteditable="true"]',
                    'textarea[placeholder*="Tweet your reply"]:not([data-testid*="compose"])',
                    'textarea[placeholder*="Post your reply"]:not([data-testid*="compose"])',

                    # 🎯 最后备用选择器
                    '.public-DraftEditor-content:not([data-testid*="compose"])',
                    'div[aria-multiline="true"][contenteditable="true"]:not([data-testid*="compose"])'
                ]

                input_element = None
                for j, input_selector in enumerate(input_selectors):
                    try:
                        self.logger.info(f"🔍 尝试输入框选择器 {j+1}: {input_selector}")
                        elements = await driver_wrapper.find_elements(By.CSS_SELECTOR, input_selector)

                        if elements:
                            # 🎯 关键修复：智能选择逻辑 - 优先弹窗内输入框
                            for element in elements:
                                try:
                                    # 检查元素是否可见
                                    if element.is_displayed():
                                        # 🔍 检查是否在弹窗内（最高优先级）
                                        is_in_dialog = False
                                        try:
                                            # 检查父元素是否包含弹窗标识
                                            parent_html = element.find_element(By.XPATH, "./ancestor::div[@role='dialog']")
                                            if parent_html:
                                                is_in_dialog = True
                                                self.logger.info(f"✅ 找到弹窗内输入框: {username} - {input_selector}")
                                        except:
                                            try:
                                                parent_html = element.find_element(By.XPATH, "./ancestor::div[@aria-modal='true']")
                                                if parent_html:
                                                    is_in_dialog = True
                                                    self.logger.info(f"✅ 找到模态框内输入框: {username} - {input_selector}")
                                            except:
                                                pass

                                        # 如果在弹窗内，立即选择
                                        if is_in_dialog:
                                            input_element = element
                                            self.logger.info(f"🎯 优先选择弹窗内输入框: {username}")
                                            break

                                        # 如果不在弹窗内，检查位置（避免页面顶部）
                                        location = element.location
                                        y_position = location.get('y', 0)

                                        # 如果Y坐标大于300（页面下方），认为是正确的评论框
                                        if y_position > 300:
                                            input_element = element
                                            self.logger.info(f"✅ 找到页面下方输入框: {username} - {input_selector} (Y: {y_position})")
                                            break
                                        else:
                                            self.logger.debug(f"⚠️ 跳过页面顶部的输入框 (Y: {y_position})")
                                except Exception as pos_e:
                                    self.logger.debug(f"位置检查失败: {pos_e}")
                                    continue

                            # 如果找到了合适的输入框，跳出外层循环
                            if input_element:
                                break

                            # 如果没有找到合适位置的，使用第一个可见的（最后的备用方案）
                            for element in elements:
                                if element.is_displayed():
                                    input_element = element
                                    self.logger.warning(f"⚠️ 使用备用输入框: {username} - {input_selector}")
                                    break

                            if input_element:
                                break

                    except Exception as e:
                        self.logger.info(f"⚠️ 输入框选择器 {j+1} 失败: {e}")
                        continue

                if input_element:
                            try:
                                # 🔧 处理评论内容，移除可能导致ChromeDriver问题的字符
                                safe_comment_content = self._sanitize_comment_content(comment_content)

                                # 🎭 使用自然输入模拟（防检测）
                                self.logger.info(f"⌨️ 开始自然输入评论内容: {username}")
                                self.logger.debug(f"原始内容: {comment_content}")
                                self.logger.debug(f"安全内容: {safe_comment_content}")

                                # 检查是否启用反检测功能
                                enable_anti_detection = getattr(self, 'enable_natural_typing', True)

                                # 🔄 输入重试机制
                                input_success = False
                                for input_attempt in range(2):  # 最多重试2次
                                    try:
                                        input_success = await self._natural_typing_input(
                                            driver_wrapper, input_element, safe_comment_content,
                                            enable_anti_detection=enable_anti_detection
                                        )
                                        if input_success:
                                            break
                                        else:
                                            self.logger.warning(f"⚠️ 输入尝试 {input_attempt + 1} 失败，重试...")
                                            await asyncio.sleep(0.5)
                                    except Exception as input_e:
                                        self.logger.warning(f"⚠️ 输入异常 {input_attempt + 1}: {input_e}")
                                        if input_attempt < 1:
                                            await asyncio.sleep(0.5)

                                if not input_success:
                                    self.logger.error(f"❌ 评论内容输入失败: {username}")
                                    return False

                                self.logger.info(f"✅ 评论内容输入完成: {username} - {safe_comment_content}")

                                # 🎯 模拟真实用户行为：输入完成后延迟1-2秒再发送
                                review_delay = random.uniform(1.0, 2.0)
                                self.logger.info(f"⏱️ 模拟用户检查评论内容，延迟 {review_delay:.1f}秒...")
                                await asyncio.sleep(review_delay)

                                # 🔧 调试模式：保存输入完成后的截图
                                debug_mode = getattr(self, 'debug_mode', False)
                                if debug_mode:
                                    try:
                                        debug_screenshot_path = f"debug_comment_input_complete_{username}_{int(time.time())}.png"
                                        driver_wrapper.driver.save_screenshot(debug_screenshot_path)
                                        self.logger.info(f"🔧 调试截图已保存: {debug_screenshot_path}")
                                    except Exception as debug_e:
                                        self.logger.warning(f"⚠️ 调试截图保存失败: {debug_e}")

                                # 🎯 关键修复：根据输入框位置智能查找对应的发送按钮
                                self.logger.info(f"🔍 查找发送按钮: {username}")

                                # 🧠 智能检查输入框位置（优化版）
                                is_input_in_dialog = False
                                dialog_context = None

                                try:
                                    # 优先检查dialog
                                    dialog_context = input_element.find_element(By.XPATH, "./ancestor::div[@role='dialog']")
                                    if dialog_context:
                                        is_input_in_dialog = True
                                        self.logger.info(f"🎯 输入框在弹窗内: {username}")
                                except:
                                    try:
                                        # 检查aria-modal
                                        dialog_context = input_element.find_element(By.XPATH, "./ancestor::div[@aria-modal='true']")
                                        if dialog_context:
                                            is_input_in_dialog = True
                                            self.logger.info(f"🎯 输入框在模态框内: {username}")
                                    except:
                                        self.logger.info(f"🎯 输入框在页面内: {username}")

                                # 🎯 智能发送按钮选择器（优化版：减少选择器数量）
                                if is_input_in_dialog and dialog_context:
                                    # 弹窗内优先查找（在dialog_context内查找）
                                    send_selectors = [
                                        '[data-testid="tweetButton"]',
                                        '[data-testid="tweetButtonInline"]',
                                        'div[role="button"][aria-label*="Post"]',
                                        'button[aria-label*="Post"]',
                                        '[data-testid*="tweet"]'
                                    ]
                                    search_context = dialog_context
                                    self.logger.info(f"🎯 在弹窗内查找发送按钮: {username}")
                                else:
                                    # 页面内查找
                                    send_selectors = [
                                        '[data-testid="tweetButtonInline"]',
                                        '[data-testid="tweetButton"]',
                                        'div[role="button"][aria-label*="Reply"]',
                                        'div[role="button"][aria-label*="Post"]',
                                        'button[aria-label*="Reply"]'
                                    ]
                                    search_context = driver_wrapper
                                    self.logger.info(f"🎯 在页面内查找发送按钮: {username}")

                                # 🔍 智能查找发送按钮
                                send_button = None
                                for k, send_selector in enumerate(send_selectors):
                                    try:
                                        self.logger.info(f"🔍 尝试发送选择器 {k+1}: {send_selector}")

                                        if is_input_in_dialog and dialog_context:
                                            # 在弹窗内查找
                                            send_button = dialog_context.find_element(By.CSS_SELECTOR, send_selector)
                                        else:
                                            # 在整个页面查找
                                            send_button = await driver_wrapper.find_element(By.CSS_SELECTOR, send_selector)

                                        if send_button and send_button.is_enabled():
                                            self.logger.info(f"✅ 找到发送按钮: {username} - {send_selector}")
                                            break
                                        else:
                                            self.logger.info(f"⚠️ 发送按钮不可用: {username}")
                                            send_button = None
                                    except Exception as e:
                                        self.logger.info(f"⚠️ 发送选择器 {k+1} 失败: {e}")
                                        send_button = None
                                        continue

                                if send_button:
                                    await driver_wrapper.execute_async(send_button.click)
                                    await asyncio.sleep(random.uniform(1.0, 2.0))
                                    self.logger.info(f"✅ 评论发送完成: {username}")
                                    return True
                                else:
                                    self.logger.warning(f"⚠️ 未找到发送按钮，但评论输入成功: {username}")
                                    return True  # 输入成功也算成功

                            except Exception as e:
                                self.logger.error(f"❌ 评论输入或发送失败: {username} - {e}")
                                return False

                # 🔧 调试：尝试查找所有可编辑元素
                try:
                            self.logger.info(f"🔍 查找所有可编辑元素进行调试...")
                            all_editable = await driver_wrapper.find_elements(By.CSS_SELECTOR, '[contenteditable="true"]')
                            self.logger.info(f"🔍 找到 {len(all_editable)} 个可编辑元素")

                            for i, elem in enumerate(all_editable):
                                try:
                                    tag = elem.tag_name
                                    role = elem.get_attribute('role') or 'none'
                                    testid = elem.get_attribute('data-testid') or 'none'
                                    aria_label = elem.get_attribute('aria-label') or 'none'
                                    placeholder = elem.get_attribute('placeholder') or 'none'
                                    self.logger.info(f"   元素{i+1}: {tag}, role={role}, testid={testid}, aria-label={aria_label}, placeholder={placeholder}")
                                except:
                                    pass

                            # 保存调试截图
                            try:
                                screenshot_path = f"debug_comment_input_{username}_{int(time.time())}.png"
                                driver_wrapper.driver.save_screenshot(screenshot_path)
                                self.logger.error(f"❌ 未找到可用的输入框，已保存截图: {screenshot_path}")
                            except Exception as screenshot_error:
                                self.logger.error(f"❌ 未找到可用的输入框，截图保存失败: {screenshot_error}")

                except Exception as e:
                    self.logger.info(f"调试信息获取失败: {e}")

                return False

            if not input_element:
                self.logger.warning(f"⚠️ 未找到可用的输入框: {username}")
                return False

            # 如果找到输入框，继续执行评论逻辑
            # ... 这里应该有评论输入和发送的逻辑
            self.logger.info(f"✅ 评论功能执行完成: {username}")
            return True

        except Exception as e:
            self.logger.error(f"❌ 快速评论失败: {username} - {e}")
            return False

    def _generate_unique_comment_with_random_chars(self) -> str:
        """
        生成去重评论内容，使用26个英文字母、数字和表情随机组合
        控制最终长度在25-30个字符
        """
        try:
            # 🎯 字符池：26个英文字母 + 数字 + 文字表情
            char_pools = {
                'english_lower': list('abcdefghijklmnopqrstuvwxyz'),
                'english_upper': list('ABCDEFGHIJKLMNOPQRSTUVWXYZ'),
                'numbers': list('0123456789'),
                'text_emoji': [':)', ':(', ':D', ':P', '^_^', '~_~', '@_@', 'XD', ':-)', ':-(', ':-D', ':-P', 'o_o', 'O_O', '>_<', '=_=', '-_-', '+_+', '*_*', '!_!']
            }

            # 🎯 基础评论核心（短小精悍）
            base_cores = [
                "好", "赞", "棒", "妙", "牛", "强", "酷", "帅", "美", "爱",
                "支持", "同意", "认同", "喜欢", "收藏", "转发", "关注", "点赞"
            ]

            # 🎯 生成策略：基础核心 + 随机字符填充到25-30字符
            base_core = random.choice(base_cores)
            target_length = random.randint(25, 30)
            current_length = len(base_core)

            # 需要填充的字符数
            chars_needed = target_length - current_length

            # 🎯 字符类型权重（按你的要求：英文字母 + 数字 + 表情）
            char_type_weights = [
                ('english_lower', 30),    # 30% 小写英文
                ('english_upper', 20),    # 20% 大写英文
                ('numbers', 25),          # 25% 数字
                ('text_emoji', 25)        # 25% 文字表情
            ]

            # 生成随机字符序列
            random_chars = []
            for _ in range(chars_needed):
                # 根据权重选择字符类型
                total_weight = sum(weight for _, weight in char_type_weights)
                rand_num = random.randint(1, total_weight)
                cumulative_weight = 0

                for char_type, weight in char_type_weights:
                    cumulative_weight += weight
                    if rand_num <= cumulative_weight:
                        selected_char = random.choice(char_pools[char_type])
                        random_chars.append(selected_char)
                        break

            # 🎯 组合策略：随机插入到基础核心中
            result = base_core

            # 将随机字符分散插入到基础核心的各个位置
            for char in random_chars:
                # 随机选择插入位置
                insert_pos = random.randint(0, len(result))
                result = result[:insert_pos] + char + result[insert_pos:]

            # 🎯 确保长度在目标范围内
            if len(result) > 30:
                result = result[:30]
            elif len(result) < 25:
                # 如果还不够长，继续添加字符
                while len(result) < 25:
                    char_type = random.choice(['english_lower', 'numbers'])
                    char = random.choice(char_pools[char_type])
                    result += char

            self.logger.debug(f"生成去重评论: '{result}' (长度: {len(result)})")
            return result

        except Exception as e:
            self.logger.warning(f"生成去重评论失败: {e}")
            # 备用方案：简单的随机字符组合
            backup_chars = 'abcdefghijklmnopqrstuvwxyz0123456789'
            backup_result = ''.join(random.choices(backup_chars, k=25))
            return f"好{backup_result}"

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志显示组件
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTextEdit,
    QComboBox, QLabel, QGroupBox, QCheckBox
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QObject, QMetaObject, Q_ARG
from PyQt6.QtGui import QTextCursor, QColor, QFont
import sys
import re
from datetime import datetime

from src.utils.logger import LoggerMixin, get_task_log_signals


class QtLogHandler:
    """Qt日志处理器，用于将loguru日志发送到LogWidget"""

    def __init__(self, log_widget):
        self.log_widget = log_widget

    def write(self, message):
        """处理日志消息"""
        if message.strip():
            # 解析loguru的日志格式
            self.parse_and_add_log(message.strip())

    def parse_and_add_log(self, message):
        """解析并添加日志消息"""
        try:
            # 🔧 修复：检查log_widget是否仍然有效
            if not hasattr(self, 'log_widget') or self.log_widget is None:
                return  # 静默忽略，避免错误循环

            # 检查log_widget是否已被删除（C++对象检查）
            try:
                # 尝试访问一个简单的属性来检查对象是否有效
                _ = self.log_widget.objectName()
            except RuntimeError:
                # C++对象已被删除，清理引用并返回
                self.log_widget = None
                return

            # 使用正则表达式解析loguru的日志格式
            # 格式: 2025-08-03 03:03:22 | INFO     | src.ui.main_window:__init__:48 | 主窗口初始化完成
            pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) \| (\w+)\s+\| ([^|]+) \| (.+)'
            match = re.match(pattern, message)

            if match:
                timestamp, level, location, msg = match.groups()
                # 提取模块名
                module_parts = location.split(':')
                if len(module_parts) > 0:
                    module = module_parts[0].split('.')[-1]  # 取最后一部分作为模块名
                else:
                    module = "系统"

                # 🔧 修复：安全地发送信号
                try:
                    self.log_widget.log_message_signal.emit(level.strip(), module, msg)
                except RuntimeError:
                    # 信号发送失败，对象可能已被删除
                    self.log_widget = None
                    return
            else:
                # 如果解析失败，直接添加原始消息
                try:
                    self.log_widget.log_message_signal.emit("INFO", "系统", message)
                except RuntimeError:
                    self.log_widget = None
                    return

        except Exception as e:
            # 🔧 修复：避免错误循环，只在log_widget有效时才尝试记录错误
            try:
                if hasattr(self, 'log_widget') and self.log_widget is not None:
                    try:
                        _ = self.log_widget.objectName()  # 检查对象是否有效
                        self.log_widget.log_message_signal.emit("ERROR", "日志系统", f"日志解析失败: {e}")
                    except RuntimeError:
                        self.log_widget = None
            except:
                # 完全静默处理，避免无限循环
                pass


class LogWidget(QWidget, LoggerMixin):
    """日志显示组件"""

    # 定义信号用于线程安全的日志添加
    log_message_signal = pyqtSignal(str, str, str)  # level, module, message

    def __init__(self):
        super().__init__()
        self.max_lines = 1000  # 最大显示行数
        self.log_handler = None  # 日志处理器
        self.current_mode = "system"  # 当前日志模式

        # 连接信号
        self.log_message_signal.connect(self.add_log_message)

        self.init_ui()
        self.setup_log_monitoring()
        self.setup_real_log_handler()
        self.setup_task_log_style()
        self.setup_task_log_signals()
    
    def init_ui(self):
        """初始化用户界面"""
        # 🔄 改为水平布局，控制面板在左，日志显示在右
        layout = QHBoxLayout(self)

        # 创建控制面板（左侧）
        control_panel = self.create_control_panel()
        control_panel.setFixedWidth(300)  # 固定控制面板宽度
        layout.addWidget(control_panel)

        # 创建日志显示区域（右侧，占满剩余空间）
        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        # 🎯 移除高度限制，让日志显示区域占满整个高度
        #self.log_display.setMaximumHeight(200)  # 注释掉这行

        # 设置字体
        font = QFont("Consolas", 9)
        font.setFixedPitch(True)
        self.log_display.setFont(font)

        # 添加日志显示区域，设置拉伸因子让它占满剩余空间
        layout.addWidget(self.log_display, 1)  # 拉伸因子为1，占满剩余空间
    
    def create_control_panel(self) -> QGroupBox:
        """创建控制面板"""
        group_box = QGroupBox("日志控制")
        # 🔄 改为垂直布局，适合左侧面板
        layout = QVBoxLayout(group_box)

        # 日志模式选择
        mode_layout = QHBoxLayout()
        mode_layout.addWidget(QLabel("模式:"))
        self.mode_combo = QComboBox()
        self.mode_combo.addItem("系统日志", "system")
        self.mode_combo.addItem("任务执行日志", "task")
        self.mode_combo.currentTextChanged.connect(self.switch_log_mode)
        mode_layout.addWidget(self.mode_combo)
        layout.addLayout(mode_layout)

        # 日志级别过滤区域
        level_layout = QHBoxLayout()
        level_layout.addWidget(QLabel("级别:"))
        self.level_combo = QComboBox()
        self.level_combo.addItem("全部", "")
        self.level_combo.addItem("DEBUG", "DEBUG")
        self.level_combo.addItem("INFO", "INFO")
        self.level_combo.addItem("WARNING", "WARNING")
        self.level_combo.addItem("ERROR", "ERROR")
        self.level_combo.currentTextChanged.connect(self.filter_logs)
        level_layout.addWidget(self.level_combo)
        layout.addLayout(level_layout)

        # 自动滚动选项
        self.auto_scroll_cb = QCheckBox("自动滚动")
        self.auto_scroll_cb.setChecked(True)
        layout.addWidget(self.auto_scroll_cb)



        # 添加弹性空间，让控件靠上对齐
        layout.addStretch()

        return group_box
    
    def setup_log_monitoring(self):
        """设置日志监控"""
        # 日志监控现在通过实时日志处理器实现
        # 不再需要定时器和模拟日志
        pass

    def setup_task_log_style(self):
        """设置任务日志样式"""
        # 当切换到任务模式时，会应用深色主题
        pass

    def switch_log_mode(self):
        """切换日志模式"""
        new_mode = self.mode_combo.currentData()
        if new_mode != self.current_mode:
            self.current_mode = new_mode
            self.log_display.clear()  # 清空显示区域

            if new_mode == "task":
                # 切换到任务执行日志模式
                self.setup_task_mode()
                self.add_task_welcome_message()
            else:
                # 切换到系统日志模式
                self.setup_system_mode()

    def setup_task_mode(self):
        """设置任务日志模式"""
        # 应用深色主题
        self.log_display.setStyleSheet("""
            QTextEdit {
                background-color: #1e1e1e;
                color: #ffffff;
                border: 1px solid #3c3c3c;
                border-radius: 4px;
                padding: 8px;
            }
        """)

        # 更新级别选择器
        self.level_combo.clear()
        self.level_combo.addItem("全部", "all")
        self.level_combo.addItem("任务开始/结束", "task")
        self.level_combo.addItem("成功操作", "success")
        self.level_combo.addItem("失败操作", "error")
        self.level_combo.addItem("详细步骤", "detail")

    def setup_system_mode(self):
        """设置系统日志模式"""
        # 恢复默认主题
        self.log_display.setStyleSheet("")

        # 恢复级别选择器
        self.level_combo.clear()
        self.level_combo.addItem("全部", "")
        self.level_combo.addItem("DEBUG", "DEBUG")
        self.level_combo.addItem("INFO", "INFO")
        self.level_combo.addItem("WARNING", "WARNING")
        self.level_combo.addItem("ERROR", "ERROR")

    def add_task_welcome_message(self):
        """添加任务日志欢迎信息"""
        welcome_text = """
═══════════════════════════════════════════════════════════════
🚀 任务执行实时日志模式已启动
═══════════════════════════════════════════════════════════════
📋 此模式将显示发帖和互动任务的详细执行过程
🔍 包括每个账号的每个操作步骤和结果
⏱️  实时更新，支持级别筛选功能
═══════════════════════════════════════════════════════════════

等待任务开始...

"""
        self.log_display.setPlainText(welcome_text)

    # 任务日志记录方法
    def log_task_start(self, task_type: str, task_config: dict):
        """记录任务开始"""
        if self.current_mode != "task":
            return

        if task_type == "posting":
            message = f"🚀 开始发帖任务 - 账号数量: {task_config.get('account_count', 0)}"
        elif task_type == "interaction":
            message = f"🔄 开始互动任务 - 目标账号: {task_config.get('target_account', 'N/A')}"
        else:
            message = f"📋 开始{task_type}任务"

        self.add_task_log_line(message, "task", "#00ff00")

    def log_task_end(self, task_type: str, result: dict):
        """记录任务结束"""
        if self.current_mode != "task":
            return

        success_count = result.get('success_count', 0)
        failed_count = result.get('failed_count', 0)

        if task_type == "posting":
            message = f"✅ 发帖任务完成 - 成功: {success_count}, 失败: {failed_count}"
        elif task_type == "interaction":
            message = f"✅ 互动任务完成 - 成功: {success_count}, 失败: {failed_count}"
        else:
            message = f"✅ {task_type}任务完成"

        color = "#00ff00" if failed_count == 0 else "#ffaa00"
        self.add_task_log_line(message, "task", color)
        self.add_task_log_line("─" * 80, "separator", "#666666")

    def log_account_start(self, account_name: str, action: str):
        """记录账号开始操作"""
        if self.current_mode != "task":
            return

        if action == "posting":
            message = f"👤 账号 {account_name} 开始准备发帖"
        elif action == "interaction":
            message = f"👤 账号 {account_name} 开始执行互动"
        else:
            message = f"👤 账号 {account_name} 开始{action}"

        self.add_task_log_line(message, "detail", "#00aaff")

    def log_step_success(self, account_name: str, step: str, details: str = ""):
        """记录步骤成功"""
        if self.current_mode != "task":
            return

        step_icons = {
            "login": "🔐",
            "content_input": "📝",
            "media_upload": "📁",
            "photo_upload": "🖼️",
            "video_upload": "🎥",
            "post_publish": "📤",
            "like": "👍",
            "comment": "💬",
            "retweet": "🔁",
            "follow": "➕",
            "unfollow": "➖"
        }

        icon = step_icons.get(step, "✅")
        step_text = self.get_step_text(step)

        if details:
            message = f"  {icon} 账号 {account_name} {step_text}成功: {details}"
        else:
            message = f"  {icon} 账号 {account_name} {step_text}成功"

        self.add_task_log_line(message, "success", "#00ff00")

    def log_step_failure(self, account_name: str, step: str, error: str):
        """记录步骤失败"""
        if self.current_mode != "task":
            return

        step_text = self.get_step_text(step)
        message = f"  ❌ 账号 {account_name} {step_text}失败: {error}"

        self.add_task_log_line(message, "error", "#ff4444")

    def log_interaction_action(self, actor_account: str, target_account: str, action: str, success: bool, details: str = ""):
        """记录互动操作"""
        if self.current_mode != "task":
            return

        action_icons = {
            "like": "👍",
            "comment": "💬",
            "retweet": "🔁",
            "follow": "➕"
        }

        action_texts = {
            "like": "点赞",
            "comment": "评论",
            "retweet": "转发",
            "follow": "关注"
        }

        icon = action_icons.get(action, "🔄")
        action_text = action_texts.get(action, action)

        if success:
            message = f"  {icon} 账号 {actor_account} 对 {target_account} 进行{action_text} - 成功"
            if details:
                message += f": {details}"
            color = "#00ff00"
            log_type = "success"
        else:
            message = f"  ❌ 账号 {actor_account} 对 {target_account} 进行{action_text} - 失败: {details}"
            color = "#ff4444"
            log_type = "error"

        self.add_task_log_line(message, log_type, color)

    def get_step_text(self, step: str) -> str:
        """获取步骤文本"""
        step_texts = {
            "login": "登录",
            "content_input": "文案输入",
            "media_upload": "媒体上传",
            "photo_upload": "照片上传",
            "video_upload": "视频上传",
            "post_publish": "帖子发布",
            "like": "点赞",
            "comment": "评论",
            "retweet": "转发",
            "follow": "关注",
            "unfollow": "取消关注"
        }
        return step_texts.get(step, step)

    def add_task_log_line(self, message: str, log_type: str, color: str):
        """添加任务日志行"""
        # 检查过滤级别
        level_filter = self.level_combo.currentData()
        if level_filter != "all":
            if level_filter == "task" and log_type not in ["task", "separator"]:
                return
            elif level_filter == "success" and log_type != "success":
                return
            elif level_filter == "error" and log_type != "error":
                return
            elif level_filter == "detail" and log_type not in ["detail", "success", "error"]:
                return

        # 添加时间戳
        if log_type != "separator":
            timestamp = datetime.now().strftime("%H:%M:%S")
            full_message = f"[{timestamp}] {message}"
        else:
            full_message = message

        # 添加到显示区域
        cursor = self.log_display.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.End)

        # 设置文本颜色
        format = cursor.charFormat()
        format.setForeground(QColor(color))
        cursor.setCharFormat(format)

        cursor.insertText(full_message + "\n")

        # 限制行数
        self.limit_lines()

        # 自动滚动
        if self.auto_scroll_cb.isChecked():
            self.log_display.moveCursor(QTextCursor.MoveOperation.End)

    def setup_real_log_handler(self):
        """设置实时日志处理器"""
        try:
            from loguru import logger

            # 创建自定义日志处理器
            self.log_handler = QtLogHandler(self)

            # 添加到loguru
            logger.add(
                self.log_handler,
                format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
                level="DEBUG",
                colorize=False
            )

            self.add_log_message("INFO", "日志系统", "实时日志处理器已启动")

        except Exception as e:
            self.add_log_message("ERROR", "日志系统", f"设置实时日志处理器失败: {e}")
    
    def add_log_message(self, level: str, module: str, message: str):
        """添加日志消息"""
        # 只在系统日志模式下处理系统日志
        if self.current_mode != "system":
            return

        try:
            from datetime import datetime

            # 检查级别过滤
            filter_level = self.level_combo.currentData()
            if filter_level and level != filter_level:
                return

            # 格式化日志消息
            timestamp = datetime.now().strftime("%H:%M:%S")
            log_line = f"[{timestamp}] [{level}] {module}: {message}"

            # 设置颜色
            color = self.get_level_color(level)

            # 添加到显示区域
            cursor = self.log_display.textCursor()
            cursor.movePosition(QTextCursor.MoveOperation.End)

            # 设置文本颜色
            format = cursor.charFormat()
            format.setForeground(color)
            cursor.setCharFormat(format)

            cursor.insertText(log_line + "\n")

            # 限制行数
            self.limit_lines()

            # 自动滚动
            if self.auto_scroll_cb.isChecked():
                self.log_display.moveCursor(QTextCursor.MoveOperation.End)

        except Exception as e:
            print(f"添加日志消息失败: {e}")

    def setup_task_log_signals(self):
        """设置任务日志信号连接"""
        try:
            task_signals = get_task_log_signals()

            # 连接任务日志信号
            task_signals.task_started.connect(self.on_task_started)
            task_signals.task_ended.connect(self.on_task_ended)
            task_signals.account_started.connect(self.on_account_started)
            task_signals.step_success.connect(self.on_step_success)
            task_signals.step_failure.connect(self.on_step_failure)
            task_signals.interaction_action.connect(self.on_interaction_action)
            task_signals.custom_log.connect(self.on_custom_log)

            self.add_log_message("INFO", "日志系统", "任务日志信号连接成功")

        except Exception as e:
            self.add_log_message("ERROR", "日志系统", f"设置任务日志信号失败: {e}")

    def on_task_started(self, task_type: str, task_config: dict):
        """处理任务开始信号"""
        # 切换到任务日志模式
        self.mode_combo.setCurrentText("任务日志")
        self.current_mode = "task"

        # 记录任务开始
        self.log_task_start(task_type, task_config)

    def on_task_ended(self, task_type: str, result: dict):
        """处理任务结束信号"""
        if self.current_mode == "task":
            self.log_task_end(task_type, result)

    def on_account_started(self, account_name: str, action: str):
        """处理账号开始操作信号"""
        if self.current_mode == "task":
            if action == "posting":
                message = f"👤 账号 {account_name} 开始准备发帖"
            elif action == "interaction":
                message = f"👤 账号 {account_name} 开始执行互动"
            else:
                message = f"👤 账号 {account_name} 开始{action}"

            self.add_task_log_line(message, "detail", "#00aaff")

    def on_step_success(self, account_name: str, step: str, details: str):
        """处理步骤成功信号"""
        if self.current_mode == "task":
            step_icons = {
                "login": "🔐",
                "content_input": "📝",
                "media_upload": "📁",
                "photo_upload": "🖼️",
                "video_upload": "🎥",
                "post_publish": "📤",
                "like": "👍",
                "comment": "💬",
                "retweet": "🔁",
                "follow": "➕",
                "unfollow": "➖"
            }

            icon = step_icons.get(step, "✅")
            step_text = self.get_step_text(step)

            if details:
                message = f"  {icon} 账号 {account_name} {step_text}成功: {details}"
            else:
                message = f"  {icon} 账号 {account_name} {step_text}成功"

            self.add_task_log_line(message, "success", "#00ff00")

    def on_step_failure(self, account_name: str, step: str, error: str):
        """处理步骤失败信号"""
        if self.current_mode == "task":
            step_text = self.get_step_text(step)
            message = f"  ❌ 账号 {account_name} {step_text}失败: {error}"
            self.add_task_log_line(message, "error", "#ff4444")

    def on_interaction_action(self, actor_account: str, target_account: str, action: str, success: bool, details: str):
        """处理互动操作信号"""
        if self.current_mode == "task":
            action_icons = {
                "like": "👍",
                "comment": "💬",
                "retweet": "🔁",
                "follow": "➕"
            }

            action_texts = {
                "like": "点赞",
                "comment": "评论",
                "retweet": "转发",
                "follow": "关注"
            }

            icon = action_icons.get(action, "🔄")
            action_text = action_texts.get(action, action)

            if success:
                message = f"  {icon} 账号 {actor_account} 对 {target_account} 进行{action_text} - 成功"
                if details:
                    message += f": {details}"
                color = "#00ff00"
                log_type = "success"
            else:
                message = f"  ❌ 账号 {actor_account} 对 {target_account} 进行{action_text} - 失败: {details}"
                color = "#ff4444"
                log_type = "error"

            self.add_task_log_line(message, log_type, color)

    def on_custom_log(self, message: str, log_type: str, color: str):
        """处理自定义日志信号"""
        if self.current_mode == "task":
            self.add_task_log_line(message, log_type, color)

    def get_step_text(self, step: str) -> str:
        """获取步骤文本"""
        step_texts = {
            "login": "登录",
            "content_input": "输入内容",
            "media_upload": "上传媒体",
            "photo_upload": "上传图片",
            "video_upload": "上传视频",
            "post_publish": "发布帖子",
            "like": "点赞",
            "comment": "评论",
            "retweet": "转发",
            "follow": "关注",
            "unfollow": "取消关注"
        }
        return step_texts.get(step, step)

    def get_level_color(self, level: str) -> QColor:
        """获取日志级别对应的颜色"""
        color_map = {
            'DEBUG': QColor(128, 128, 128),    # 灰色
            'INFO': QColor(0, 0, 0),           # 黑色
            'WARNING': QColor(255, 165, 0),    # 橙色
            'ERROR': QColor(255, 0, 0),        # 红色
            'CRITICAL': QColor(139, 0, 0)      # 深红色
        }
        return color_map.get(level, QColor(0, 0, 0))
    
    def limit_lines(self):
        """限制显示行数"""
        try:
            document = self.log_display.document()
            if document.blockCount() > self.max_lines:
                cursor = QTextCursor(document)
                cursor.movePosition(QTextCursor.MoveOperation.Start)
                cursor.movePosition(QTextCursor.MoveOperation.Down, 
                                  QTextCursor.MoveMode.KeepAnchor, 
                                  document.blockCount() - self.max_lines)
                cursor.removeSelectedText()
        except Exception as e:
            print(f"限制行数失败: {e}")
    
    def filter_logs(self):
        """过滤日志"""
        # 过滤逻辑在各自的add方法中实现
        # 这里可以添加额外的过滤逻辑
        pass
    

    




[34696:1488:0801/052336.851:INFO:components\enterprise\browser\controller\chrome_browser_cloud_management_controller.cc:202] No machine level policy manager exists.
[4952:9936:0801/052337.613:WARNING:net\spdy\spdy_session.cc:3054] Received HEADERS for invalid stream 1
[34696:1488:0801/052337.705:WARNING:CONSOLE:76] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('chrome-untrusted://new-tab-page') does not match the recipient window's origin ('chrome://new-tab-page').", source: chrome://new-tab-page/shared.rollup.js (76)
[34696:1488:0801/052339.629:WARNING:chrome\browser\media\prefs\capture_device_ranking.h:255] Can't initialize the value of media.audio_input.user_preference_ranking because media.default_audio_capture_device isn't registered or is empty
[34696:1488:0801/052339.629:WARNING:chrome\browser\media\prefs\capture_device_ranking.h:255] Can't initialize the value of media.video_input.user_preference_ranking because media.default_video_capture_Device isn't registered or is empty
[6960:27248:0801/052339.714:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[34696:1488:0801/052340.343:INFO:CONSOLE:1] "Error", source: https://abs.twimg.com/responsive-web/client-web/main.9c086aba.js (1)
[34696:1488:0801/052340.483:INFO:CONSOLE:74] "[GSI_LOGGER]: Your client application uses one of the Google One Tap prompt UI status methods that may stop functioning when FedCM becomes mandatory. Refer to the migration guide to update your code accordingly and opt-in to FedCM to test your changes. Learn more: https://developers.google.com/identity/gsi/web/guides/fedcm-migration?s=dc#display_moment and https://developers.google.com/identity/gsi/web/guides/fedcm-migration?s=dc#skipped_moment", source: https://accounts.google.com/gsi/client (74)
[34696:1488:0801/052340.484:INFO:CONSOLE:75] "[GSI_LOGGER]: FedCM get() rejects with TypeError: Failed to execute 'get' on 'CredentialsContainer': Failed to read the 'identity' property from 'CredentialRequestOptions': Failed to read the 'mode' property from 'IdentityCredentialRequestOptions': The provided value 'widget' is not a valid enum value of type IdentityCredentialRequestOptionsMode.", source: https://accounts.google.com/gsi/client (75)
[34696:1488:0801/052340.485:INFO:CONSOLE:75] "[GSI_LOGGER]: FedCM get() rejects with TypeError: Failed to execute 'get' on 'CredentialsContainer': Failed to read the 'identity' property from 'CredentialRequestOptions': Failed to read the 'mode' property from 'IdentityCredentialRequestOptions': The provided value 'widget' is not a valid enum value of type IdentityCredentialRequestOptionsMode.", source: https://accounts.google.com/gsi/client (75)
[34696:1488:0801/052340.660:INFO:CONSOLE:0] "Banner not shown: beforeinstallpromptevent.preventDefault() called. The page must call beforeinstallpromptevent.prompt() to show the banner.", source: https://x.com/i/flow/login?redirect_after_login=%2Fhome (0)
[34696:27304:0801/052343.837:ERROR:google_apis\gcm\engine\registration_request.cc:291] Registration response error message: DEPRECATED_ENDPOINT
[34696:1488:0801/052344.370:WARNING:chrome\browser\media\prefs\capture_device_ranking.h:255] Can't initialize the value of media.audio_input.user_preference_ranking because media.default_audio_capture_device isn't registered or is empty
[34696:1488:0801/052344.370:WARNING:chrome\browser\media\prefs\capture_device_ranking.h:255] Can't initialize the value of media.video_input.user_preference_ranking because media.default_video_capture_Device isn't registered or is empty
[6960:27248:0801/052344.453:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[34696:1488:0801/052344.514:INFO:CONSOLE:1] "Error", source: https://abs.twimg.com/responsive-web/client-web/main.9c086aba.js (1)
[34696:1488:0801/052344.560:INFO:CONSOLE:74] "[GSI_LOGGER]: Your client application uses one of the Google One Tap prompt UI status methods that may stop functioning when FedCM becomes mandatory. Refer to the migration guide to update your code accordingly and opt-in to FedCM to test your changes. Learn more: https://developers.google.com/identity/gsi/web/guides/fedcm-migration?s=dc#display_moment and https://developers.google.com/identity/gsi/web/guides/fedcm-migration?s=dc#skipped_moment", source: https://accounts.google.com/gsi/client (74)
[34696:1488:0801/052344.561:INFO:CONSOLE:75] "[GSI_LOGGER]: FedCM get() rejects with TypeError: Failed to execute 'get' on 'CredentialsContainer': Failed to read the 'identity' property from 'CredentialRequestOptions': Failed to read the 'mode' property from 'IdentityCredentialRequestOptions': The provided value 'widget' is not a valid enum value of type IdentityCredentialRequestOptionsMode.", source: https://accounts.google.com/gsi/client (75)
[34696:1488:0801/052344.561:INFO:CONSOLE:75] "[GSI_LOGGER]: FedCM get() rejects with TypeError: Failed to execute 'get' on 'CredentialsContainer': Failed to read the 'identity' property from 'CredentialRequestOptions': Failed to read the 'mode' property from 'IdentityCredentialRequestOptions': The provided value 'widget' is not a valid enum value of type IdentityCredentialRequestOptionsMode.", source: https://accounts.google.com/gsi/client (75)
[34696:1488:0801/052344.706:INFO:CONSOLE:0] "Banner not shown: beforeinstallpromptevent.preventDefault() called. The page must call beforeinstallpromptevent.prompt() to show the banner.", source: https://x.com/ (0)
[34696:1488:0801/052345.316:INFO:CONSOLE:0] "[Report Only] Refused to frame 'https://accounts.google.com/' because an ancestor violates the following Content Security Policy directive: "frame-ancestors 'self'".
", source:  (0)
[34696:1488:0801/052348.788:WARNING:chrome\browser\media\prefs\capture_device_ranking.h:255] Can't initialize the value of media.audio_input.user_preference_ranking because media.default_audio_capture_device isn't registered or is empty
[34696:1488:0801/052348.788:WARNING:chrome\browser\media\prefs\capture_device_ranking.h:255] Can't initialize the value of media.video_input.user_preference_ranking because media.default_video_capture_Device isn't registered or is empty
[6960:27248:0801/052348.867:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[34696:1488:0801/052348.907:INFO:CONSOLE:1] "Error", source: https://abs.twimg.com/responsive-web/client-web/main.9c086aba.js (1)
[34696:1488:0801/052349.033:INFO:CONSOLE:74] "[GSI_LOGGER]: Your client application uses one of the Google One Tap prompt UI status methods that may stop functioning when FedCM becomes mandatory. Refer to the migration guide to update your code accordingly and opt-in to FedCM to test your changes. Learn more: https://developers.google.com/identity/gsi/web/guides/fedcm-migration?s=dc#display_moment and https://developers.google.com/identity/gsi/web/guides/fedcm-migration?s=dc#skipped_moment", source: https://accounts.google.com/gsi/client (74)
[34696:1488:0801/052349.034:INFO:CONSOLE:75] "[GSI_LOGGER]: FedCM get() rejects with TypeError: Failed to execute 'get' on 'CredentialsContainer': Failed to read the 'identity' property from 'CredentialRequestOptions': Failed to read the 'mode' property from 'IdentityCredentialRequestOptions': The provided value 'widget' is not a valid enum value of type IdentityCredentialRequestOptionsMode.", source: https://accounts.google.com/gsi/client (75)
[34696:1488:0801/052349.128:INFO:CONSOLE:0] "Banner not shown: beforeinstallpromptevent.preventDefault() called. The page must call beforeinstallpromptevent.prompt() to show the banner.", source: https://x.com/i/flow/login (0)
[34696:27304:0801/052404.101:ERROR:google_apis\gcm\engine\registration_request.cc:291] Registration response error message: DEPRECATED_ENDPOINT
[6960:26252:0801/052404.128:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[6960:26252:0801/052404.128:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[34696:1488:0801/052404.211:INFO:CONSOLE:0] "[DOM] Password field is not contained in a form: (More info: https://goo.gl/9p2vKq) %o", source: https://x.com/i/flow/login (0)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
发帖工作流模块
"""

import asyncio
from datetime import datetime
from typing import List, Dict, Any, Optional

from src.database.connection import get_db_session
from src.database.models import Account, AccountGroup, PostingTask, MediaFile
from src.modules.posting.content_manager import ContentManager
from src.modules.posting.executor import PostingExecutor
from src.core.account_manager import AccountManager
from src.utils.logger import LoggerMixin
from src.config.constants import TASK_STATUS, ACCOUNT_STATUS
from src.config.settings import get_settings


class PostingWorkflow(LoggerMixin):
    """发帖工作流"""
    
    def __init__(self):
        self.settings = get_settings()
        self.content_manager = ContentManager()
        self.posting_executor = PostingExecutor()
        self.account_manager = AccountManager()
        self._stop_requested = False
    
    def stop_workflow(self):
        """停止工作流"""
        self._stop_requested = True
        self.logger.info("工作流停止请求已发出")

        # 🔧 修复：停止工作流时立即清理浏览器资源
        try:
            from src.core.browser_manager import get_browser_pool
            browser_pool = get_browser_pool()
            # 🔧 修复：工作流停止时不使用最终关闭标志，保持可以重新启动
            browser_pool.sync_close_all(is_final_shutdown=False)
            self.logger.info("工作流停止时已清理浏览器资源")
        except Exception as e:
            self.logger.warning(f"工作流停止时清理浏览器资源失败: {e}")
    
    def reset_stop_flag(self):
        """重置停止标志"""
        self._stop_requested = False
    
    async def execute_batch_posting(self,
                                   posting_group_id: int,
                                   content_config: Dict[str, Any],
                                   execution_mode: str = 'sequential') -> Dict[str, Any]:
        """
        执行批量发帖

        Args:
            posting_group_id: 发帖分组ID
            content_config: 内容配置
            execution_mode: 执行模式 ('sequential', 'random')

        Returns:
            执行结果统计
        """
        try:
            # 重置停止标志，确保新的发帖任务可以正常开始
            self.reset_stop_flag()

            self.logger.info(f"开始批量发帖: 分组{posting_group_id}, 模式{execution_mode}")

            # 发送任务开始信号
            task_config = {
                'posting_group_id': posting_group_id,
                'execution_mode': execution_mode,
                'content_config': content_config
            }
            self.log_task_start("posting", task_config)

            # 检查是否需要停止
            if self._stop_requested:
                self.logger.info("检测到停止请求，取消批量发帖")
                return {
                    'success_count': 0,
                    'failed_count': 0,
                    'total_count': 0,
                    'errors': ['用户取消操作']
                }
            
            # 获取发帖账号
            accounts, total_count = self.account_manager.get_accounts(
                group_id=posting_group_id,
                status=ACCOUNT_STATUS['LOGGED_IN']
            )

            if not accounts:
                self.logger.warning("没有找到可用的发帖账号")
                return {
                    'success_count': 0,
                    'failed_count': 0,
                    'total_count': 0,
                    'errors': ['没有可用的发帖账号']
                }

            # 根据执行模式决定账号顺序
            if execution_mode == 'random':
                import random
                accounts = accounts.copy()  # 创建副本避免修改原列表
                random.shuffle(accounts)
                self.logger.info(f"🎲 随机打乱账号顺序，共 {len(accounts)} 个账号")
            else:
                self.logger.info(f"📝 按顺序执行，共 {len(accounts)} 个账号")

            # 创建发帖任务
            task_ids = []
            for account in accounts:
                # 检查是否需要停止
                if self._stop_requested:
                    self.logger.info("检测到停止请求，停止创建任务")
                    break
                    
                task_id = self._create_posting_task(account, content_config)
                if task_id:
                    task_ids.append(task_id)
            
            # 再次检查是否需要停止
            if self._stop_requested:
                self.logger.info("检测到停止请求，取消执行发帖")
                return {
                    'success_count': 0,
                    'failed_count': 0,
                    'total_count': len(task_ids),
                    'errors': ['用户取消操作']
                }
            
            # 执行发帖 - 账号顺序已在上面处理，这里都用顺序执行
            results = await self._execute_sequential_posting(task_ids)
            
            # 统计结果
            success_count = sum(1 for r in results if r['success'])
            failed_count = len(results) - success_count
            errors = [r['error'] for r in results if not r['success'] and r.get('error')]
            
            # 移动成功发帖的账号到已发帖分组
            posted_group_id = content_config.get('posted_group_id')
            if posted_group_id:
                successful_account_ids = [
                    r['account_id'] for r in results if r['success']
                ]
                if successful_account_ids:
                    self.logger.info(f"🔄 开始移动 {len(successful_account_ids)} 个成功账号到分组 {posted_group_id}")
                    moved_count = self.account_manager.move_accounts_to_group(
                        successful_account_ids,
                        posted_group_id,
                        reset_post_count=False  # 发帖成功后移动不重置发帖数量
                    )
                    self.logger.info(f"✅ 成功移动 {moved_count} 个账号到已发帖分组")
                else:
                    self.logger.info("📝 没有成功的账号需要移动")
            else:
                self.logger.info("📝 未配置已发帖分组，跳过账号移动")
            
            result_summary = {
                'success_count': success_count,
                'failed_count': failed_count,
                'total_count': len(task_ids),
                'errors': errors
            }
            
            self.logger.info(f"批量发帖完成: 成功{success_count}, 失败{failed_count}")

            # 发送任务结束信号
            self.log_task_end("posting", result_summary)

            return result_summary
            
        except Exception as e:
            self.logger.error(f"批量发帖失败: {e}")

            # 发送任务失败信号
            error_result = {
                'success_count': 0,
                'failed_count': 0,
                'total_count': 0,
                'errors': [str(e)]
            }
            self.log_task_end("posting", error_result)

            return error_result
    
    def _create_posting_task(self, account: Account, content_config: Dict[str, Any]) -> Optional[int]:
        """
        创建发帖任务 - 支持智能文案选择
        
        Args:
            account: 账号对象
            content_config: 内容配置
            
        Returns:
            发帖任务ID
        """
        session = get_db_session()
        try:
            # 准备内容 - 支持智能模板选择
            template = content_config.get('template')  # 可能为None，触发智能选择
            media_config = content_config.get('media_config', {})
            template_config = content_config.get('template_config', {})
            
            content = self.content_manager.prepare_content(
                template=template,
                media_config=media_config,
                template_config=template_config
            )
            
            # 验证内容
            is_valid, error_msg = self.content_manager.validate_content(content['text'])
            if not is_valid:
                self.logger.warning(f"账号 {account.id} 内容验证失败: {error_msg}")
                return None
            
            # 创建任务
            media_id_list = []
            if content['media']:
                # 确保media.id被正确转换为int类型
                for media in content['media']:
                    try:
                        media_id = int(media.id)
                        media_id_list.append(media_id)
                    except (ValueError, TypeError, AttributeError) as e:
                        self.logger.warning(f"无法获取媒体ID: {e}")
            
            task = PostingTask(
                account_id=account.id,
                content=content['text'],
                status=TASK_STATUS['PENDING']
            )

            # 单独设置media_list以避免InstrumentedAttribute错误
            task.media_list = media_id_list

            # 🎯 设置已发帖分组ID（如果配置中有的话）
            if 'posted_group_id' in content_config:
                task.posted_group_id = content_config['posted_group_id']
            
            session.add(task)
            session.commit()
            
            # 获取task.id并返回
            task_id = task.id
            
            self.logger.debug(f"为账号 {account.id} 创建发帖任务: {content['text'][:50]}...")
            return task_id
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"创建发帖任务失败: {e}")
            return None
        finally:
            session.close()
    
    async def _execute_sequential_posting(self, task_ids: List[int]) -> List[Dict[str, Any]]:
        """
        顺序执行发帖
        
        Args:
            task_ids: 任务ID列表
            
        Returns:
            执行结果列表
        """
        results = []
        
        for task_id in task_ids:
            # 检查是否需要停止
            if self._stop_requested:
                self.logger.info("检测到停止请求，停止顺序发帖")
                # 为剩余任务添加取消结果
                remaining_tasks = task_ids[len(results):]
                for remaining_task_id in remaining_tasks:
                    results.append({
                        'task_id': remaining_task_id,
                        'account_id': None,
                        'success': False,
                        'error': '用户取消操作'
                    })
                break
                
            try:
                result = await self._execute_single_posting_task_by_id(task_id)
                results.append(result)
                
                # 任务间延迟
                if result['success']:
                    delay = self.settings.anti_detection.get('random_delay_max', 3.0)
                    await asyncio.sleep(delay)
                    
            except Exception as e:
                self.logger.error(f"执行发帖任务失败: {e}")
                results.append({
                    'task_id': task_id,
                    'account_id': None,
                    'success': False,
                    'error': str(e)
                })
        
        return results
    

    
    async def _execute_single_posting_task_by_id(self, task_id: int) -> Dict[str, Any]:
        """
        通过任务ID执行单个发帖任务

        Args:
            task_id: 发帖任务ID

        Returns:
            执行结果
        """
        # 每次执行时重新获取数据库会话，避免会话失效问题
        session = get_db_session()
        try:
            # 查询task对象
            task = session.query(PostingTask).filter(
                PostingTask.id == task_id
            ).first()

            if not task:
                raise Exception(f"任务不存在: {task_id}")

            # 检查是否需要停止
            if self._stop_requested:
                self.logger.info(f"检测到停止请求，跳过任务 {task_id}")
                return {
                    'task_id': task_id,
                    'account_id': task.account_id,
                    'success': False,
                    'error': '用户取消操作'
                }

            # 更新任务状态
            task.status = TASK_STATUS['PROCESSING']
            session.commit()

            # 获取账号信息
            account = session.query(Account).filter(
                Account.id == task.account_id
            ).first()

            if not account:
                raise Exception(f"账号不存在: {task.account_id}")

            # 获取媒体文件
            media_files = []
            if task.media_list:
                media_files = session.query(MediaFile).filter(
                    MediaFile.id.in_(task.media_list)
                ).all()

            # 执行发帖
            post_result = await self.posting_executor.execute_post(
                account=account,
                content=task.content,
                media_files=media_files
            )
            
            # 更新任务状态
            if post_result['success']:
                task.status = TASK_STATUS['COMPLETED']
                task.posted_at = datetime.now()
                if 'post_url' in post_result:
                    task.post_url = post_result['post_url']

                # 🎯 发帖成功后，移动账号到指定分组
                if hasattr(task, 'posted_group_id') and task.posted_group_id:
                    try:
                        self.logger.info(f"🔄 单个任务：移动账号 {account.username} 到分组 {task.posted_group_id}")
                        moved_count = self.account_manager.move_accounts_to_group(
                            [task.account_id],
                            task.posted_group_id,
                            reset_post_count=False  # 发帖成功后移动不重置发帖数量
                        )
                        if moved_count > 0:
                            self.logger.info(f"✅ 账号 {account.username} 已移动到已发帖分组 {task.posted_group_id}")
                        else:
                            self.logger.warning(f"⚠️ 账号 {account.username} 分组转移失败")
                    except Exception as move_error:
                        self.logger.error(f"❌ 账号分组转移异常: {move_error}")
                else:
                    self.logger.debug(f"📝 任务未配置已发帖分组ID，跳过账号移动")

            else:
                task.status = TASK_STATUS['FAILED']
                task.error_message = post_result.get('error', '未知错误')

            session.commit()

            return {
                'task_id': task.id,
                'account_id': task.account_id,
                'success': post_result['success'],
                'error': post_result.get('error') if not post_result['success'] else None,
                'post_url': post_result.get('post_url'),
                'tweet_id': post_result.get('tweet_id'),
                'account_username': account.username
            }
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"执行发帖任务失败: {e}")
            
            # 尝试更新任务状态为失败 - 使用新的会话避免会话失效问题
            try:
                update_session = get_db_session()
                try:
                    task = update_session.query(PostingTask).filter(
                        PostingTask.id == task_id
                    ).first()
                    if task:
                        task.status = TASK_STATUS['FAILED']
                        task.error_message = str(e)
                        update_session.commit()
                finally:
                    update_session.close()
            except Exception as update_error:
                self.logger.error(f"更新任务状态失败: {update_error}")
            
            return {
                'task_id': task_id,
                'account_id': None,
                'success': False,
                'error': str(e)
            }
            
        finally:
            session.close()
    
    async def _execute_single_posting_task(self, task: PostingTask) -> Dict[str, Any]:
        """
        执行单个发帖任务（保留原方法以兼容性）
        
        Args:
            task: 发帖任务
            
        Returns:
            执行结果
        """
        return await self._execute_single_posting_task_by_id(task.id)
    
    def get_posting_statistics(self, group_id: int = None, 
                              start_date: datetime = None,
                              end_date: datetime = None) -> Dict[str, Any]:
        """
        获取发帖统计
        
        Args:
            group_id: 分组ID
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            统计信息
        """
        session = get_db_session()
        try:
            query = session.query(PostingTask)
            
            # 添加过滤条件
            if group_id:
                query = query.join(Account).filter(Account.group_id == group_id)
            
            if start_date:
                query = query.filter(PostingTask.created_at >= start_date)
            
            if end_date:
                query = query.filter(PostingTask.created_at <= end_date)
            
            # 统计各状态任务数量
            total_tasks = query.count()
            completed_tasks = query.filter(
                PostingTask.status == TASK_STATUS['COMPLETED']
            ).count()
            failed_tasks = query.filter(
                PostingTask.status == TASK_STATUS['FAILED']
            ).count()
            pending_tasks = query.filter(
                PostingTask.status == TASK_STATUS['PENDING']
            ).count()
            processing_tasks = query.filter(
                PostingTask.status == TASK_STATUS['PROCESSING']
            ).count()
            
            return {
                'total_tasks': total_tasks,
                'completed_tasks': completed_tasks,
                'failed_tasks': failed_tasks,
                'pending_tasks': pending_tasks,
                'processing_tasks': processing_tasks,
                'success_rate': completed_tasks / total_tasks * 100 if total_tasks > 0 else 0
            }
            
        except Exception as e:
            self.logger.error(f"获取发帖统计失败: {e}")
            return {}
        finally:
            session.close()
    
    def cancel_pending_tasks(self, group_id: int = None) -> int:
        """
        取消待处理任务
        
        Args:
            group_id: 分组ID
            
        Returns:
            取消的任务数量
        """
        session = get_db_session()
        try:
            query = session.query(PostingTask).filter(
                PostingTask.status == TASK_STATUS['PENDING']
            )
            
            if group_id:
                query = query.join(Account).filter(Account.group_id == group_id)
            
            tasks = query.all()
            
            for task in tasks:
                task.status = TASK_STATUS['CANCELLED']
            
            session.commit()
            
            self.logger.info(f"取消待处理任务: {len(tasks)}个")
            return len(tasks)
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"取消待处理任务失败: {e}")
            return 0
        finally:
            session.close()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
互动工作流模块
"""

import asyncio
import random
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

from src.database.connection import get_db_session
from src.database.models import Account, InteractionTask, PostingTask
from src.modules.interaction.executor import InteractionExecutor
from src.core.account_manager import AccountManager
from src.utils.logger import LoggerMixin
from src.config.constants import TASK_STATUS, ACCOUNT_STATUS, INTERACTION_TYPES
from src.config.settings import get_settings


class InteractionWorkflow(LoggerMixin):
    """互动工作流"""
    
    def __init__(self):
        self.settings = get_settings()
        self.interaction_executor = InteractionExecutor()
        self.account_manager = AccountManager()
    
    async def execute_post_interactions(self, post_url: str, 
                                      interaction_group_id: int,
                                      interaction_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        对指定帖子执行互动
        
        Args:
            post_url: 帖子URL
            interaction_group_id: 互动分组ID
            interaction_config: 互动配置
            
        Returns:
            执行结果统计
        """
        try:
            self.logger.info(f"开始帖子互动: {post_url}")

            # 发送任务开始信号
            task_config = {
                'interaction_group_id': interaction_group_id,
                'post_url': post_url,
                'interaction_config': interaction_config
            }
            self.log_task_start("interaction", task_config)

            # 获取互动账号
            accounts, total_count = self.account_manager.get_accounts(
                group_id=interaction_group_id,
                status=ACCOUNT_STATUS['LOGGED_IN']
            )
            
            if not accounts:
                self.logger.warning("没有找到可用的互动账号")
                return {
                    'success_count': 0,
                    'failed_count': 0,
                    'total_count': 0,
                    'errors': ['没有可用的互动账号']
                }
            
            # 执行批量互动
            result = await self.interaction_executor.batch_interact(
                accounts, post_url, interaction_config
            )
            
            # 更新任务状态
            self._update_interaction_tasks_status(post_url, result['details'])

            # 发送任务结束信号
            self.log_task_end("interaction", result)

            return result
            
        except Exception as e:
            self.logger.error(f"帖子互动失败: {e}")

            # 发送任务失败信号
            error_result = {
                'success_count': 0,
                'failed_count': 0,
                'total_count': 0,
                'error': str(e)
            }
            self.log_task_end("interaction", error_result)

            return error_result
    
    async def execute_auto_interactions(self, interaction_group_id: int,
                                      interaction_config: Dict[str, Any],
                                      time_window_hours: int = 24) -> Dict[str, Any]:
        """
        自动对最近发布的帖子执行互动
        
        Args:
            interaction_group_id: 互动分组ID
            interaction_config: 互动配置
            time_window_hours: 时间窗口（小时）
            
        Returns:
            执行结果统计
        """
        try:
            self.logger.info(f"开始自动互动: 时间窗口{time_window_hours}小时")
            
            # 获取最近发布的帖子
            recent_posts = self._get_recent_posts(time_window_hours)
            
            if not recent_posts:
                self.logger.info("没有找到最近发布的帖子")
                return {
                    'success_count': 0,
                    'failed_count': 0,
                    'total_count': 0,
                    'processed_posts': 0
                }
            
            # 对每个帖子执行互动
            total_results = {
                'success_count': 0,
                'failed_count': 0,
                'total_count': 0,
                'processed_posts': 0,
                'post_results': []
            }
            
            for post in recent_posts:
                if not post.post_url:
                    continue
                
                try:
                    # 检查是否已经互动过
                    if self._has_interacted_with_post(post.post_url):
                        self.logger.debug(f"帖子已互动过: {post.post_url}")
                        continue
                    
                    # 执行互动
                    result = await self.execute_post_interactions(
                        post.post_url, interaction_group_id, interaction_config
                    )
                    
                    # 累计结果
                    total_results['success_count'] += result['success_count']
                    total_results['failed_count'] += result['failed_count']
                    total_results['total_count'] += result['total_count']
                    total_results['processed_posts'] += 1
                    
                    total_results['post_results'].append({
                        'post_url': post.post_url,
                        'result': result
                    })
                    
                    # 帖子间延迟
                    delay = random.uniform(10, 30)
                    await asyncio.sleep(delay)
                    
                except Exception as e:
                    self.logger.error(f"处理帖子互动失败: {post.post_url}, {e}")
                    total_results['failed_count'] += 1
                    continue
            
            self.logger.info(f"自动互动完成: 处理{total_results['processed_posts']}个帖子")
            return total_results
            
        except Exception as e:
            self.logger.error(f"自动互动失败: {e}")
            return {
                'success_count': 0,
                'failed_count': 0,
                'total_count': 0,
                'processed_posts': 0,
                'error': str(e)
            }
    
    def _get_recent_posts(self, time_window_hours: int) -> List[PostingTask]:
        """
        获取最近发布的帖子
        
        Args:
            time_window_hours: 时间窗口（小时）
            
        Returns:
            帖子列表
        """
        session = get_db_session()
        try:
            cutoff_time = datetime.now() - timedelta(hours=time_window_hours)
            
            posts = session.query(PostingTask).filter(
                PostingTask.status == TASK_STATUS['COMPLETED'],
                PostingTask.completed_time >= cutoff_time,
                PostingTask.post_url.isnot(None)
            ).order_by(PostingTask.completed_time.desc()).all()
            
            return posts
            
        except Exception as e:
            self.logger.error(f"获取最近帖子失败: {e}")
            return []
        finally:
            session.close()
    
    def _has_interacted_with_post(self, post_url: str) -> bool:
        """
        检查是否已经与帖子互动过
        
        Args:
            post_url: 帖子URL
            
        Returns:
            是否已互动
        """
        session = get_db_session()
        try:
            interaction_count = session.query(InteractionTask).filter(
                InteractionTask.post_url == post_url,
                InteractionTask.status == TASK_STATUS['COMPLETED']
            ).count()
            
            return interaction_count > 0
            
        except Exception as e:
            self.logger.error(f"检查互动状态失败: {e}")
            return False
        finally:
            session.close()
    
    def _update_interaction_tasks_status(self, post_url: str, results: List[Dict[str, Any]]):
        """
        更新互动任务状态
        
        Args:
            post_url: 帖子URL
            results: 执行结果列表
        """
        session = get_db_session()
        try:
            for result in results:
                # 查找对应的任务
                task = session.query(InteractionTask).filter(
                    InteractionTask.post_url == post_url,
                    InteractionTask.account_id == result['account_id'],
                    InteractionTask.action_type == result['action_type'],
                    InteractionTask.status == TASK_STATUS['PENDING']
                ).first()
                
                if task:
                    if result['success']:
                        task.status = TASK_STATUS['COMPLETED']
                        task.completed_time = datetime.now()
                    else:
                        task.status = TASK_STATUS['FAILED']
            
            session.commit()
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"更新互动任务状态失败: {e}")
        finally:
            session.close()
    
    def get_interaction_statistics(self, group_id: int = None,
                                 start_date: datetime = None,
                                 end_date: datetime = None) -> Dict[str, Any]:
        """
        获取互动统计
        
        Args:
            group_id: 分组ID
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            统计信息
        """
        session = get_db_session()
        try:
            query = session.query(InteractionTask)
            
            # 添加过滤条件
            if group_id:
                query = query.join(Account).filter(Account.group_id == group_id)
            
            if start_date:
                query = query.filter(InteractionTask.created_at >= start_date)
            
            if end_date:
                query = query.filter(InteractionTask.created_at <= end_date)
            
            # 统计各类型互动数量
            total_interactions = query.count()
            
            like_count = query.filter(
                InteractionTask.action_type == INTERACTION_TYPES['LIKE'],
                InteractionTask.status == TASK_STATUS['COMPLETED']
            ).count()
            
            retweet_count = query.filter(
                InteractionTask.action_type == INTERACTION_TYPES['RETWEET'],
                InteractionTask.status == TASK_STATUS['COMPLETED']
            ).count()
            
            comment_count = query.filter(
                InteractionTask.action_type == INTERACTION_TYPES['COMMENT'],
                InteractionTask.status == TASK_STATUS['COMPLETED']
            ).count()
            
            completed_interactions = query.filter(
                InteractionTask.status == TASK_STATUS['COMPLETED']
            ).count()
            
            failed_interactions = query.filter(
                InteractionTask.status == TASK_STATUS['FAILED']
            ).count()
            
            return {
                'total_interactions': total_interactions,
                'completed_interactions': completed_interactions,
                'failed_interactions': failed_interactions,
                'like_count': like_count,
                'retweet_count': retweet_count,
                'comment_count': comment_count,
                'success_rate': completed_interactions / total_interactions * 100 if total_interactions > 0 else 0
            }
            
        except Exception as e:
            self.logger.error(f"获取互动统计失败: {e}")
            return {}
        finally:
            session.close()
    
    def cleanup_old_interaction_tasks(self, days: int = 30) -> int:
        """
        清理旧的互动任务
        
        Args:
            days: 保留天数
            
        Returns:
            清理的任务数量
        """
        session = get_db_session()
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            old_tasks = session.query(InteractionTask).filter(
                InteractionTask.created_at < cutoff_date
            ).all()
            
            for task in old_tasks:
                session.delete(task)
            
            session.commit()
            
            self.logger.info(f"清理旧互动任务: {len(old_tasks)}个")
            return len(old_tasks)
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"清理旧互动任务失败: {e}")
            return 0
        finally:
            session.close()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
发帖管理组件 - 简化版
移除了复杂的模板池、变量管理和内容编辑器功能
回归简单直观的发帖界面
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTextEdit, QPushButton, 
    QComboBox, QLabel, QGroupBox, QCheckBox, QSpinBox, QMessageBox,
    QDialog, QListWidget, QListWidgetItem
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont

from src.core.workflow_coordinator import get_workflow_coordinator
from src.core.account_manager import AccountManager
from src.utils.logger import LoggerMixin
from src.modules.posting.template_manager import TemplateManager


class PostingThread(QThread):
    """发帖执行线程"""
    
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    posting_completed = pyqtSignal(dict)
    
    def __init__(self, posting_group_id, content_config, execution_mode,
                 interaction_group_id=None, interaction_config=None):
        super().__init__()
        self.posting_group_id = posting_group_id
        self.content_config = content_config
        self.execution_mode = execution_mode
        self.interaction_group_id = interaction_group_id
        self.interaction_config = interaction_config
        self.workflow_coordinator = get_workflow_coordinator()
        self._stop_requested = False
    
    def run(self):
        """执行发帖"""
        try:
            if self._stop_requested:
                self.status_updated.emit("发帖已取消")
                self.posting_completed.emit({'success_count': 0, 'failed_count': 0, 'error': '用户取消'})
                return
                
            self.status_updated.emit("正在执行发帖...")
            self.progress_updated.emit(0)
            
            # 执行发帖工作流
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                if self._stop_requested:
                    self.status_updated.emit("发帖已取消")
                    self.posting_completed.emit({'success_count': 0, 'failed_count': 0, 'error': '用户取消'})
                    return
                    
                # 🎯 根据是否启用联动选择不同的工作流
                if self.interaction_group_id and self.interaction_config:
                    # 使用发帖-互动联动工作流
                    from src.modules.posting.posting_interaction_workflow import PostingInteractionWorkflow
                    posting_interaction_workflow = PostingInteractionWorkflow()

                    result = loop.run_until_complete(
                        posting_interaction_workflow.execute_posting_with_auto_interaction(
                            self.posting_group_id,
                            self.interaction_group_id,
                            self.content_config,
                            self.interaction_config
                        )
                    )
                else:
                    # 使用普通发帖工作流
                    result = loop.run_until_complete(
                        self.workflow_coordinator.posting_workflow.execute_batch_posting(
                            self.posting_group_id, self.content_config, self.execution_mode
                        )
                    )
                
                if self._stop_requested:
                    self.status_updated.emit("发帖已取消")
                    self.posting_completed.emit({'success_count': 0, 'failed_count': 0, 'error': '用户取消'})
                else:
                    self.progress_updated.emit(100)
                    self.posting_completed.emit(result)
            finally:
                loop.close()
                
        except Exception as e:
            if self._stop_requested:
                self.status_updated.emit("发帖已取消")
                self.posting_completed.emit({'success_count': 0, 'failed_count': 0, 'error': '用户取消'})
            else:
                self.status_updated.emit(f"发帖失败: {e}")
                self.posting_completed.emit({'success_count': 0, 'failed_count': 0, 'error': str(e)})
    
    def stop_posting(self):
        """停止发帖"""
        self._stop_requested = True
        self.status_updated.emit("正在停止发帖...")
        if self.isRunning():
            self.terminate()
            self.wait(3000)


class ContentHistoryDialog(QDialog):
    """内容历史对话框"""
    
    def __init__(self, parent=None, history_list=None):
        super().__init__(parent)
        self.history_list = history_list or []
        self.selected_content = ""
        self.init_ui()
    
    def init_ui(self):
        self.setWindowTitle("内容历史")
        self.setModal(True)
        self.resize(500, 400)
        
        layout = QVBoxLayout(self)
        
        layout.addWidget(QLabel("选择历史内容:"))
        
        self.history_widget = QListWidget()
        for content in self.history_list:
            item = QListWidgetItem(content[:100] + "..." if len(content) > 100 else content)
            item.setData(Qt.ItemDataRole.UserRole, content)
            self.history_widget.addItem(item)
        
        self.history_widget.itemDoubleClicked.connect(self.on_item_selected)
        layout.addWidget(self.history_widget)
        
        # 按钮
        btn_layout = QHBoxLayout()
        
        use_btn = QPushButton("使用")
        use_btn.clicked.connect(self.on_item_selected)
        btn_layout.addWidget(use_btn)
        
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        btn_layout.addWidget(cancel_btn)
        
        layout.addLayout(btn_layout)
    
    def on_item_selected(self):
        current_item = self.history_widget.currentItem()
        if current_item:
            self.selected_content = current_item.data(Qt.ItemDataRole.UserRole)
            self.accept()


class PostingWidget(QWidget, LoggerMixin):
    """发帖管理组件 - 简化版"""
    
    status_updated = pyqtSignal(str)
    progress_updated = pyqtSignal(int)
    
    def __init__(self):
        super().__init__()
        self.account_manager = AccountManager()
        self.workflow_coordinator = get_workflow_coordinator()
        self.template_manager = TemplateManager()
        

        
        self.init_ui()
        self.load_groups()
        self.update_media_stats()
        self.update_template_stats()
        
        # 🔧 修复：连接信号，当配置变化时自动更新按钮状态
        self.posting_group_combo.currentTextChanged.connect(self.update_start_button_state)
        self.interaction_group_combo.currentTextChanged.connect(self.update_start_button_state)
        self.auto_like_count_spin.valueChanged.connect(self.update_start_button_state)
        self.auto_retweet_count_spin.valueChanged.connect(self.update_start_button_state)
        self.auto_comment_count_spin.valueChanged.connect(self.update_start_button_state)
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        
        # 文案库配置区域
        template_group = QGroupBox("文案库配置")
        template_layout = QVBoxLayout(template_group)
        
        # 文案库管理和状态
        template_status_layout = QHBoxLayout()
        
        self.template_manage_btn = QPushButton("管理文案库")
        self.template_manage_btn.clicked.connect(self.open_template_management)
        template_status_layout.addWidget(self.template_manage_btn)
        
        self.template_stats_label = QLabel("文案库: 0 个文案")
        template_status_layout.addWidget(self.template_stats_label)
        
        template_status_layout.addStretch()
        template_layout.addLayout(template_status_layout)
        
        # 文案选择策略配置
        strategy_layout = QHBoxLayout()
        
        strategy_layout.addWidget(QLabel("选择策略:"))
        self.template_strategy_combo = QComboBox()
        self.template_strategy_combo.addItems(["随机选择", "顺序选择"])
        strategy_layout.addWidget(self.template_strategy_combo)
        
        strategy_layout.addStretch()
        template_layout.addLayout(strategy_layout)
        
        layout.addWidget(template_group)
        
        # 媒体配置
        media_group = QGroupBox("媒体配置")
        media_layout = QVBoxLayout(media_group)
        
        # 媒体状态和管理
        media_status_layout = QHBoxLayout()
        
        self.media_manage_btn = QPushButton("媒体管理")
        self.media_manage_btn.clicked.connect(self.open_media_management)
        media_status_layout.addWidget(self.media_manage_btn)
        
        self.media_stats_label = QLabel("媒体库: 0 个文件")
        media_status_layout.addWidget(self.media_stats_label)
        
        media_status_layout.addStretch()
        media_layout.addLayout(media_status_layout)
        
        # 媒体选项
        media_options_layout = QHBoxLayout()
        
        self.use_image_cb = QCheckBox("使用图片")
        self.use_image_cb.toggled.connect(self.on_image_toggle)
        media_options_layout.addWidget(self.use_image_cb)
        
        media_options_layout.addWidget(QLabel("数量:"))
        self.image_count_spin = QSpinBox()
        self.image_count_spin.setMinimum(1)
        self.image_count_spin.setMaximum(4)
        self.image_count_spin.setValue(1)
        self.image_count_spin.setEnabled(False)
        media_options_layout.addWidget(self.image_count_spin)
        
        self.use_video_cb = QCheckBox("使用视频")
        self.use_video_cb.toggled.connect(self.on_video_toggle)
        media_options_layout.addWidget(self.use_video_cb)
        
        media_options_layout.addWidget(QLabel("数量:"))
        self.video_count_spin = QSpinBox()
        self.video_count_spin.setMinimum(1)
        self.video_count_spin.setMaximum(1)
        self.video_count_spin.setValue(1)
        self.video_count_spin.setEnabled(False)
        media_options_layout.addWidget(self.video_count_spin)
        
        media_options_layout.addStretch()
        media_layout.addLayout(media_options_layout)
        
        layout.addWidget(media_group)
        
        # 发帖配置
        posting_group = QGroupBox("发帖配置")
        posting_layout = QVBoxLayout(posting_group)
        
        # 分组选择
        group_layout = QHBoxLayout()
        
        group_layout.addWidget(QLabel("发帖分组:"))
        self.posting_group_combo = QComboBox()
        group_layout.addWidget(self.posting_group_combo)
        
        group_layout.addWidget(QLabel("已发分组:"))
        self.posted_group_combo = QComboBox()
        group_layout.addWidget(self.posted_group_combo)

        group_layout.addStretch()
        posting_layout.addLayout(group_layout)

        # 🎯 新功能：发帖-互动联动配置
        interaction_layout = QHBoxLayout()

        # 启用联动功能
        self.enable_auto_interaction_cb = QCheckBox("启用发帖后自动互动")
        self.enable_auto_interaction_cb.stateChanged.connect(self.on_auto_interaction_toggled)
        interaction_layout.addWidget(self.enable_auto_interaction_cb)

        # 互动分组选择
        interaction_layout.addWidget(QLabel("互动分组:"))
        self.interaction_group_combo = QComboBox()
        self.interaction_group_combo.setEnabled(False)
        interaction_layout.addWidget(self.interaction_group_combo)

        interaction_layout.addStretch()
        posting_layout.addLayout(interaction_layout)

        # 互动数量配置
        interaction_count_layout = QHBoxLayout()

        interaction_count_layout.addWidget(QLabel("点赞数:"))
        self.auto_like_count_spin = QSpinBox()
        self.auto_like_count_spin.setRange(0, 50)
        self.auto_like_count_spin.setValue(9)
        self.auto_like_count_spin.setEnabled(False)
        interaction_count_layout.addWidget(self.auto_like_count_spin)

        interaction_count_layout.addWidget(QLabel("转发数:"))
        self.auto_retweet_count_spin = QSpinBox()
        self.auto_retweet_count_spin.setRange(0, 50)
        self.auto_retweet_count_spin.setValue(9)
        self.auto_retweet_count_spin.setEnabled(False)
        interaction_count_layout.addWidget(self.auto_retweet_count_spin)

        interaction_count_layout.addWidget(QLabel("评论数:"))
        self.auto_comment_count_spin = QSpinBox()
        self.auto_comment_count_spin.setRange(0, 50)
        self.auto_comment_count_spin.setValue(9)
        self.auto_comment_count_spin.setEnabled(False)
        interaction_count_layout.addWidget(self.auto_comment_count_spin)

        interaction_count_layout.addStretch()
        posting_layout.addLayout(interaction_count_layout)

        # 互动执行模式配置
        interaction_mode_layout = QHBoxLayout()

        interaction_mode_layout.addWidget(QLabel("互动模式:"))
        self.auto_interaction_mode_combo = QComboBox()
        self.auto_interaction_mode_combo.addItem("并发执行", "concurrent")
        self.auto_interaction_mode_combo.addItem("串行执行", "sequential")
        self.auto_interaction_mode_combo.setEnabled(False)
        interaction_mode_layout.addWidget(self.auto_interaction_mode_combo)

        interaction_mode_layout.addWidget(QLabel("最大并发:"))
        self.auto_max_concurrent_spin = QSpinBox()
        self.auto_max_concurrent_spin.setRange(1, 20)
        self.auto_max_concurrent_spin.setValue(5)
        self.auto_max_concurrent_spin.setEnabled(False)
        interaction_mode_layout.addWidget(self.auto_max_concurrent_spin)

        interaction_mode_layout.addStretch()
        posting_layout.addLayout(interaction_mode_layout)
        
        # 执行模式
        mode_layout = QHBoxLayout()

        mode_layout.addWidget(QLabel("执行模式:"))
        self.execution_mode_combo = QComboBox()
        self.execution_mode_combo.addItems(["顺序执行", "随机执行"])
        mode_layout.addWidget(self.execution_mode_combo)
        
        mode_layout.addStretch()
        posting_layout.addLayout(mode_layout)
        
        layout.addWidget(posting_group)
        
        # 控制按钮
        control_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("开始发帖")
        self.start_btn.clicked.connect(self.start_posting)
        control_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("停止发帖")
        self.stop_btn.clicked.connect(self.stop_posting)
        self.stop_btn.setEnabled(False)
        control_layout.addWidget(self.stop_btn)
        
        self.preview_btn = QPushButton("预览内容")
        self.preview_btn.clicked.connect(self.preview_content)
        control_layout.addWidget(self.preview_btn)
        
        self.refresh_btn = QPushButton("刷新数据")
        self.refresh_btn.clicked.connect(self.refresh_data)
        control_layout.addWidget(self.refresh_btn)
        
        control_layout.addStretch()
        layout.addLayout(control_layout)
    

    
    def load_groups(self):
        """加载分组数据"""
        try:
            # 清空现有项目
            self.posting_group_combo.clear()
            self.posted_group_combo.clear()
            
            # 获取账户分组
            groups = self.account_manager.group_manager.get_all_groups()
            
            for group in groups:
                group_id = group.id
                group_name = group.name
                display_text = f"{group_name} (ID: {group_id})"

                self.posting_group_combo.addItem(display_text, group_id)
                self.posted_group_combo.addItem(display_text, group_id)
                self.interaction_group_combo.addItem(display_text, group_id)
            
            self.logger.info(f"加载了 {len(groups)} 个分组")
            
            # 🔧 修复：分组数据更新后，更新按钮状态
            self.update_start_button_state()
            
        except Exception as e:
            self.logger.error(f"加载分组失败: {e}")
            QMessageBox.warning(self, "错误", f"加载分组失败: {e}")

    def on_auto_interaction_toggled(self, checked):
        """自动互动功能切换"""
        self.interaction_group_combo.setEnabled(checked)
        self.auto_like_count_spin.setEnabled(checked)
        self.auto_retweet_count_spin.setEnabled(checked)
        self.auto_comment_count_spin.setEnabled(checked)
        self.auto_interaction_mode_combo.setEnabled(checked)
        self.auto_max_concurrent_spin.setEnabled(checked)

        if checked:
            self.logger.info("✅ 启用发帖后自动互动功能")
        else:
            self.logger.info("❌ 禁用发帖后自动互动功能")
        
        # 🔧 修复：自动互动配置变化后，更新按钮状态
        self.update_start_button_state()
    
    def update_media_stats(self):
        """更新媒体统计信息"""
        try:
            from src.modules.posting.content_manager import MediaManager
            media_manager = MediaManager()
            stats = media_manager.get_media_stats()
            total_count = stats.get('images', 0) + stats.get('videos', 0)
            self.media_stats_label.setText(
                f"媒体库: {total_count} 个文件 (图片: {stats.get('images', 0)}, 视频: {stats.get('videos', 0)})"
            )
        except Exception as e:
            self.logger.error(f"更新媒体统计失败: {e}")
    
    def on_image_toggle(self, checked):
        """图片使用切换"""
        self.image_count_spin.setEnabled(checked)
    
    def on_video_toggle(self, checked):
        """视频使用切换"""
        self.video_count_spin.setEnabled(checked)
    
    def open_media_management(self):
        """打开媒体管理对话框"""
        try:
            from src.ui.dialogs.media_management_dialog import MediaManagementDialog
            dialog = MediaManagementDialog(self)
            dialog.media_updated.connect(self.update_media_stats)
            dialog.exec()
        except Exception as e:
            self.logger.error(f"打开媒体管理失败: {e}")
            QMessageBox.critical(self, "错误", f"打开媒体管理失败: {e}")
    
    def validate_config(self, show_warnings: bool = True) -> bool:
        """验证配置
        
        Args:
            show_warnings: 是否显示警告对话框，默认True
        """
        # 检查文案库
        stats = self.template_manager.get_template_stats()
        if stats.get('total_count', 0) == 0:
            if show_warnings:
                QMessageBox.warning(self, "配置错误", "文案库为空，请先添加文案")
            return False
        
        if not self.posting_group_combo.currentData():
            if show_warnings:
                QMessageBox.warning(self, "配置错误", "请选择发帖分组")
            return False

        # 🎯 验证联动功能配置
        if self.enable_auto_interaction_cb.isChecked():
            if not self.interaction_group_combo.currentData():
                if show_warnings:
                    QMessageBox.warning(self, "配置错误", "启用自动互动时，请选择互动分组")
                return False

            # 检查互动数量配置
            total_interactions = (self.auto_like_count_spin.value() +
                                self.auto_retweet_count_spin.value() +
                                self.auto_comment_count_spin.value())
            if total_interactions == 0:
                if show_warnings:
                    QMessageBox.warning(self, "配置错误", "启用自动互动时，至少设置一种互动类型的数量大于0")
                return False

        return True
    
    def update_start_button_state(self):
        """实时更新开始发帖按钮状态"""
        try:
            # 如果正在发帖，按钮应该禁用
            if hasattr(self, 'posting_thread') and self.posting_thread.isRunning():
                self.start_btn.setEnabled(False)
                return
            
            # 根据配置验证结果更新按钮状态
            is_valid = self.validate_config(show_warnings=False)
            self.start_btn.setEnabled(is_valid)
            
        except Exception as e:
            self.logger.error(f"更新开始按钮状态失败: {e}")
            # 出错时禁用按钮以防意外操作
            self.start_btn.setEnabled(False)
    
    def start_posting(self):
        """开始发帖"""
        try:
            # 如果有正在运行的线程，先停止它
            if hasattr(self, 'posting_thread') and self.posting_thread.isRunning():
                self.logger.warning("检测到正在运行的发帖任务，先停止它")
                self.stop_posting()
                # 等待线程结束
                if not self.posting_thread.wait(3000):
                    self.posting_thread.terminate()
                    self.posting_thread.wait(1000)

            # 🔧 修复：重置浏览器池状态，确保可以重新启动
            self._reset_browser_pool_for_restart()

            if not self.validate_config():
                return

            # 准备配置
            strategy_text = self.template_strategy_combo.currentText()
            template_strategy = 'random' if strategy_text == '随机选择' else 'sequential'
            
            template_config = {
                'template_strategy': template_strategy,
                'template_category': None
            }
            
            media_config = {
                'use_images': self.use_image_cb.isChecked(),
                'image_count': self.image_count_spin.value() if self.use_image_cb.isChecked() else 0,
                'use_videos': self.use_video_cb.isChecked(),
                'video_count': self.video_count_spin.value() if self.use_video_cb.isChecked() else 0
            }
            
            content_config = {
                'template_config': template_config,
                'media_config': media_config
            }

            # 🔧 修复：添加已发帖分组ID到配置中
            posted_group_id = self.posted_group_combo.currentData()
            if posted_group_id:
                content_config['posted_group_id'] = posted_group_id
                self.logger.info(f"设置已发帖分组ID: {posted_group_id}")

            posting_group_id = self.posting_group_combo.currentData()
            execution_mode = "sequential" if self.execution_mode_combo.currentText() == "顺序执行" else "random"

            # 🎯 检查是否启用自动互动
            enable_auto_interaction = self.enable_auto_interaction_cb.isChecked()
            interaction_group_id = self.interaction_group_combo.currentData() if enable_auto_interaction else None

            interaction_config = None
            if enable_auto_interaction:
                interaction_config = {
                    'like_count': self.auto_like_count_spin.value(),
                    'retweet_count': self.auto_retweet_count_spin.value(),
                    'comment_count': self.auto_comment_count_spin.value(),
                    'execution_mode': self.auto_interaction_mode_combo.currentData(),
                    'max_concurrent': self.auto_max_concurrent_spin.value()
                }
                mode_text = "并发" if interaction_config['execution_mode'] == 'concurrent' else "串行"
                self.logger.info(f"🎯 启用自动互动: 点赞{interaction_config['like_count']}, 转发{interaction_config['retweet_count']}, 评论{interaction_config['comment_count']}")
                self.logger.info(f"🚀 互动模式: {mode_text}执行 (最大并发: {interaction_config['max_concurrent']})")

            # 创建并启动发帖线程（支持联动）
            self.posting_thread = PostingThread(
                posting_group_id, content_config, execution_mode,
                interaction_group_id, interaction_config
            )
            self.posting_thread.progress_updated.connect(self.progress_updated)
            self.posting_thread.status_updated.connect(self.status_updated)
            self.posting_thread.posting_completed.connect(self.on_posting_completed)
            self.posting_thread.finished.connect(self.on_posting_finished)
            
            self.posting_thread.start()
            
            # 更新UI状态
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            
        except Exception as e:
            self.logger.error(f"启动发帖失败: {e}")
            QMessageBox.critical(self, "错误", f"启动发帖失败: {e}")
    
    def stop_posting(self):
        """停止发帖"""
        try:
            if hasattr(self, 'posting_thread') and self.posting_thread.isRunning():
                self.posting_thread.stop_posting()

                # 停止工作流
                if hasattr(self.posting_thread, 'workflow_coordinator'):
                    self.posting_thread.workflow_coordinator.posting_workflow.stop_workflow()

                self.status_updated.emit("正在停止发帖...")
                self.stop_btn.setEnabled(False)

                # 🔧 修复：立即清理浏览器资源，不等待线程结束
                self._reset_browser_pool()

                # 等待线程结束
                if self.posting_thread.wait(5000):
                    self.status_updated.emit("发帖已停止")
                else:
                    self.status_updated.emit("强制停止发帖")
                    # 强制终止线程
                    self.posting_thread.terminate()
                    self.posting_thread.wait(1000)
                    # 再次清理确保彻底
                    self._reset_browser_pool()
            else:
                self.status_updated.emit("没有正在运行的发帖任务")
                # 即使没有运行任务，也清理一下浏览器状态
                self._reset_browser_pool()

        except Exception as e:
            self.logger.error(f"停止发帖失败: {e}")
            self.status_updated.emit(f"停止发帖失败: {e}")
            # 出错时也要清理浏览器资源
            try:
                self._reset_browser_pool()
            except Exception as cleanup_error:
                self.logger.error(f"清理浏览器资源失败: {cleanup_error}")
    
    def _reset_browser_pool(self):
        """重置浏览器池状态 - 停止任务时彻底清理Chrome进程"""
        try:
            from src.core.browser_manager import get_browser_pool
            browser_pool = get_browser_pool()

            # 🔧 修复：停止任务时不使用最终关闭标志，保持可以重新启动
            browser_pool.sync_close_all(is_final_shutdown=False)
            self.logger.info("浏览器池已彻底清理")
        except Exception as e:
            self.logger.error(f"重置浏览器池失败: {e}")

    def _reset_browser_pool_for_restart(self):
        """重置浏览器池以便重新启动 - 用于开始新任务时"""
        try:
            from src.core.browser_manager import get_browser_pool
            browser_pool = get_browser_pool()

            # 🔧 修复：重置浏览器池状态，确保可以重新启动
            browser_pool.reset_for_restart()
            self.logger.info("浏览器池已重置，可以重新启动")
        except Exception as e:
            self.logger.error(f"重置浏览器池失败: {e}")
    
    def on_posting_completed(self, result: dict):
        """发帖完成回调"""
        try:
            # 🎯 支持联动结果显示
            if 'posting_success_count' in result:
                # 联动模式结果
                posting_success = result.get('posting_success_count', 0)
                posting_failed = result.get('posting_failed_count', 0)
                total_interactions = result.get('total_interactions', 0)
                successful_interactions = result.get('successful_interactions', 0)
                interaction_rate = result.get('interaction_success_rate', 0)

                if posting_success > 0:
                    message = f"发帖-互动联动完成!\n\n"
                    message += f"📝 发帖结果: 成功 {posting_success} 个, 失败 {posting_failed} 个\n"
                    message += f"🎯 互动结果: 成功 {successful_interactions}/{total_interactions} 个 ({interaction_rate:.1f}%)"

                    QMessageBox.information(self, "联动完成", message)
                else:
                    error_msg = result.get('error', '未知错误')
                    QMessageBox.warning(self, "联动失败", f"发帖-互动联动失败: {error_msg}")
            else:
                # 普通发帖模式结果
                success_count = result.get('success_count', 0)
                failed_count = result.get('failed_count', 0)

                if success_count > 0:
                    QMessageBox.information(
                        self, "发帖完成",
                        f"发帖完成: 成功 {success_count} 个, 失败 {failed_count} 个"
                    )
                else:
                    error_msg = result.get('error', '未知错误')
                    QMessageBox.warning(self, "发帖失败", f"发帖失败: {error_msg}")
            
        except Exception as e:
            self.logger.error(f"处理发帖结果失败: {e}")
    
    def on_posting_finished(self):
        """发帖线程结束"""
        # 🔧 修复：发帖结束后，根据配置状态更新按钮，而不是直接启用
        self.update_start_button_state()
        self.stop_btn.setEnabled(False)
        self.progress_updated.emit(-1)  # 隐藏进度条
        self._reset_browser_pool()
    
    def preview_content(self):
        """预览内容"""
        try:
            # 检查文案库
            stats = self.template_manager.get_template_stats()
            if stats.get('total_count', 0) == 0:
                QMessageBox.warning(self, "警告", "文案库为空，请先添加文案")
                return
            
            # 获取文案库配置
            strategy_text = self.template_strategy_combo.currentText()
            template_strategy = 'random' if strategy_text == '随机选择' else 'sequential'
            
            template_config = {
                'template_strategy': template_strategy,
                'template_category': None
            }
            
            # 获取媒体配置
            media_config = {
                'use_images': self.use_image_cb.isChecked(),
                'image_count': self.image_count_spin.value() if self.use_image_cb.isChecked() else 0,
                'use_videos': self.use_video_cb.isChecked(),
                'video_count': self.video_count_spin.value() if self.use_video_cb.isChecked() else 0
            }
            
            # 生成预览
            from src.modules.posting.content_manager import ContentManager
            content_manager = ContentManager()
            previews = []
            
            for i in range(3):
                try:
                    content = content_manager.prepare_content(
                        template=None,
                        media_config=media_config,
                        template_config=template_config
                    )
                    preview_text = f"预览 {i+1}:\n{content['text']}"
                    if content.get('media'):
                        media_info = f"\n媒体文件: {len(content['media'])} 个"
                        preview_text += media_info
                    previews.append(preview_text)
                except Exception as e:
                    previews.append(f"预览 {i+1}: 生成失败 - {e}")
            
            # 显示预览对话框
            preview_text = "\n\n" + "="*50 + "\n\n"
            preview_text = preview_text.join(previews)
            
            from PyQt6.QtWidgets import QDialogButtonBox
            dialog = QDialog(self)
            dialog.setWindowTitle("内容预览")
            dialog.setModal(True)
            dialog.resize(600, 400)
            
            layout = QVBoxLayout(dialog)
            
            text_edit = QTextEdit()
            text_edit.setPlainText(preview_text)
            text_edit.setReadOnly(True)
            layout.addWidget(text_edit)
            
            button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok)
            button_box.accepted.connect(dialog.accept)
            layout.addWidget(button_box)
            
            dialog.exec()
            
        except Exception as e:
            self.logger.error(f"预览内容失败: {e}")
            QMessageBox.critical(self, "错误", f"预览内容失败: {e}")
    
    def open_template_management(self):
        """打开文案库管理对话框"""
        try:
            from src.ui.dialogs.template_library_dialog import TemplateLibraryDialog
            dialog = TemplateLibraryDialog(self.template_manager, self)
            dialog.template_updated.connect(self.update_template_stats)
            dialog.exec()
        except Exception as e:
            self.logger.error(f"打开文案库管理失败: {e}")
            QMessageBox.critical(self, "错误", f"打开文案库管理失败: {e}")
    
    def update_template_stats(self):
        """更新文案库统计信息"""
        try:
            stats = self.template_manager.get_template_stats()
            total_count = stats.get('total_count', 0)
            categories = stats.get('categories', [])
            
            self.template_stats_label.setText(f"文案库: {total_count} 个文案")
            
            # 🔧 修复：文案库统计更新后，更新按钮状态
            self.update_start_button_state()
                
        except Exception as e:
            self.logger.error(f"更新文案库统计失败: {e}")
    
    def refresh_data(self):
        """刷新数据"""
        try:
            self.load_groups()
            self.update_media_stats()
            self.update_template_stats()
            self.status_updated.emit("数据已刷新")
        except Exception as e:
            self.logger.error(f"刷新数据失败: {e}")
            QMessageBox.critical(self, "错误", f"刷新数据失败: {e}")
